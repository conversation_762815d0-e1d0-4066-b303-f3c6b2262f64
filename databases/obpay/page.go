package obpay

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
)

type Page struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 标题
	Title string `xorm:"not null varchar(64) 'title'"`
	// 终端类型
	TerminalType int32 `xorm:"not null tinyint(4) 'terminal_type'"`
	// 头图
	HeadImg string `xorm:"not null varchar(256) 'head_img'"`
	// 头图男性
	HeadImgMan string `xorm:"not null varchar(256) 'head_img_man'"`
	// 头视频
	HeadVideo string `xorm:"not null varchar(256) 'head_video'"`
	// 头视频男性
	HeadVideoMan string `xorm:"not null varchar(256) 'head_video_man'"`
	// 课程简介图
	DescImg string `xorm:"not null varchar(256) 'desc_img'"`
	// 返回挽留弹窗商品id
	RetainSkuID int64 `xorm:"not null int(11) 'retain_sku_id'"`
	// 支付挽留弹窗商品id
	PayRetainSkuID int64 `xorm:"not null int(11) 'pay_retain_sku_id'"`
	// 返回挽留弹窗图片
	RetainImg string `xorm:"not null varchar(256) 'retain_img'"`
	// 返回挽留弹窗图片
	RetainImgPad string `xorm:"not null varchar(256) 'retain_img_pad'"`
	// 支付挽留弹窗图片
	PayRetainImg string `xorm:"not null varchar(256) 'pay_retain_img'"`
	// 按钮phone图片
	PayButtonImgPhone string `xorm:"not null varchar(256) 'pay_button_img_phone'"`
	// 按钮pad图片
	PayButtonImgPad string `xorm:"not null varchar(256) 'pay_button_img_pad'"`
	// 是否删除 1 是 2 否
	IsDel int32 `xorm:"not null tinyint(1) 'is_del'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
	// 付费页素材返回挽留-选中
	RetainImgSelected string `xorm:"not null varchar(256) 'retain_img_selected'"`
	// 付费页素材返回挽留-未选中
	RetainImgNoSelected string `xorm:"not null varchar(256) 'retain_img_no_selected'"`
	// 付费页素材支付挽留-选中
	PayRetainImgSelected string `xorm:"not null varchar(256) 'pay_retain_img_selected'"`
	// 付费页素材支付挽留-未选中
	PayRetainNoSelected string `xorm:"not null varchar(256) 'pay_retain_no_selected'"`
	// SKU样式
	SkuStyle int32 `xorm:"not null tinyint(1) 'sku_style'"`
	// 返回挽留弹窗优惠素材
	RetainOfferImg string `xorm:"not null varchar(255) 'retain_offer_image'"`
	// 支付挽留弹窗优惠素材
	PayRetainOfferImg string `xorm:"not null varchar(255) 'pay_retain_offer_image'"`
	// 退出挽留弹窗id
	RetainPopupID int64 `xorm:"not null int(11) 'retain_popup_id'"`
	// 支付挽留弹窗id
	PayRetainPopupID int64 `xorm:"not null int(11) 'pay_retain_popup_id'"`
	// 未支付挽留弹窗id
}

// TableName 获取表名
func (Page) TableName() string {
	return "ob_pay_page"
}

// Save 保存数据
func (a *Page) SaveByTran(session *xorm.Session) error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := session.Insert(a)
	return err
}

// Update 更新数据
func (a *Page) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

// Update 更新数据
func (a *Page) UpdateMustColByTran(session *xorm.Session) error {
	a.UpdateTime = time.Now().Unix()
	_, err := session.ID(a.ID).MustCols("ratio",
		"desc_img", "retain_sku_id", "pay_retain_sku_id", "retain_img", "pay_retain_img").Update(a)
	return err
}

type page struct {
}

var TbPage page

func (p *page) GetItemByID(uid, id int64) *Page {
	var table Page
	ok, err := databases.GetEngine().ID(id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (p *page) GetAll() []*Page {
	var tables []*Page
	err := databases.GetEngine().Where("is_del = ?", library.No).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
