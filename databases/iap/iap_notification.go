package iap

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
)

type Notification struct {
	ID                    int64  `xorm:"not null pk autoincr INT(11) 'id'"`
	OriginalTransactionID string `xorm:"varchar(128) notnull default '' 'original_transaction_id'"` // apple 原始订单号
	TransactionID         string `xorm:"varchar(128) notnull default '' 'transaction_id'"`          // iap回传的订单号
	NotificationType      string `xorm:"not null varchar(64) 'notification_type'"`
	Subtype               string `xorm:"varchar(64) notnull default '' 'subtype'"` // 回调子类型
	NotificationContent   string `xorm:"not null longtext 'notification_content'"`
	ProcessResult         string `xorm:"not null text 'process_result'"`
	NotificationDetail    string `xorm:"longtext 'notification_detail'"`  // 回调内容详情
	DataDecodedPayload    string `xorm:"longtext 'data_decoded_payload'"` // 解析后的回调内容
	IsDeal                int32  `xorm:"not null default '2' tinyint(1) 'is_deal'"`
	CreateTime            int64  `xorm:"not null int(11) 'create_time'"`
	UpdateTime            int64  `xorm:"not null int(11) 'update_time'"`
}

func (*Notification) TableName() string {
	return "ios_iap_notification"
}

// Save 保存数据
func (a *Notification) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *Notification) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

type notification struct{}

var TbNotification notification

// GetNotDealList 获取未处理的ios通知
func (*notification) GetNotDealList(start, end int64) []*Notification {
	var tables []*Notification
	err := databases.GetEngine().Where("is_deal = ? and create_time >= ? and create_time <= ?", library.No, start, end).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetItemByID 获取通知
func (*notification) GetItemByID(id int64) *Notification {
	var table Notification
	ok, err := databases.GetEngine().ID(id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
