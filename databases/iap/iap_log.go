package iap

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
)

type IOSReceiptLog struct {
	ID                    int64  `xorm:"not null pk autoincr INT(11) 'id'"`
	UID                   int64  `xorm:"not null bigint(11) 'uid'"`
	OrderID               string `xorm:"not null varchar(128) 'order_id'"`
	TransactionID         string `xorm:"not null varchar(128) 'transaction_id'"`
	OriginalTransactionID string `xorm:"not null varchar(128) 'original_transaction_id'"`
	Request               string `xorm:"not null longtext 'request'"`
	ReceiptDetail         string `xorm:"not null longtext 'receipt_detail'"`
	ProcessResult         string `xorm:"not null text 'process_result'"`
	IsDeal                int32  `xorm:"not null default '2' tinyint(1) 'is_deal'"`
	CreateTime            int64  `xorm:"not null int(11) 'create_time'"`
	UpdateTime            int64  `xorm:"not null int(11) 'update_time'"`
}

func (*IOSReceiptLog) TableName() string {
	return "ios_iap_request_log"
}

// Save 保存数据
func (a *IOSReceiptLog) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *IOSReceiptLog) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

// UpdateByTran 事务更新数据
func (a *IOSReceiptLog) UpdateByTran(session *xorm.Session) error {
	a.UpdateTime = time.Now().Unix()
	_, err := session.ID(a.ID).Update(a)
	return err
}

type iosreceipt struct{}

var TbIOSReceipt iosreceipt

func (*iosreceipt) GetItemByID(id int64) *IOSReceiptLog {
	var table IOSReceiptLog
	ok, err := databases.GetEngine().ID(id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetNotDealList 获取未处理的ios receipt上报
func (*iosreceipt) GetNotDealList(start, end int64) []*IOSReceiptLog {
	var tables []*IOSReceiptLog
	err := databases.GetEngine().Where("is_deal = ? and create_time >= ? and create_time <= ?", library.No, start, end).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetListByUID 根据UID获取IOS上报数据
func (*iosreceipt) GetListByUID(uid int64) []*IOSReceiptLog {
	var tables []*IOSReceiptLog
	err := databases.GetEngine().Where("uid = ? ", uid).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
