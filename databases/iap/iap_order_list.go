package iap

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
)

type IOSOrder struct {
	ID                   int64  `xorm:"not null pk autoincr INT(11) 'id'"`
	OriginalTranID       string `xorm:"not null varchar(128) 'original_tran_id'"`
	TranID               string `xorm:"not null varchar(128) 'tran_id'"`
	WebItemID            string `xorm:"not null varchar(128) 'web_item_id'"`
	ProductID            string `xorm:"not null varchar(64) 'product_id'"`
	UID                  int64  `xorm:"not null bigint(11) 'uid'"`
	OrderID              string `xorm:"not null varchar(128) 'order_id'"`
	PurchaseTime         int64  `xorm:"not null int(11) 'purchase_time'"`
	ExpireTime           int64  `xorm:"not null int(11) 'expire_time'"`
	CancelTime           int64  `xorm:"not null int(11) 'cancel_time'"`
	Receipt              string `xorm:"not null longtext 'receipt'"`
	CreateTime           int64  `xorm:"not null int(11) 'create_time'"`
	UpdateTime           int64  `xorm:"not null int(11) 'update_time'"`
	IsUpgraded           int    `xorm:"not null tinyint(1) 'is_upgraded'"`
	IsTrialPeriod        int    `xorm:"not null tinyint(1) 'is_trial_period'"`
	IsInIntroOfferPeriod int    `xorm:"not null tinyint(1) 'is_in_intro_offer_period'"`
	RefundStatus         int    `xorm:"not null tinyint(1) 'refund_status'"` // 0:未退款，1：已申请 2已退款 3 已拒绝退款
	AnonymousID          string `xorm:"not null default '' VARCHAR(128) 'anonymous_id'" json:"anonymous_id"`
}

func (*IOSOrder) TableName() string {
	return "ios_iap_order_list"
}

// Save 保存数据
func (a *IOSOrder) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *IOSOrder) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

// SaveByTran 事务保存数据
func (a *IOSOrder) SaveByTran(session *xorm.Session) error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}

// UpdateByTran 事务更新数据
func (a *IOSOrder) UpdateByTran(session *xorm.Session) error {
	a.UpdateTime = time.Now().Unix()
	_, err := session.ID(a.ID).Update(a)
	return err
}

type iosorder struct{}

var TbIOSOrder iosorder

func (*iosorder) GetItemByTran(originalTranID, tranID string) *IOSOrder {
	var table IOSOrder
	ok, err := databases.GetEngine().Where("original_tran_id = ? and tran_id = ?",
		originalTranID, tranID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetLastItemByOriginalTran 根据原始交易id获取最后一条数据
func (*iosorder) GetLastItemByOriginalTran(originalTranID string) *IOSOrder {
	var table IOSOrder
	ok, err := databases.GetEngine().Where("original_tran_id = ?",
		originalTranID).Desc("id").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*iosorder) GetItemByWebItemID(originalTranID, webItemID string) *IOSOrder {
	var table IOSOrder
	ok, err := databases.GetEngine().Where("original_tran_id = ? and web_item_id = ?",
		originalTranID, webItemID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*iosorder) GetListByOriginal(originalTranID string) []*IOSOrder {
	var tables []*IOSOrder
	err := databases.GetEngine().Where("original_tran_id = ?", originalTranID).Desc("id").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (*iosorder) GetListByUID(uid int64) []*IOSOrder {
	var tables []*IOSOrder
	err := databases.GetEngine().Where("uid = ?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (*iosorder) GetItemByOrderID(orderID string) *IOSOrder {
	var table IOSOrder
	ok, err := databases.GetEngine().Where("order_id = ?", orderID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*iosorder) GetNoOfferByOrig(originalTranID string) []*IOSOrder {
	var tables []*IOSOrder
	err := databases.GetEngine().Where("original_tran_id = ? and is_trial_period = 0 "+
		"and is_in_intro_offer_period = 0", originalTranID).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (*iosorder) GetIOSOrderByProductID(uid int64, productIDArr []string) []*IOSOrder {
	var tables []*IOSOrder
	err := databases.GetEngine().Where("uid = ?", uid).In("product_id", productIDArr).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
