package tiktok

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
)

type AttributionLog struct {
	ID                   int64  `xorm:"pk autoincr not null comment('主键ID') int(11) 'id'"`
	ThirdOpenID          string `xorm:"default 'NULL' comment('第三方账号码') varchar(128) 'third_open_id'"`
	ThirdUniqID          string `xorm:"not null default '' comment('第三方账号唯一码') varchar(128) 'third_uniq_id'"`
	Mobile               string `xorm:"not null default '' comment('手机号') varchar(24) 'mobile'"`
	FirstAttributionTime int64  `xorm:"not null default 0 comment('首次激活时间') int(11) 'first_attribution_time'"`
	AttributionTime      int64  `xorm:"not null default 0 comment('激活时间') int(11) 'attribution_time'"`
	CreateTime           int64  `xorm:"not null default 0 comment('创建时间') int(11) 'create_time'"`
	UpdateTime           int64  `xorm:"not null default 0 comment('更新时间') int(11) 'update_time'"`
}

func (a *AttributionLog) TableName() string {
	return "attribution_tiktok_log"
}

// Save 保存数据
func (a *AttributionLog) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *AttributionLog) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

type attributionLog struct {
}

var TbAttributionLog attributionLog

func (*attributionLog) GetItemByMobile(mobile string) *AttributionLog {
	var table AttributionLog
	ok, err := databases.GetEngine().Where("mobile = ?", mobile).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
