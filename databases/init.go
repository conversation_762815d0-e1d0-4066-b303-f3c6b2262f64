package databases

import (
	"fmt"

	"github.com/sirupsen/logrus"
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/gokit/xorm"

	srvconf "gitlab.dailyyoga.com.cn/server/children/config"
)

var (
	DB       *xorm.Connection
	YoGaCsDB *xorm.Connection
)

const (
	MaxOpenConns = 40
	MaxIdleConns = 30
)

func GetEngine() *xorm.Engine {
	return DB.GetEngine(xorm.Slave)
}

func GetEngineMaster() *xorm.Engine {
	return DB.GetEngine(xorm.Master)
}

func GetYoGaCsMaster() *xorm.Engine {
	return YoGaCsDB.GetEngine(xorm.Master)
}

func GetYoGaCs() *xorm.Engine {
	return YoGaCsDB.GetEngine(xorm.Slave)
}

func Starter(ms *microservice.Microservice) {
	DB = xorm.Initial(xorm.WithLogger(logger.GetInstance()), xorm.WithLogLevel(xorm.LogInfo))
	YoGaCsDB = xorm.Initial(xorm.WithLogger(logger.GetInstance()), xorm.WithLogLevel(xorm.LogInfo))
	options := make([]xorm.ConnOption, 0)
	options = append(options, xorm.WithMaxOpen(MaxOpenConns), xorm.WithMaxIdle(MaxIdleConns))
	if err := DB.AddConnect(GetDns(srvconf.Get().DBChildren.Master), xorm.Master, options...); err != nil {
		logrus.Fatalf("connection database failed. type: master, err: %s", err)
		return
	}
	for _, v := range srvconf.Get().DBChildren.Slaves {
		if err := DB.AddConnect(GetDns(v), xorm.Slave, options...); err != nil {
			logrus.Fatalf("connection database failed. type: slave, err: %s", err)
		}
	}
	if err := YoGaCsDB.AddConnect(GetDns(srvconf.Get().DBYoGaCs.Master), xorm.Master, options...); err != nil {
		logrus.Fatalf("connection database failed. type: master, err: %s", err)
		return
	}
	for _, v := range srvconf.Get().DBYoGaCs.Slaves {
		if err := YoGaCsDB.AddConnect(GetDns(v), xorm.Slave, options...); err != nil {
			logrus.Fatalf("connection database failed. type: slave, err: %s", err)
		}
	}
}

// nolint
func GetDns(database srvconf.DatabaseConf) string {
	charset := "utf8mb4"
	return fmt.Sprintf("%s:%s@tcp(%s)/%s?charset=%s&loc=Asia%%2FShanghai",
		database.User, database.Password, database.Address, database.Databases, charset)
}
