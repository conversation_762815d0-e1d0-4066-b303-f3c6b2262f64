package user

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/pay"
)

type WebProductSubscribeUser struct {
	ID          int64 `xorm:"pk autoincr BIGINT(30) 'id'" json:"id"`
	UID         int64 `xorm:"not null BIGINT(30) 'uid'" json:"uid"`
	ProductID   int   `xorm:"not null INT(6) 'product_id'" json:"product_id"`
	PayType     int   `xorm:"not null tinyint(4) 'pay_type'"`
	ExpiresTime int64 `xorm:"int(11) 'expires_time'" json:"expires_time"`
	// 扣款时第一次失败时间 失败则清零
	BeginFailTime   int64  `xorm:"int(11) 'begin_fail_time'" json:"begin_fail_time"`
	OrderID         string `xorm:"not null varchar(50) 'order_id'" json:"order_id"`
	OriginalOrderID string `xorm:"not null varchar(50) 'original_order_id'" json:"original_order_id"`
	// 第三方订单ID
	ThirdPartTransactionID string `xorm:"'third_part_transaction_id'" json:"third_part_transaction_id"`
	// 原始第三方订单ID
	OriginalTransactionID string `xorm:"'third_part_original_transaction_id'" json:"third_part_original_transaction_id"`
	// 优惠类型：1-无 2-首购 3-试用 4-试用+首购
	OfferType int `xorm:"not null default 1 TINYINT(1) 'offer_type'" json:"offer_type"`
	// 签约状态
	Status     pay.ContractChangeType `xorm:"not null default 0 TINYINT(2) 'status'" json:"status"`
	CreateTime int64                  `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64                  `xorm:"not null int(11) 'update_time'"`
}

// TableName Get table name
func (w *WebProductSubscribeUser) TableName() string {
	return "web_product_subscribe_user"
}

// Save 保存数据
func (w *WebProductSubscribeUser) Save() error {
	w.CreateTime = time.Now().Unix()
	w.UpdateTime = w.CreateTime
	_, err := databases.GetEngineMaster().Insert(w)
	return err
}

// Update 更新数据
func (w *WebProductSubscribeUser) Update() error {
	w.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(w.ID).MustCols("begin_fail_time").Update(w)
	return err
}

// SaveByTran 事务保存数据
func (w *WebProductSubscribeUser) SaveByTran(session *xorm.Session) error {
	w.CreateTime = time.Now().Unix()
	w.UpdateTime = w.CreateTime
	_, err := databases.GetEngineMaster().Insert(w)
	return err
}

// UpdateByTran 事务更新数据
func (w *WebProductSubscribeUser) UpdateByTran(session *xorm.Session) error {
	w.UpdateTime = time.Now().Unix()
	_, err := session.ID(w.ID).Update(w)
	return err
}

func (w *WebProductSubscribeUser) FindOrCreate() error {
	var table WebProductSubscribeUser
	ok, err := databases.GetEngine().
		Where("third_part_original_transaction_id=? AND product_id=?",
			w.OriginalTransactionID, w.ProductID).Get(&table)
	if err != nil {
		logger.Error(err)
		return err
	}
	if ok {
		up := &WebProductSubscribeUser{
			ThirdPartTransactionID: w.ThirdPartTransactionID,
			OriginalTransactionID:  w.OriginalTransactionID,
			Status:                 w.Status,
			ExpiresTime:            w.ExpiresTime,
			OfferType:              w.OfferType,
		}
		// 签约状态 过期时间取更大的
		if w.Status == pay.ContractChangeTypeEnum.ContractChangeTypeAdd && table.ExpiresTime > up.ExpiresTime {
			up.ExpiresTime = table.ExpiresTime
		}
		_, err := databases.GetEngineMaster().ID(table.ID).Update(up)
		if err != nil {
			return err
		}
		return nil
	}
	w.CreateTime = time.Now().Unix()
	w.UpdateTime = w.CreateTime
	_, err = databases.GetEngineMaster().Insert(w)
	if err != nil {
		return err
	}
	return nil
}

type wPSU struct{}

var TbWPSubU wPSU

// GetWebSubscribeUser 获取会员订阅信息
func (*wPSU) GetWebSubscribeUser(uid int64) *WebProductSubscribeUser {
	var table WebProductSubscribeUser
	ok, err := databases.GetEngine().Where("uid=? AND status=?", uid, library.Yes).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetValidSubscribeUser 获取用户所有有效的会员订阅信息
func (*wPSU) GetValidSubscribeUser(uid int64) []*WebProductSubscribeUser {
	var tables []*WebProductSubscribeUser
	err := databases.GetEngine().Where("uid=? AND status=?", uid, library.Yes).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetItemByContractID 根据签约id获取订阅信息
func (*wPSU) GetItemByContractID(contractID string) *WebProductSubscribeUser {
	var table WebProductSubscribeUser
	ok, err := databases.GetEngine().Where("third_part_original_transaction_id=?", contractID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetTimeChargeUserList 获取可以扣款的订阅信息
func (*wPSU) GetTimeChargeUserList(startTime, endTime int64, payType int) []*WebProductSubscribeUser {
	tables := make([]*WebProductSubscribeUser, 0)
	err := databases.GetEngine().Where("status = ? and pay_type = ?", library.Yes, payType).
		And("expires_time >= ? and expires_time <= ? ", startTime, endTime).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetUserSubscribeList 获取用户会员订阅列表
func (*wPSU) GetUserSubscribeList(uid int64) []*WebProductSubscribeUser {
	tables := make([]*WebProductSubscribeUser, 0)
	err := databases.GetEngine().Where("uid = ?", uid).Desc("id").
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetItemByThirdOriginalTranID 根据签约code获取订阅信息
func (*wPSU) GetItemByThirdOriginalTranID(uid int64, originalTranID string) *WebProductSubscribeUser {
	var table WebProductSubscribeUser
	ok, err := databases.GetEngine().Where("third_part_original_transaction_id=? and uid = ?",
		originalTranID, uid).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetListByThirdOriginalTranID 根据第三方交易ID 获取列表
func (*wPSU) GetListByThirdOriginalTranID(originalTranID string) []*WebProductSubscribeUser {
	var tables []*WebProductSubscribeUser
	err := databases.GetEngine().Where("third_part_original_transaction_id=?", originalTranID).Desc("id").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetItemByOrderID 根据订单ID 获取订阅信息
func (*wPSU) GetItemByOrderID(uid int64, orderID string) *WebProductSubscribeUser {
	var table WebProductSubscribeUser
	ok, err := databases.GetEngine().Where("uid = ? AND order_id = ?", uid, orderID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetItem 获取会员订阅信息
func (*wPSU) GetItem(id int64) *WebProductSubscribeUser {
	var table WebProductSubscribeUser
	ok, err := databases.GetEngine().ID(id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*wPSU) GetItemByOriginalOrderID(uid int64, orderID string) *WebProductSubscribeUser {
	var table WebProductSubscribeUser
	ok, err := databases.GetEngine().Where("uid = ? AND original_order_id = ?", uid, orderID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*wPSU) GetItemByOrderIDBySession(uid int64, orderID string, session *xorm.Session) *WebProductSubscribeUser {
	var table WebProductSubscribeUser
	ok, err := session.Where("uid = ? AND order_id = ?", uid, orderID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*wPSU) CheckOfferTypeHistory(uid, productID int64, orderID string, offerType int) []*WebProductSubscribeUser {
	var tables []*WebProductSubscribeUser
	err := databases.GetEngine().Where("uid = ? AND original_order_id != ? AND product_id = ?", uid,
		orderID, productID).And("offer_type = ?", offerType).Desc("id").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetItemByID 根据订单ID 获取订阅信息
func (*wPSU) GetItemByID(id int64) *WebProductSubscribeUser {
	var table WebProductSubscribeUser
	ok, err := databases.GetEngine().Where("id = ?", id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
