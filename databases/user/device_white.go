package user

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
)

type DeviceWhite struct {
	ID         int64  `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	UID        int64  `xorm:"not null default 0 BIGINT(20) 'uid'" json:"uid"`
	AdminName  string `xorm:"not null default '' VARCHAR(128) 'admin_name'" json:"admin_name"`
	Desc       string `xorm:"not null default '' VARCHAR(256) 'desc'" json:"desc"`
	IsDel      int32  `xorm:"not null default 2 TINYINT(1) 'is_del'" json:"is_del"`
	CreateTime int64  `xorm:"not null INT(11) 'create_time'" json:"create_time"`
	UpdateTime int64  `xorm:"not null INT(11) 'update_time'" json:"update_time"`
}

func (w *DeviceWhite) TableName() string {
	return "user_device_white"
}

// Save 保存数据
func (w *DeviceWhite) Save() error {
	w.CreateTime = time.Now().Unix()
	w.UpdateTime = w.CreateTime
	_, err := databases.GetEngineMaster().Insert(w)
	return err
}

// Update 更新数据
func (w *DeviceWhite) Update() error {
	w.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(w.ID).Update(w)
	return err
}

type deviceWhite struct{}

var TbDeviceWhite deviceWhite

func (*deviceWhite) GetWhiteUser(uid int64) *DeviceWhite {
	var table DeviceWhite
	ok, err := databases.GetEngine().
		Where("uid = ? and is_del = ?",
			uid, library.No).
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
