package user

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	libu "gitlab.dailyyoga.com.cn/server/children/library/user"
)

type StorePraise struct {
	ID int64 `xorm:"int(11) notnull pk autoincr 'id'" json:"id"`
	// 用户ID
	UID int64 `xorm:"not null bigint(20) 'uid'" json:"uid"`
	// 图片
	Img string `xorm:"not null varchar(255) 'img'" json:"img"`
	// 设备ID
	DeviceID string `xorm:"not null varchar(255) 'device_id'" json:"device_id"`
	// 审核状态 0 待审核 1 通过 2 拒绝
	AuditStatus int32 `xorm:"not null varchar(255) 'audit_status'" json:"audit_status"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'" json:"create_time"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'" json:"update_time"`
	// 资源id
	PraiseResourceID int64 `xorm:"not null int(11) 'praise_resource_id'" json:"praise_resource_id"`
	// 1 应用市场，2巨量  好评引导平台
	PraiseLatform int32 `xorm:"not null default 1 TINYINT(1) 'praise_latform'" json:"praise_latform"`
	// 1: "VIP"  5: "精练课堂卡"  7: "凯格尔专区卡"   8 点子食谱
	AwardType int64 `xorm:"not null int(11) 'award_type'"`
	// 奖励时长
	AwardDuration int64 `xorm:"not null int(11) 'award_duration'"`
	EntranceType  int32 `xorm:"not null int(11) 'entrance_type'" json:"entrance_type"`
}

// TableName 获取表名
func (s *StorePraise) TableName() string {
	return "store_praise"
}

// Save 保存数据
func (s *StorePraise) Save() error {
	s.CreateTime = time.Now().Unix()
	s.UpdateTime = s.CreateTime

	_, err := databases.GetEngineMaster().Insert(s)
	return err
}

type praise struct{}

var TbStorePraise praise

// GetItem 根据uid 获取好评
func (a *praise) GetItemByUID(uid int64) *StorePraise {
	var table StorePraise

	ok, err := databases.GetEngine().Where("uid = ? ", uid).In("audit_status",
		[]libu.StorePraiseAuditInt{libu.StorePraiseAuditEnum.Wait, libu.StorePraiseAuditEnum.Pass}).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetItem 根据设备ID 获取好评
func (a *praise) GetItemByDeviceID(deviceID string) *StorePraise {
	var table StorePraise

	ok, err := databases.GetEngine().Where("device_id = ? ", deviceID).In("audit_status",
		[]libu.StorePraiseAuditInt{libu.StorePraiseAuditEnum.Wait, libu.StorePraiseAuditEnum.Pass}).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
