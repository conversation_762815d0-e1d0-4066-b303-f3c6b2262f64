package user

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
)

type PraiseResource struct {
	ID int64 `xorm:"int(11) notnull pk autoincr 'id'"`
	// 标题
	Title string `xorm:"not null varchar(255) 'title'"`
	// 1 应用市场，2巨量  好评引导平台
	PraiseLatform int32 `xorm:"not null default 1 TINYINT(1) 'praise_latform'"`
	// 1 有抽奖 2 固定奖励
	IsLottery int64 `xorm:"not null int(11) 'is_lottery'"`
	// 1: "VIP"
	AwardType int64 `xorm:"not null int(11) 'award_type'"`
	// 奖励时长
	AwardDuration int64 `xorm:"not null int(11) 'award_duration'"`
	// 素材图
	Img string `xorm:"not null default '' varchar(255) 'img'"`
	// 是否删除
	IsDel int32 `xorm:"not null default 2 TINYINT(1) 'is_del'"`
	// 所选渠道
	ClientChannel string `xorm:"varchar(512) notnull default '' 'client_channel'"`
	// 用户分群ID
	UserGroupID int64 `xorm:"notnull default 0 'user_group_id'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (s *PraiseResource) TableName() string {
	return "store_praise_resource"
}

// Save 保存数据
func (s *PraiseResource) Save() error {
	s.CreateTime = time.Now().Unix()
	s.UpdateTime = s.CreateTime

	_, err := databases.GetEngineMaster().Insert(s)
	return err
}

// Update 更新数据
func (s *PraiseResource) Update() error {
	s.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().MustCols(
		"award_duration", "pag_resource_id", "img").ID(s.ID).Update(s)
	return err
}

type praiseResource struct{}

var TbPraiseResource praiseResource

func (a *praiseResource) GetItemByID(id int64) *PraiseResource {
	var table PraiseResource
	ok, err := databases.GetEngine().ID(id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*praiseResource) GetListByPage(page, pageSize int) []*PraiseResource {
	tables := make([]*PraiseResource, 0)
	session := databases.GetEngine().NewSession()
	defer session.Close()
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		session.Limit(pageSize, offset)
	}
	err := session.Desc("id").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (*praiseResource) GetList() []*PraiseResource {
	tables := make([]*PraiseResource, 0)
	err := databases.GetEngine().Where("is_del =? ",
		library.No).Desc("id").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
