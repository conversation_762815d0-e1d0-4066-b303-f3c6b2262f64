package user

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
)

type RecordWhite struct {
	ID         int64  `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	UID        int64  `xorm:"not null default 0 BIGINT(20) 'uid'" json:"uid"`
	AdminName  string `xorm:"not null default '' VARCHAR(128) 'admin_name'" json:"admin_name"`
	Desc       string `xorm:"not null default '' VARCHAR(256) 'desc'" json:"desc"`
	IsDel      int32  `xorm:"not null default 2 TINYINT(1) 'is_del'" json:"is_del"`
	CreateTime int64  `xorm:"not null INT(11) 'create_time'" json:"create_time"`
	UpdateTime int64  `xorm:"not null INT(11) 'update_time'" json:"update_time"`
}

func (r *RecordWhite) TableName() string {
	return "user_record_white"
}

// Save 保存数据
func (r *RecordWhite) Save() error {
	r.CreateTime = time.Now().Unix()
	r.UpdateTime = r.CreateTime
	_, err := databases.GetEngineMaster().Insert(r)
	return err
}

// Update 更新数据
func (r *RecordWhite) Update() error {
	r.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(r.ID).Update(r)
	return err
}

type recordWhite struct{}

var TbRecordWhite recordWhite

func (*recordWhite) GetWhiteUser(uid int64) *RecordWhite {
	var table RecordWhite
	ok, err := databases.GetEngine().
		Where("uid = ? and is_del = ?",
			uid, library.No).
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
