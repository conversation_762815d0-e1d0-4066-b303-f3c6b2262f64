package user

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
)

type CrackUser struct {
	ID int64 `xorm:"not null pk autoincr INT(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null bigint(20) 'uid'" json:"uid"`
	// 设备ID
	DeviceID string `xorm:"not null varchar(255) 'device_id'" json:"device_id"`
	// 信息
	Message string `xorm:"not null varchar(5000) 'message'" json:"message"`
	// 状态
	Status     int   `xorm:"not null default 0 TINYINT(3) 'status'" json:"status"`
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

func (c *CrackUser) TableName() string {
	return "crack_version_user"
}

// Save 保存数据
func (c *CrackUser) Save() error {
	c.CreateTime = time.Now().Unix()
	c.UpdateTime = c.CreateTime
	_, err := databases.GetEngineMaster().Insert(c)
	return err
}

// Update 更新数据
func (c *CrackUser) Update() error {
	c.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(c.ID).Update(c)
	return err
}

type crackUser struct{}

var TbCrackUser crackUser

// GetItem 根据uid 获取信息
func (c *crackUser) GetItem(uid int64) *CrackUser {
	var table CrackUser

	ok, err := databases.GetEngine().Where("uid = ? ", uid).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetItemByUIDAndDeviceID 根据uid 获取信息
func (c *crackUser) GetItemByUIDAndDeviceID(uid int64, deviceID string) *CrackUser {
	var table CrackUser
	ok, err := databases.GetEngine().Where("uid = ? AND device_id = ? AND status = ?", uid, deviceID,
		library.Yes).Desc("id").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetItemByUIDOrDeviceID 根据uid deviceID获取信息
func (c *crackUser) GetItemByUIDOrDeviceID(uid int64, deviceID string) *CrackUser {
	var table CrackUser
	ok, err := databases.GetEngine().Where("(uid = ? OR device_id = ? ) AND status = ?", uid, deviceID,
		library.Yes).Desc("id").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetItemByDeviceID 根据uid 获取信息
func (c *crackUser) GetItemByDeviceID(deviceID string) *CrackUser {
	var table CrackUser
	ok, err := databases.GetEngine().Where("device_id = ? AND status = ?", deviceID,
		library.Yes).Desc("id").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetByUIDList 根据uid 获取信息
func (c *crackUser) GetByUIDList(uidList []int64) []*CrackUser {
	var tables []*CrackUser
	err := databases.GetEngine().Where("status = ?", library.Yes).In("uid", uidList).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetByDeviceIDList 根据uid 获取信息
func (c *crackUser) GetByDeviceIDList(deviceIDList []string) []*CrackUser {
	var tables []*CrackUser
	err := databases.GetEngine().Where("status = ?", library.Yes).In("device_id", deviceIDList).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
