package user

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
)

type DeviceInfo struct {
	ID         int    `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	UID        int64  `xorm:"not null default 0 BIGINT(20) 'uid'" json:"uid"`
	DeviceID   string `xorm:"not null default '' VARCHAR(128) 'device_id'" json:"device_id"`
	LoginTime  int64  `xorm:"not null INT(10) 'login_time'" json:"login_time"`
	CreateTime int64  `xorm:"not null INT(10) 'create_time'" json:"create_time"`
	UpdateTime int64  `xorm:"not null INT(10) 'update_time'" json:"update_time"`
}

func (i *DeviceInfo) TableName() string {
	return "user_device_info"
}

// Save 保存数据
func (i *DeviceInfo) Save() error {
	i.CreateTime = time.Now().Unix()
	i.UpdateTime = i.CreateTime
	_, err := databases.GetEngineMaster().Insert(i)
	return err
}

// Update 更新数据
func (i *DeviceInfo) Update() error {
	i.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(i.ID).Update(i)
	return err
}

type deviceInfo struct{}

var TbDeviceInfo deviceInfo

func (*deviceInfo) GetDeviceInfo(uid int64, deviceID string) *DeviceInfo {
	var table DeviceInfo
	ok, err := databases.GetEngine().
		Where("uid = ? and device_id = ?",
			uid, deviceID).
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
