package user

import (
	"time"

	"github.com/go-xorm/xorm"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
)

type AccountThirdMoreAppID struct {
	ID          int64  `xorm:"pk autoincr not null comment('主键ID') int(11) 'id'"`
	ThirdOpenID string `xorm:"default 'NULL' comment('第三方账号码') varchar(128) 'third_open_id'"`
	ThirdUniqID string `xorm:"not null default '' comment('第三方账号唯一码') varchar(128) 'third_uniq_id'"`
	AppID       string `xorm:"not null default '' comment('第三方账号') varchar(64) 'app_id'"`
	SessionKey  string `xorm:"not null default '' comment('会话密钥') varchar(64) 'session_key'"`
	LoginType   int32  `xorm:"not null default 1 comment('第三方登录类型枚举见登录接口登录方式') tinyint(4) 'login_type'"`
	CreateTime  int64  `xorm:"not null default 0 comment('创建时间') int(11) 'create_time'"`
	UpdateTime  int64  `xorm:"not null default 0 comment('更新时间') int(11) 'update_time'"`
}

func (a *AccountThirdMoreAppID) TableName() string {
	return "account_third_more_appid"
}

func (a *AccountThirdMoreAppID) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}

// SaveByTran 保存数据
func (a *AccountThirdMoreAppID) SaveByTran(session *xorm.Session) error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := session.Insert(a)
	return err
}

// Update 更新数据
func (a *AccountThirdMoreAppID) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

// UpdateByTran 事务更新数据
func (a *AccountThirdMoreAppID) UpdateByTran(session *xorm.Session) error {
	a.UpdateTime = time.Now().Unix()
	_, err := session.ID(a.ID).Update(a)
	return err
}

type moreAppID struct{}

// TbMoreAppID 外部引用对象
var TbMoreAppID moreAppID

// GetUserByThirdOpenID 通过第三方账号获取第三方认证关联
func (a *moreAppID) GetUserByThirdOpenID(thirdUnID, thirdID, appID string) *AccountThirdMoreAppID {
	var table AccountThirdMoreAppID
	ok, err := databases.GetEngine().
		Where("third_uniq_id = ? and third_open_id = ? and app_id = ?", thirdUnID, thirdID, appID).
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetUserByThirdUniqID 获取信息
func (a *moreAppID) GetUserByThirdUniqID(thirdUnID string, loginType int32) []*AccountThirdMoreAppID {
	var tables []*AccountThirdMoreAppID
	err := databases.GetEngine().Where("third_uniq_id = ? and login_type=?", thirdUnID, loginType).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetItemByOpenID 通过openID获取第三方认证关联
func (a *moreAppID) GetItemByOpenID(openID, appID string) *AccountThirdMoreAppID {
	var table AccountThirdMoreAppID
	ok, err := databases.GetEngine().
		Where("third_open_id = ? and app_id = ?", openID, appID).
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetItemByThirdOpenIDAndUniqID 通过ThirdOpenID和ThirdUniqID获取第三方认证关联
func (a *moreAppID) GetItemByThirdOpenIDAndUniqID(thirdOpenID, thirdUniqID string) *AccountThirdMoreAppID {
	var table AccountThirdMoreAppID
	ok, err := databases.GetEngine().
		Where("third_open_id = ? and third_uniq_id = ?", thirdOpenID, thirdUniqID).
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
