package user

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
)

type PraiseFinishPop struct {
	ID int64 `xorm:"int(11) notnull pk autoincr 'id'"`
	// 标题
	Title string `xorm:"not null varchar(255) 'title'"`
	// 用户分群
	UserGroup int64 `xorm:"not null int(11) 'user_group'"`
	// 资源id
	PraiseResourceID int64 `xorm:"not null int(11) 'praise_resource_id'"`
	// 素材图
	Img string `xorm:"not null default '' varchar(255) 'img'"`
	// 按钮文案
	ButtonText string `xorm:"not null default '' varchar(255) 'button_text'"`
	// 按钮文案
	ButtonTextSub string `xorm:"not null default '' varchar(255) 'button_text_sub'"`
	// 弹几天
	DayNum int `xorm:"not null int(11) 'day_num'"`
	// 弹出次数
	PopNumDay int `xorm:"not null int(11) 'pop_num_day'"`
	// 弹出次数 次
	PopNumNum int `xorm:"not null int(11) 'pop_num_num'"`
	// 是否删除
	IsDel int32 `xorm:"not null default 2 TINYINT(1) 'is_del'"`
	// 是否在线
	IsOnline int32 `xorm:"not null tinyint(1) 'is_online'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (s *PraiseFinishPop) TableName() string {
	return "store_praise_finish_pop"
}

// Save 保存数据
func (s *PraiseFinishPop) Save() error {
	s.CreateTime = time.Now().Unix()
	s.UpdateTime = s.CreateTime

	_, err := databases.GetEngineMaster().Insert(s)
	return err
}

// Update 更新数据
func (s *PraiseFinishPop) Update() error {
	s.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().MustCols("user_group").ID(s.ID).Update(s)
	return err
}

type praiseFinishPop struct{}

var TbPraiseFinishPop praiseFinishPop

func (a *praiseFinishPop) GetItemByID(id int64) *PraiseFinishPop {
	var table PraiseFinishPop
	ok, err := databases.GetEngine().ID(id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*praiseFinishPop) GetList() []*PraiseFinishPop {
	tables := make([]*PraiseFinishPop, 0)
	err := databases.GetEngine().Where("is_del =? and is_online = ?",
		library.No, library.Yes).Desc("id").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
