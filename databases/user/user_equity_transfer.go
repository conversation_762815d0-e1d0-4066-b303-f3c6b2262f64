package user

import (
	"time"

	"github.com/go-xorm/xorm"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	db "gitlab.dailyyoga.com.cn/server/children/databases"
)

type EquityTransfer struct {
	ID         int64 `xorm:"pk autoincr BIGINT(20) 'id'" json:"id"`
	DUID       int64 `xorm:"not null default 0 INT(10) 'd_uid'" json:"d_uid"`
	UID        int64 `xorm:"int(11) unsigned notnull 'uid'" json:"uid"`
	CreateTime int64 `xorm:"not null default 0 INT(10) 'create_time'" json:"create_time"`
	UpdateTime int64 `xorm:"not null default 0 INT(10) 'update_time'" json:"update_time"`
}

// TableName Get table name
func (u *EquityTransfer) TableName() string {
	return "user_equity_transfer"
}

func (u *EquityTransfer) Save() error {
	u.CreateTime = time.Now().Unix()
	u.UpdateTime = u.CreateTime
	_, err := db.GetEngineMaster().Insert(u)
	return err
}

// Save 保存
func (u *EquityTransfer) SaveByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.Insert(u)
	return err
}

// update 修改
func (u *EquityTransfer) UpdateByTransaction(session *xorm.Session) error {
	u.UpdateTime = time.Now().Unix()
	_, err := session.ID(u.ID).Update(u)
	return err
}

type equityTransfer struct {
}

var TbUserEquityTransfer equityTransfer

// GetItemByDUID 获取特权卡信息
func (w *equityTransfer) GetItemByDUID(dUID int64) *EquityTransfer {
	var table EquityTransfer
	ok, err := db.GetEngine().Where("d_uid = ?", dUID).Get(&table)
	if err != nil {
		logger.Errorf("获取特权卡产品失败, err: %s", err)
		return nil
	}
	if !ok {
		return nil
	}

	return &table
}
