package user

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library/user"
)

type VipHistory struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// UID
	UID int64 `xorm:"not null bigint(20) 'uid'"`
	// 原会员开始时间
	OriginStartTime int64 `xorm:"not null int(11) 'origin_start_time'"`
	// 原会员结束时间
	OriginEndTime int64 `xorm:"not null int(11) 'origin_end_time'"`
	// 新会员开始时间
	StartTime int64 `xorm:"not null int(11) 'start_time'"`
	// 新会员结束时间
	EndTime int64 `xorm:"not null int(11) 'end_time'"`
	// 会员时长类型 1天2月3年
	DurationType int `xorm:"not null tinyint(4) 'duration_type'"`
	// 会员时长
	DurationValue int `xorm:"not null int(6) 'duration_value'"`
	// 会员时长
	OperateType user.VipOperateType `xorm:"not null int(6) 'operation_type'"`
	// 会员来源 1 系统发放 2 后台发放
	SourceType user.VipSourceType `xorm:"not null int(11) 'source_type'"`
	// 变更原因 1 购买产品 2 退款 3 活动发放 999 其他
	ChangeReason user.VipChangeReason `xorm:"not null int(11) 'change_reason'"`
	// 会员类型 1 会员  2语音包
	VipType int `xorm:"not null tinyint(1) 'vip_type'" json:"vip_type"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
	// 订单ID
	OrderID string `xorm:"not null varchar(64) 'order_id'"`
}

// TableName 获取表名
func (VipHistory) TableName() string {
	return "account_vip_record"
}

// Save 保存数据
func (a *VipHistory) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}

func (a *VipHistory) Update() error {
	_, err := databases.GetEngineMaster().ID(a.ID).Update(a)
	if err != nil {
		return err
	}
	return nil
}

// SaveByTran 事务保存数据
func (a *VipHistory) SaveByTran(session *xorm.Session) error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := session.Insert(a)
	return err
}

type viphistory struct{}

var TbVipHistory viphistory

func (v *viphistory) GetListByUID(uid int64) []*VipHistory {
	var tables []*VipHistory
	err := databases.GetEngine().
		Where("uid= ? ", uid).Desc("create_time").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (v *viphistory) GetListByIDRange(start, end int) []*VipHistory {
	var tables []*VipHistory
	err := databases.GetEngine().Where("id >= ? AND id < ?", start, end).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (v *viphistory) GetLastByUID(uid int64) *VipHistory {
	var table VipHistory
	ok, err := databases.GetEngine().Where("uid = ?", uid).
		Where("operation_type = ?", 1).Desc("id").Limit(1).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
