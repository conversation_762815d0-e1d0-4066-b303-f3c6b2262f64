package user

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
)

type AdminOperateVipRecord struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'" json:"id"`
	// UID
	UID int64 `xorm:"not null bigint(20) 'uid'" json:"uid"`
	// 会员变更记录表ID
	RecordID int64 `xorm:"not null int(11) 'record_id'" json:"record_id"`
	// 会员时长类型 1天2月3年
	DurationType int `xorm:"not null tinyint(4) 'duration_type'" json:"duration_type"`
	// 会员时长
	DurationValue int `xorm:"not null int(6) 'duration_value'" json:"duration_value"`
	// 操作类型 加减
	OperateType int `xorm:"not null int(6) 'operate_type'" json:"operate_type"`
	// 备注
	Desc string `xorm:"not null varchar(255) 'desc'" json:"desc"`
	// 变更原因 1 购买产品 2 退款 3 活动发放 999 其他
	ChangeReason int `xorm:"not null int(11) 'change_reason'" json:"change_reason"`
	// 后台用户
	AdminName string `xorm:"not null varchar(255) 'admin_name'" json:"admin_name"`
	// 会员类型 1 会员  2语音包
	VipType int `xorm:"not null tinyint(1) 'vip_type'" json:"vip_type"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'" json:"create_time"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'" json:"update_time"`
}

// TableName 获取表名
func (AdminOperateVipRecord) TableName() string {
	return "admin_operate_vip_record"
}

// SaveByTran 事务保存数据
func (a *AdminOperateVipRecord) SaveByTran(session *xorm.Session) error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := session.Insert(a)
	return err
}

type aoRecord struct{}

var TbAORecord aoRecord

// GetList 获取后台操作会员记录
func (*aoRecord) GetList(uid int64, page, pageSize int) []*AdminOperateVipRecord {
	tables := make([]*AdminOperateVipRecord, 0)
	session := databases.GetEngine().NewSession()
	defer session.Close()
	if uid > 0 {
		session.Where("uid = ? ", uid)
	}
	offset := (page - 1) * pageSize
	err := session.Limit(pageSize, offset).Desc("id").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
