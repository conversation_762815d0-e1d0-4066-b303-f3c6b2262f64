package user

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"

	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
)

type DeviceLock struct {
	ID             int64  `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	UID            int64  `xorm:"not null default 0 BIGINT(20) 'uid'" json:"uid"`
	Mobile         string `xorm:"not null default '' VARCHAR(24) 'mobile'" json:"mobile"`
	ValidType      int32  `xorm:"not null default 0 TINYINT(1) 'valid_type'" json:"valid_type"`
	ValidResources string `xorm:"not null default '' VARCHAR(256) 'valid_resources'" json:"valid_resources"`
	AdminName      string `xorm:"not null default '' VARCHAR(128) 'admin_name'" json:"admin_name"`
	OperateTime    int64  `xorm:"not null default 0 INT(11) 'operate_time'" json:"operate_time"`
	LockStatus     int32  `xorm:"not null default 1 TINYINT(1) 'lock_status'" json:"lock_status"`
	CreateTime     int64  `xorm:"not null INT(11) 'create_time'" json:"create_time"`
	UpdateTime     int64  `xorm:"not null INT(11) 'update_time'" json:"update_time"`
}

func (u *DeviceLock) TableName() string {
	return "user_device_lock"
}

// Save 保存数据
func (u *DeviceLock) Save() error {
	u.CreateTime = time.Now().Unix()
	u.UpdateTime = u.CreateTime
	_, err := databases.GetEngineMaster().Insert(u)
	return err
}

// Update 更新数据
func (u *DeviceLock) Update() error {
	u.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(u.ID).Update(u)
	return err
}

type deviceLock struct{}

var TbDeviceLock deviceLock

func (*deviceLock) GetLockUser(uid int64) *DeviceLock {
	var table DeviceLock
	ok, err := databases.GetEngine().
		Where("uid = ? and lock_status = ?",
			uid, library.Yes).
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*deviceLock) GetLockUserByMobile(mobile string) *DeviceLock {
	var table DeviceLock
	ok, err := databases.GetEngine().
		Where("mobile = ? and lock_status = ?",
			mobile, library.Yes).
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
