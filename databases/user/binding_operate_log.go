package user

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/children/databases"
)

type BindingOperateLog struct {
	ID          int64  `xorm:"'id'"`           // 主键
	UID         int64  `xorm:"'uid'"`          // uid
	Parameter   string `xorm:"'parameter'"`    // 参数
	OperateUser string `xorm:"'operate_user'"` // 操作人
	CreateTime  int64  `xorm:"'create_time'"`  // 创建时间
	UpdateTime  int64  `xorm:"'update_time'"`  // 更新时间
}

func (b *BindingOperateLog) TableName() string {
	return "binding_operate_log"
}

// Save 保存数据
func (b *BindingOperateLog) Save() error {
	b.CreateTime = time.Now().Unix()
	b.UpdateTime = b.CreateTime
	_, err := databases.GetEngineMaster().Insert(b)
	return err
}

// Update 更新数据
func (b *BindingOperateLog) Update() error {
	b.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(b.ID).Update(b)
	return err
}
