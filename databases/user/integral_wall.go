package user

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
)

type IntegralWall struct {
	ID         int64  `xorm:"'id'" json:"id"`
	IP         string `xorm:"'ip'" json:"ip"`
	OS         string `xorm:"'os'" json:"os"`
	Device     string `xorm:"'device'" json:"device"`
	Idfa       string `xorm:"'idfa'" json:"idfa"`
	AppID      string `xorm:"'appid'" json:"appid"`
	Keywords   string `xorm:"'keywords'" json:"keywords"`
	CreateTime int64  `xorm:"'create_time'" json:"create_time"`
	UpdateTime int64  `xorm:"'update_time'" json:"update_time"`
}

type integralwall struct{}

var TbIntegralWall integralwall

func (i *IntegralWall) TableName() string {
	return "integral"
}

// Save 保存数据
func (i *IntegralWall) Save() error {
	i.CreateTime = time.Now().Unix()
	i.UpdateTime = i.CreateTime

	_, err := databases.GetEngineMaster().Insert(i)
	return err
}

func (i *integralwall) GetItem(ip, os, device, idfa string) *IntegralWall {
	var table IntegralWall
	if idfa != "" {
		ok, err := databases.GetEngine().Where("idfa = ?", idfa).OrderBy("id desc").Get(&table)
		if err != nil {
			logger.Error(err)
			return nil
		}
		if !ok {
			return nil
		}
		if table.IP == ip || table.OS == os || table.Device == device {
			return &table
		}
	} else {
		ok, err := databases.GetEngine().Where("ip = ?", ip).OrderBy("id desc").Get(&table)
		if err != nil {
			logger.Error(err)
			return nil
		}
		if !ok {
			return nil
		}
		if table.OS == os || table.Device == device {
			return &table
		}
	}
	return nil
}
