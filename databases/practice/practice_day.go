package practice

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
)

// Log 用户练习记录
type Log struct {
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) 'uid'"`
	// OB计划ID
	ProgramID int64 `xorm:"not null int(11) 'program_id'"`
	// 课程ID
	CourseID int64 `xorm:"not null int(11) 'course_id'"`
	// 第几天
	OrderDay int `xorm:"not null tinyint(4) 'order_day'"`
	// 卡路里
	Calories int `xorm:"not null int(11) 'calories'"`
	// 练习时长(min)
	Minutes int `xorm:"not null int(11) 'minutes'"`
	// 是否中途退出：1-是，0-否
	IsExit int `xorm:"not null tinyint(1) 'is_exit'"`
	// 练习感受
	PracticeFeel int32 `xorm:"not null tinyint(2) 'practice_feel'"`
	// 练习开始时间
	PracticeStartTime int64 `xorm:"not null int(11) 'practice_start_time'"`
	// 练习类型 1练习开始 2练习中退 3练习完成
	PracticeType int `xorm:"not null tinyint(1) 'practice_type'"`
	// 练习场景 1 课程详情 2 OB课表 3 即刻开练
	SceneType int `xorm:"not null tinyint(1) 'scene_type'"`
	// 练习时长(s)
	PlayTime int `xorm:"not null int(11) 'play_time'"`
	// 练习标签
	PracticeLabels string `xorm:"not null varchar(512) 'practice_labels'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (Log) TableName() string {
	return "user_practice_log"
}

// Save 保存数据
func (p *Log) Save() error {
	p.CreateTime = time.Now().Unix()
	p.UpdateTime = p.CreateTime

	_, err := databases.GetEngineMaster().Insert(p)
	return err
}

// Update 更新数据
func (p *Log) Update() error {
	p.UpdateTime = time.Now().Unix()

	_, err := databases.GetEngineMaster().ID(p.ID).Update(p)
	return err
}

type practiceLog struct{}

// TbPracticeLog 外部引用对象
var TbPracticeLog practiceLog

func (*practiceLog) GetItem(uid, courseID, practiceCurrentTime int64) *Log {
	var table Log
	ok, err := databases.GetEngine().Where("uid = ? and course_id = ? and practice_start_time = ?",
		uid, courseID, practiceCurrentTime).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetLogByRange 根据时间区间获取
func (p *practiceLog) GetLogByRange(uid, start, end, page, pageSize int64) []*Log {
	var tables []*Log
	offset := (page - 1) * pageSize
	err := databases.GetEngine().
		Where("uid = ?", uid).
		Where("practice_start_time >= ? and practice_start_time <= ?", start, end).
		Desc("practice_start_time").
		Limit(int(pageSize), int(offset)).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetFavoriteCourse 根据时间区间获取最喜欢的课程
func (p *practiceLog) GetFavoriteCourse(uid, start, end int64) []*Log {
	var tables []*Log
	err := databases.GetEngine().
		Select("COUNT(id) as practice_count,program_id,course_id").
		Where("uid = ?", uid).
		Where("practice_start_time >= ? and practice_start_time <= ?", start, end).
		GroupBy("course_id").
		OrderBy("practice_count desc").
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetFavoriteCourseItem 获取最喜欢的课程一节课程
func (p *practiceLog) GetFavoriteCourseItem(uid, courseID, start, end int64) *Log {
	var table Log
	ok, err := databases.GetEngine().
		Where("uid = ?", uid).
		Where("course_id = ?", courseID).
		And("practice_start_time >= ? and practice_start_time <= ?", start, end).
		OrderBy("practice_start_time desc").
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (p *practiceLog) GetAllByUID(uid int64) []Log {
	var tables []Log
	err := databases.GetEngine().Where("uid = ?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (*practiceLog) GetPracticeCount(uid, courseID int64) int64 {
	var table Log
	count, err := databases.GetEngine().Where("uid = ? and course_id = ?",
		uid, courseID).Count(&table)
	if err != nil {
		logger.Error(err)
		return 0
	}
	return count
}

// GetListByRange 根据时间区间获取
func (p *practiceLog) GetListByRange(uid, start, end int64) []*Log {
	var tables []*Log
	err := databases.GetEngine().
		Where("uid = ?", uid).
		Where("practice_start_time >= ? and practice_start_time <= ?", start, end).
		Desc("practice_start_time").
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (p *practiceLog) GetAllByUIDLimit(uid int64) *Log {
	var tables Log
	ok, err := databases.GetEngine().
		Where("uid = ?", uid).
		Limit(1).
		Get(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &tables
}

func (p *practiceLog) GetListByRangeByProgram(uid, start, end int64) []*Log {
	var tables []*Log
	err := databases.GetEngine().
		Where("uid = ?", uid).
		Where("practice_start_time >= ? and practice_start_time <= ? and program_id != 0", start, end).
		Desc("practice_start_time").
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
