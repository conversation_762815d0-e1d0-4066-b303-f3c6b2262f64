package practice

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/children/databases"
)

// Day 用户练习日期
type Day struct {
	ID int64 `xorm:"not null autoincr pk int(11) unsigned 'id'"`
	// 用户ID
	UID int64 `xorm:"not null int(11) 'uid'"`
	// 练习日期
	DateIndex string `xorm:"not null varchar(16) 'date_index'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (Day) TableName() string {
	return "user_practice_day"
}

// Save 保存数据
func (p *Day) Save() error {
	p.CreateTime = time.Now().Unix()
	p.UpdateTime = p.CreateTime

	_, err := databases.GetEngineMaster().Insert(p)
	return err
}

// Update 更新数据
func (p *Day) Update() error {
	p.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(p.ID).Update(p)
	return err
}
