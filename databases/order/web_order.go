package order

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/pay"
)

type WebOrder struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户UID
	UID int64 `xorm:"not null int(11) 'uid'"`
	// 订单ID
	OrderID string `xorm:"not null varchar(64) 'order_id'"`
	// 商品ID
	ProductID int64 `xorm:"not null int(10) 'product_id'"`
	// 订单状态 0 待支付 1 已支付
	OrderStatus pay.OrderStatusInt `xorm:"not null tinyint(1) 'order_status'"`
	// 订单金额
	OrderAmount float64 `xorm:"not null decimal(10,2) 'order_amount'"`
	// 支付方式
	PayType int `xorm:"not null tinyint(4) 'pay_type'"`
	// 支付商户号
	MchID string `xorm:"not null varchar(128) 'mch_id'"`
	// 支付时间
	PayTime int64 `xorm:"not null int(11) 'pay_time'"`
	// 是否续订 1 是 2 否
	IsRenew int `xorm:"not null tinyint(1) 'is_renew'"`
	// 订单来源
	Source        string `xorm:"not null int(11) 'source'"`
	SourceID      string `xorm:"not null varchar(128) 'source_id'"`
	SourceRefer   string `xorm:"not null int(11) 'source_refer'"`
	SourceReferID string `xorm:"not null varchar(128) 'source_refer_id'"`
	// 客户端版本
	Version string `xorm:"not null varchar(32) 'app_version'"`
	// 客户端渠道号
	Channel int `xorm:"not null int(11) 'app_channel'"`
	// 订单类型
	PaymentOrderType int `xorm:"not null int(11) 'payment_order_type'"`
	// 子产品
	ProductSubID int64 `xorm:"not null int(11) 'product_sub_id'"`
	// 关联挑战赛产品ID
	ChallengeID int64 `xorm:"not null int(11) 'challenge_id'"`
	// 是否有赠品 1是 2否
	HasGift int32 `xorm:"not null int(11) 'has_gift'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// Save 保存数据
func (a *WebOrder) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *WebOrder) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

// SaveByTran 事务保存数据
func (a *WebOrder) SaveByTran(session *xorm.Session) error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}

// UpdateByTran 事务更新数据
func (a *WebOrder) UpdateByTran(session *xorm.Session) error {
	a.UpdateTime = time.Now().Unix()
	_, err := session.ID(a.ID).MustCols("order_amount").Update(a)
	return err
}

// TableName 获取表名
func (WebOrder) TableName() string {
	return "web_order"
}

type tbwebOrder struct{}

var TbWebOrder tbwebOrder

func (tbwebOrder) GetItemByOrderID(orderID string) *WebOrder {
	var table WebOrder

	ok, err := databases.GetEngine().Where("order_id = ?", orderID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (tbwebOrder) GetItemByUID(uid int64) *WebOrder {
	var table WebOrder

	ok, err := databases.GetEngine().Where("uid = ? and order_status = ?", uid, pay.OrderStatus.Paid).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetOtherOrderByUID 获取用户除此订单之外是否有其他订单
func (tbwebOrder) GetOtherOrderByUID(uid int64, orderID string) *WebOrder {
	var table WebOrder

	ok, err := databases.GetEngine().Where("uid = ? and order_id != ? and order_status = ?",
		uid, orderID, pay.OrderStatus.Paid).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetListByUID 获取用户订单列表
func (tbwebOrder) GetListByUID(uid int64) []*WebOrder {
	var tables []*WebOrder

	err := databases.GetEngine().Where("uid = ? and order_status = ?", uid, pay.OrderStatus.Paid).
		Desc("id").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetAllListByUID 获取用户订单列表包含未付款订单
func (tbwebOrder) GetAllListByUID(uid int64) []*WebOrder {
	var tables []*WebOrder

	err := databases.GetEngine().Where("uid = ?", uid).
		Desc("id").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (tbwebOrder) GetItemByUIDProductID(uid, productID int64) *WebOrder {
	var table WebOrder
	ok, err := databases.GetEngine().Where("uid = ? and order_status = ? and product_id = ?",
		uid, pay.OrderStatus.Paid, productID).OrderBy("id desc").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (tbwebOrder) GetOrderListByOrder(orderIDArr []string) []*WebOrder {
	var tables []*WebOrder
	err := databases.GetEngine().Where("order_status = ?", library.Yes).In("order_id", orderIDArr).Find(&tables)
	if err != nil {
		logger.Error("查询订单失败", err)
		return nil
	}
	return tables
}

func (tbwebOrder) GetListByUIDAndTime(uid, minTime, maxTime int64) []*WebOrder {
	var tables []*WebOrder
	err := databases.GetEngine().Where("uid = ? and order_status = ? and update_time > ? and update_time < ?",
		uid, pay.OrderStatus.Paid, minTime, maxTime).Desc("id").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
