package order

import (
	"time"

	"github.com/go-xorm/xorm"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library/pay"
)

type TiktokContract struct {
	ID                 int64                  `xorm:"pk autoincr not null comment('主键') int 'id'"`
	OrderID            string                 `xorm:"not null default '' varchar(64) 'order_id'"`
	UID                int64                  `xorm:"not null default 0 bigint 'uid'"`
	OpenID             string                 `xorm:"not null varchar(255) 'open_id'"`
	ProductID          int64                  `xorm:"not null default 0 int 'product_id'"`
	ChangeType         pay.ContractChangeType `xorm:"not null default 0 tinyint 'change_type'"`
	ContractCode       string                 `xorm:"not null default '' varchar(50) 'contract_code'"`
	MerchantID         string                 `xorm:"not null default '' varchar(100) 'app_id'"` // 抖音的商户ID
	ContractID         string                 `xorm:"not null default '' varchar(100) 'contract_id'"`
	ServiceID          string                 `xorm:"not null default '' varchar(100) 'service_id'"`
	ExecuteTime        string                 `xorm:"not null default '' varchar(50) 'execute_time'"`
	OfferType          int32                  `xorm:"not null default 1  tinyint 'offer_type'"`
	OfferFirstBuyPrice float64                `xorm:"default 0.00 DECIMAL(10,2) 'offer_first_buy_price'"`
	OfferFirstBuyCycle int32                  `xorm:"not null default 0 tinyint 'offer_first_buy_cycle'"`
	OfferTrialPrice    float64                `xorm:"default 0.00 DECIMAL(10,2) 'offer_trial_price'" `
	OfferTrialDay      int32                  `xorm:"not null default 0 INT(5) 'offer_trial_day'" `
	CreateTime         int64                  `xorm:"not null default 0 comment('创建时间') int 'create_time'"`
	UpdateTime         int64                  `xorm:"not null default 0 comment('更新时间') int 'update_time'"`
}

func (a *TiktokContract) TableName() string {
	return "web_order_tiktok_contract"
}

// Save 保存数据
func (a *TiktokContract) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *TiktokContract) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

// UpdateByTran 事务更新数据
func (a *TiktokContract) UpdateByTran(session *xorm.Session) error {
	a.UpdateTime = time.Now().Unix()
	_, err := session.ID(a.ID).Update(a)
	return err
}

// GetMerchantID 根据签约协议获取商户ID
func (a *TiktokContract) GetMerchantID() string {
	if a.MerchantID != "" {
		return a.MerchantID
	}
	return ""
}

type tiktokContract struct{}

var TbTiktokContract tiktokContract

func (*tiktokContract) GetItemByContractCode(code string) *TiktokContract {
	var table TiktokContract
	ok, err := databases.GetEngine().Where("contract_code = ?", code).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*tiktokContract) GetItemByOrderID(orderID string) *TiktokContract {
	var table TiktokContract
	ok, err := databases.GetEngine().Where("order_id = ?", orderID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetValidItemByTimeRange 获取时间段内是否有已签约的订单
func (*tiktokContract) GetValidItemByTimeRange(min, max int64) *TiktokContract {
	var table TiktokContract
	ok, err := databases.GetEngine().Where("create_time >= ? and create_time < ? ", min, max).
		And("change_type> ?", 0).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetListByUID 获取用户的所有记录
func (*tiktokContract) GetListByUID(uid int64) []*TiktokContract {
	var tables []*TiktokContract
	err := databases.GetEngine().Where("uid = ?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetValidListByTimeRange 获取签约的列表
func (*tiktokContract) GetValidListByTimeRange(min, max int64) []*TiktokContract {
	var tables []*TiktokContract
	err := databases.GetEngine().Where("create_time >= ? and create_time < ? ", min, max).
		And("change_type > ?", 0).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
