package order

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
)

type RefundUser struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// UID
	UID int64 `xorm:"not null bigint(11) 'uid'"`
	// 退款ID
	RefundID int64 `xorm:"not null int(11) 'refund_id'"`
	// 订单ID
	OrderID string `xorm:"not null varchar(64) 'order_id'"`
	// 手机号
	PhoneNumber string `xorm:"not null varchar(64) 'phone_number'"`
	// 昵称
	NickName string `xorm:"not null varchar(64) 'nick_name'"`
	// 扣款方式 1 微信/支付宝订阅自动扣款   2 微信/支付宝手动支付
	DebitMethod int32 `xorm:"not null tinyint(1) 'debit_method'"`
	// 扣款时间
	DebitTime string `xorm:"not null varchar(16) 'debit_time'"`
	// 扣款时间
	DebitAmount float32 `xorm:"not null decimal(10,2) 'debit_amount'"`
	// 具体问题
	SpecificIssues string `xorm:"not null varchar(500) 'specific_issues'"`
	// 订单截图
	OrderPictureURL string `xorm:"not null varchar(200) 'order_picture_url'"`
	// 沟通状态 1 未沟通、2 已退款、3 已忽略
	CommunicateStatus int32 `xorm:"not null tinyint(4) 'communicate_status'"`
	// 备注
	Remark string `xorm:"not null varchar(300) 'remark'"`
	// 退费原因
	ReasonType int `xorm:"not null tinyint(4) 'reason_type'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
	// 自动退款
	IsAutoRefund int32 `xorm:"not null tinyint(1) 'is_auto_refund'"`
}

// TableName 获取表名
func (RefundUser) TableName() string {
	return "order_refund_user"
}

// Save 保存数据
func (a *RefundUser) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *RefundUser) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

// UpdateByTran 事务更新数据
func (a *RefundUser) UpdateByTran(session *xorm.Session) error {
	a.UpdateTime = time.Now().Unix()
	_, err := session.ID(a.ID).Update(a)
	return err
}

type refundUser struct{}

var TbRefundUser refundUser

// GetRefundUserItem 根据订单号获取退款单
func (*refundUser) GetRefundUserItem(orderID string) *RefundUser {
	var table RefundUser
	ok, err := databases.GetEngine().Where("order_id = ? ", orderID).OrderBy("id desc").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetItemByID 获取退款单
func (*refundUser) GetItemByID(id int64) *RefundUser {
	var table RefundUser
	ok, err := databases.GetEngine().ID(id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*refundUser) GetLastItemByUID(uid int64) *RefundUser {
	var table RefundUser
	ok, err := databases.GetEngine().Where("uid = ?", uid).OrderBy("id DESC").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

type UserRefundSerach struct {
	UID               int64 `json:"uid"`
	CreateTimeStart   int64 `json:"create_time_start"`
	CreateTimeEnd     int64 `json:"create_time_end"`
	CommunicateStatus int32 `json:"communicate_status"`
	ReasonType        int32 `json:"reason_type"`
	Page              int   `json:"page"`
	PageSize          int   `json:"page_size"`
}

func (*refundUser) SearchList(params *UserRefundSerach) []*RefundUser {
	var tables []*RefundUser
	session := databases.GetEngine().NewSession()
	defer session.Close()
	if params.UID > 0 {
		session.And("uid = ?", params.UID)
	}
	if params.CreateTimeStart > 0 {
		session.And("create_time >= ?", params.CreateTimeStart)
	}
	if params.CreateTimeEnd > 0 {
		session.And("create_time <= ?", params.CreateTimeEnd)
	}
	if params.CommunicateStatus > 0 {
		session.And("communicate_status = ?", params.CommunicateStatus)
	}
	if params.ReasonType > 0 {
		session.And("reason_type = ?", params.ReasonType)
	}
	offset := (params.Page - 1) * params.PageSize
	session.OrderBy("id desc")
	err := session.Limit(params.PageSize, offset).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (*refundUser) GetRefundUserItemByOrderID(orderID string) []*RefundUser {
	var tables []*RefundUser
	err := databases.GetEngine().Where("order_id = ? ", orderID).OrderBy("id desc").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (*refundUser) GetRefundUserItemByRefundID(refundID int64) *RefundUser {
	var table RefundUser
	ok, err := databases.GetEngine().Where("refund_id = ?", refundID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*refundUser) GetRefundListByUID(uid int64) []*RefundUser {
	var tables []*RefundUser
	err := databases.GetEngine().Where("uid = ? ", uid).OrderBy("id desc").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
