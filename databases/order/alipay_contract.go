package order

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library/pay"
)

type AlipayContract struct {
	ID      int64  `xorm:"not null pk autoincr INT(11) 'id'"`
	OrderID string `xorm:"not null varchar(128) 'order_id'"`
	// 商品ID
	ProductID int64 `xorm:"not null int(10) 'product_id'"`
	// 用户UID
	UID int64 `xorm:"not null bigint(20) 'uid'"`
	// 变更类型：0-等待签约 1 签约 2解约
	ChangeType pay.ContractChangeType `xorm:"not null tinyint(3) 'change_type'"`
	// 商户签约号
	ContractCode string `xorm:"not null varchar(50) 'contract_code'"`
	// 用户签约成功后协议号
	ContractID string `xorm:"not null varchar(100) 'contract_id'"`
	// 支付宝商户app_id
	AppID string `xorm:"not null varchar(100) 'app_id'"`
	// 周期类型
	PeriodType string `xorm:"not null varchar(10) 'period_type'"`
	// 周期数
	Period int `xorm:"not null tinyint(3) 'period'"`
	// 首次扣款时间
	ExecuteTime string `xorm:"not null varchar(50) 'execute_time'"`
	// 签约模式
	SubscribeMode int `xorm:"not null tinyint(3) 'subscribe_mode'"`
	// 优惠类型：1-无 2-首购 3-试用 4-试用+首购
	OfferType int `xorm:"not null default 1 TINYINT(1) 'offer_type'" json:"offer_type"`
	// 首购优惠价
	OfferFirstBuyPrice float64 `xorm:"default 0.00 DECIMAL(10,2) 'offer_first_buy_price'" json:"offer_first_buy_price"`
	// 首购优惠周期
	OfferFirstBuyCycle int `xorm:"not null default 0 TINYINT(1) 'offer_first_buy_cycle'" json:"offer_first_buy_cycle"`
	// 试用价格
	OfferTrialPrice float64 `xorm:"default 0.00 DECIMAL(10,2) 'offer_trial_price'" json:"offer_trial_price"`
	// 试用天数
	OfferTrialDay int `xorm:"not null default 0 INT(5) 'offer_trial_day'" json:"offer_trial_day"`
	// 是否历史丢弃的订阅
	IsHistoryDiscard int `xorm:"not null default 0 TINYINT(2) 'is_history_discard'" json:"is_history_discard"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (AlipayContract) TableName() string {
	return "web_order_alipay_contract"
}

// Save 保存数据
func (a *AlipayContract) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *AlipayContract) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

// UpdateByTran 事务更新数据
func (a *AlipayContract) UpdateByTran(session *xorm.Session) error {
	a.UpdateTime = time.Now().Unix()
	_, err := session.ID(a.ID).Update(a)
	return err
}

type alipaucontract struct{}

var TbAlipayContract alipaucontract

func (*alipaucontract) GetItemByContractCode(code string) *AlipayContract {
	var table AlipayContract
	ok, err := databases.GetEngine().Where("contract_code = ?", code).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*alipaucontract) GetItemByOrderID(orderID string) *AlipayContract {
	var table AlipayContract
	ok, err := databases.GetEngine().Where("order_id = ?", orderID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetValidItemByTimeRange 获取时间段内是否有已签约的订单
func (*alipaucontract) GetValidItemByTimeRange(min, max int64) *AlipayContract {
	var table AlipayContract
	ok, err := databases.GetEngine().Where("create_time >= ? and create_time < ? ", min, max).
		And("change_type> ?", 0).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetListByUID 获取用户的所有记录
func (*alipaucontract) GetListByUID(uid int64) []*AlipayContract {
	var tables []*AlipayContract
	err := databases.GetEngine().Where("uid = ?", uid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetValidListByTimeRange 获取签约的列表
func (*alipaucontract) GetValidListByTimeRange(min, max int64) []*AlipayContract {
	var tables []*AlipayContract
	err := databases.GetEngine().Where("create_time >= ? and create_time < ? ", min, max).
		And("change_type > ?", 0).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
