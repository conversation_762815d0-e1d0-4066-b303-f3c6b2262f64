package order

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
)

type MchIDAccountConfig struct {
	ID          int64  `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	ProductType int32  `xorm:"not null default 1 TINYINT(3) 'product_type'" json:"product_type"`
	ProductID   int64  `xorm:"not null default 0 INT(11) 'product_id'" json:"product_id"`
	MchID       string `xorm:"not null VARCHAR(64) 'mchid'" json:"mchid"`
	IsDel       int32  `xorm:"not null default 2 TINYINT(1) 'is_del'" json:"is_del"`
	CreateTime  int64  `xorm:"not null default 0 INT(11) 'create_time'" json:"create_time"`
	UpdateTime  int64  `xorm:"not null default 0 INT(11) 'update_time'" json:"update_time"`
}

func (MchIDAccountConfig) TableName() string {
	return "mchid_account_config"
}

type mchIDAccountConfig struct{}

var TbMchIDAccountConfig mchIDAccountConfig

// GetValidList 获取正在使用的商户账号
func (*mchIDAccountConfig) GetValidList() []*MchIDAccountConfig {
	var tables []*MchIDAccountConfig
	err := databases.GetEngine().Where("is_del = ?", library.No).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
