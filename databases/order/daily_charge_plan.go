package order

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
)

// 每日扣款计划
type DailyChargePlan struct {
	ID int64 `xorm:"not null pk autoincr INT(11) 'id'"`
	// 签约表iD
	SubID int64 `xorm:"not null int(10) 'sub_id'"`
	// 日期
	DateIndex string `xorm:"not null char(8) 'date_index'"`
	// 扣款成功or失败 1成功 2失败
	IsSuccess int `xorm:"not null tinyint(2) 'is_success'"`
	// 异常类型
	AbnormalType int `xorm:"not null tinyint(4) 'abnormal_type'"`
	// 支付宝错误原因
	ErrSubCode string `xorm:"not null varchar(128) 'err_sub_code'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (DailyChargePlan) TableName() string {
	return "daily_charge_plan"
}

// Save 保存数据
func (a *DailyChargePlan) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *DailyChargePlan) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

type dailyChargePlan struct{}

var TbDailyChargePlan dailyChargePlan

// GetPlanItem 获取扣款计划Item
func (*dailyChargePlan) GetPlanItem(dateIndex string, subID int64) *DailyChargePlan {
	var table DailyChargePlan
	ok, err := databases.GetEngine().Where("date_index = ? and sub_id = ?", dateIndex, subID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetPlanList 获取当天扣款计划
func (*dailyChargePlan) GetPlanList(dateIndex string) []*DailyChargePlan {
	tables := make([]*DailyChargePlan, 0)
	err := databases.GetEngine().Where("date_index = ?", dateIndex).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
