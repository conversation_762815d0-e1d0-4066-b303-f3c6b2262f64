package order

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/pay"
)

type Refund struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// UID
	UID int64 `xorm:"not null bigint(11) 'uid'"`
	// 订单ID
	OrderID string `xorm:"not null varchar(64) 'order_id'"`
	// 退款状态 1 待审核 2 已拒绝 3 退费中 4 退费成功 5 退费失败
	RefundStatus int `xorm:"not null tinyint(1) 'refund_status'"`
	// 退费金额
	RefundAmount float64 `xorm:"not null decimal(10,2) 'refund_amount'"`
	// 退款单号
	RefundTradeNo string `xorm:"not null varchar(72) 'refund_trade_no'"`
	// 退款时间
	RefundTime int64 `xorm:"not null int(11) 'refund_time'"`
	// 第三方返回信息
	Msg string `xorm:"not null varchar(5000) 'msg'"`
	// 退费原因
	ReasonType int `xorm:"not null tinyint(4) 'reason_type'"`
	// 退款失败返回原因
	ThirdFailMsg string `xorm:"not null varchar(255) 'third_fail_msg'"`
	// 备注
	Reason string `xorm:"not null varchar(500) 'reason'"`
	// 会员时长类型 1天2月3年
	RefundDurationType int `xorm:"not null tinyint(4) 'refund_duration_type'"`
	// 会员时长
	RefundDurationValue int `xorm:"not null int(6) 'refund_duration_value'"`
	// 提交人
	AdminName string `xorm:"not null varchar(64) 'admin_name'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
	// 是否解约，1是 2 否
	IsUnsubscribe int `xorm:"not null tinyint(1) 'unsubscribe'"`
	// 是否考核
	IsAssess int `xorm:"not null default 2 TINYINT(1) 'is_assess'" json:"is_assess"`
	// 是否全额挽回
	IsRetrieve int64 `xorm:"'is_retrieve'"`
	// 会话id
	TopicID int64 `xorm:"'topic_id'"`
	// 投诉订单
	TradeComplain int `xorm:"not null tinyint(1) 'trade_complain'"`
	RefundType    int `xorm:"not null tinyint(1) 'refund_type'"`
	// 挑战赛领取会员类型
	ChallengeVipType int64  `xorm:"not null tinyint(1) 'challenge_vip_type'"`
	OriginalEquity   string `xorm:"'original_equity'"`
	// 七鱼客服ID
	StaffID int64 `xorm:"not null int(11) 'staff_id'"`
	// 挽回类型 1接线挽回 2撤诉挽回 3外溢挽回
	RetrieveType int `xorm:"not null default 0 tinyint(4) 'retrieve_type'"`
}

// TableName 获取表名
func (Refund) TableName() string {
	return "order_refund"
}

// Save 保存数据
func (a *Refund) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *Refund) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

// UpdateByTran 事务更新数据
func (a *Refund) UpdateByTran(session *xorm.Session) error {
	a.UpdateTime = time.Now().Unix()
	_, err := session.ID(a.ID).Update(a)
	return err
}

type refund struct{}

var TbRefund refund

type SearchOrderReq struct {
	UID               int64  `json:"uid"`
	OrderID           string `json:"order_id"`
	CreateTimeStart   int64  `json:"create_time_start"`
	CreateTimeEnd     int64  `json:"create_time_end"`
	RefundTimeStart   int64  `json:"refund_time_start"`
	RefundTimeEnd     int64  `json:"refund_time_end"`
	OrderRefundStatus int    `json:"order_refund_status"`
	Page              int    `json:"page"`
	PageSize          int    `json:"page_size"`
}

// 获取退款单列表 GetRefundListByOrderID
func (*refund) GetRefundListByOrderID(orderID string) []*Refund {
	var tables []*Refund
	err := databases.GetEngine().Where("order_id = ? ", orderID).OrderBy("id asc").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetRefundByID 根据id获取退款单
func (*refund) GetRefundByID(id int64) *Refund {
	var table Refund
	ok, err := databases.GetEngine().Where("id = ? ", id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetRefundByRefundOrderID 根据退款订单号获取退款单
func (*refund) GetRefundByRefundOrderID(refundTradeNo string) *Refund {
	var table Refund
	ok, err := databases.GetEngine().Where("refund_trade_no = ? ", refundTradeNo).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*refund) GetLastRefundByOrderID(orderID string) *Refund {
	var table Refund
	ok, err := databases.GetEngine().Where("order_id = ? ", orderID).OrderBy("id DESC").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

type RefundOrderListItem struct {
	UID                 int64   `xorm:"'uid'"`
	OrderID             string  `xorm:"'order_id'"`
	CreateTime          int64   `xorm:"'create_time'"`
	PayTime             int64   `xorm:"'pay_time'"`
	ProductID           int64   `xorm:"'product_id'"`
	PayType             int     `xorm:"'pay_type'"`
	OrderAmount         string  `xorm:"'order_amount'"`
	RefundAmount        float64 `xorm:"'refund_amount'"`
	ReasonType          int     `xorm:"'reason_type'"`
	Reason              string  `xorm:"'reason'"`
	RefundTime          int64   `xorm:"'refund_time'"`
	RefundDurationType  int     `xorm:"'refund_duration_type'"`
	RefundDurationValue int     `xorm:"'refund_duration_value'"`
	OrderRefundStatus   int     `xorm:"'refund_status'"`
	AdminName           string  `xorm:"'admin_name'"`
	RefundID            int64   `xorm:"'refund_id'"`
	Mobile              string  `xorm:"'mobile'"`
	IsLogoff            int     `xorm:"'is_logoff'"`
}

func (refund) SearchOrder(req *SearchOrderReq) []*RefundOrderListItem {
	var tables []*RefundOrderListItem
	session := databases.GetEngine().NewSession()
	defer session.Close()
	session.Table("order_refund").Alias("or").
		Select("o.uid,o.order_id,o.create_time,o.pay_time,o.product_id,o.pay_type,o.order_amount,"+
			"or.refund_amount,or.refund_status,or.id as refund_id,or.reason_type,or.reason,or.refund_time,"+
			"or.refund_duration_type,or.refund_duration_value,or.admin_name,a.mobile,a.is_logoff").
		Join("LEFT", []string{"web_order", "o"}, "o.order_id = or.order_id").
		Join("LEFT", []string{"account", "a"}, "a.id = o.uid").
		Where("o.order_status = ?", pay.OrderStatus.Paid)
	if req.UID > 0 {
		session.And("o.uid = ?", req.UID)
	}
	if req.OrderID != "" {
		session.And("o.order_id = ?", req.OrderID)
	}
	if req.CreateTimeStart > 0 && req.CreateTimeEnd > 0 {
		session.And("o.create_time >= ? and o.create_time <= ?", req.CreateTimeStart, req.CreateTimeEnd)
	}
	if req.RefundTimeStart > 0 && req.RefundTimeEnd > 0 {
		session.And("or.refund_time >= ? and or.refund_time <= ?", req.RefundTimeStart, req.RefundTimeEnd)
	}
	if req.OrderRefundStatus > 0 {
		session.And("or.refund_status = ?", req.OrderRefundStatus)
	}
	if req.OrderRefundStatus == 0 {
		session.And("ISNULL(or.id)")
	}
	offset := (req.Page - 1) * req.PageSize
	session.OrderBy("or.id desc")
	session.Limit(req.PageSize, offset)
	err := session.Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// 获取退款单列表 GetRefundListByIDs
func (*refund) GetRefundListByIDs(ids []int64) []*Refund {
	var tables []*Refund
	err := databases.GetEngine().In("id", ids).OrderBy("id asc").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// 获取退款单列表 GetRefundListByUID
func (*refund) GetRefundListByUID(uid int64) []*Refund {
	var tables []*Refund
	err := databases.GetEngine().Where("uid = ?", uid).OrderBy("id asc").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (*refund) GetRefundListByOrderIDStatus(orderID string, refundStatus []int) []*Refund {
	var tables []*Refund
	err := databases.GetEngine().In("refund_status", refundStatus).
		And("order_id = ?", orderID).OrderBy("id asc").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (*refund) GetTradeComplainListByOrderID(orderID string) []*Refund {
	var tables []*Refund
	err := databases.GetEngine().Where("order_id = ? AND trade_complain = ?", orderID, library.Yes).
		OrderBy("id asc").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
