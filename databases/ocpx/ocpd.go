package ocpx

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	db "gitlab.dailyyoga.com.cn/server/children/databases"
)

type OCPD struct {
	ID                 int64  `xorm:"int(10) unsigned notnull pk autoincr 'id'"` // 主键
	TaskID             string `xorm:"varchar(255) notnull 'task_id'"`            // 任务id
	Channel            string `xorm:"varchar(255) notnull 'channel'"`            // 渠道
	CallBack           string `xorm:"varchar(255) notnull 'callback'"`           // 客户端callback
	DeviceID           string `xorm:"varchar(255) notnull 'device_id'"`          // 设备id
	DeviceIDType       int64  `xorm:"tinyint(3) notnull 'device_id_type'"`       // 设备id类型
	ActionTime         int64  `xorm:"int(10) notnull 'action_time'"`             // 上报时间
	ActionType         int64  `xorm:"tinyint(3) notnull 'action_type'"`          // 上报类型
	PushStatus         int64  `xorm:"tinyint(3) notnull 'push_status'"`          // 推送状态
	PushFinishTime     int64  `xorm:"int(10) notnull 'push_finish_time'"`        // 推送完成时间
	AccessToken        string `xorm:"varchar(255) notnull 'access_token'"`       // token
	AccessTokenExpires int64  `xorm:"int(10) notnull 'access_token_expires'"`    // token有效时间
	UID                int64  `xorm:"not null bigint(20) 'uid'" json:"uid"`
	ChannelMe          int64  `xorm:"not null int(10) 'channel_me'" json:"channel_me"`
	UpdateTime         int64  `xorm:"int(10) notnull 'update_time'"` // 修改时间
	CreateTime         int64  `xorm:"int(10) notnull 'create_time'"` // 创建时间
}

type ocpd struct{}

var TbOCPD ocpd

func (r *OCPD) TableName() string {
	return "huawei_ocpd_push"
}

// Insert 插入数据
func (r *ocpd) Insert(o *OCPD) (int64, error) {
	client := db.GetEngineMaster().NewSession()
	defer client.Close()
	_, err := client.
		Table("huawei_ocpd_push").
		Insert(o)
	if err != nil {
		return 0, err
	}
	// 坑 没有lastId 用个比较蠢的办法吧
	var table OCPD
	_, SearchErr := client.
		Table("huawei_ocpd_push").
		Where("action_time=?", o.ActionTime).
		Get(&table)
	return table.ID, SearchErr
}

// Save 修改
func (r *ocpd) Save(id int64, o *OCPD) (int64, error) {
	return db.GetEngineMaster().
		Table("huawei_ocpd_push").
		Where("id=?", id).
		Update(o)
}

func (r *OCPD) Update() error {
	r.UpdateTime = time.Now().Unix()
	_, err := db.GetEngineMaster().ID(r.ID).Update(r)
	return err
}

// GetItemByID 获取信息
func (r *ocpd) GetItemByID(id int64) *OCPD {
	var table OCPD
	ok, err := db.GetEngine().ID(id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (r *ocpd) GetItemByUID(uid int64) *OCPD {
	var table OCPD
	ok, err := db.GetEngine().Where("`uid` = ? ", uid).
		Desc("id").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
