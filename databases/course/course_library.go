package course

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libcourse "gitlab.dailyyoga.com.cn/server/children/library/course"
)

// DBCourse 课程库
type DBCourse struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 课程标题
	Title string `xorm:"not null varchar(255) 'title'"`
	// 课程描述
	Desc string `xorm:"not null varchar(255) 'desc'"`
	// 课程视频地址
	VideoURL string `xorm:"not null varchar(255) 'video_url'"`
	// 课程封面
	CoverURL string `xorm:"not null varchar(255) 'cover_url'"`
	// 课程横版封面
	HorizontalCoverURL string `xorm:"not null varchar(255) 'horizontal_cover_url'"`
	// 课程时长
	Duration float64 `xorm:"not null decimal(10,3) 'duration'"`
	// 是否VIP
	IsVIP int32 `xorm:"not null tinyint(1) 'is_vip'"`
	// 是否在线
	IsOnline int32 `xorm:"not null tinyint(1) 'is_online'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
	// 练习次数
	PracticeNum int64 `xorm:"not null int(11) 'practice_num'"`
	// 1 横屏 2 竖屏
	IsHorizontal int32 `xorm:"not null tinyint(1) 'is_horizontal'"`
	// 自定义标签
	CustomLabels string `xorm:"not null varchar(256) 'custom_labels'"`
	// 教练ID
	Coach int64 `xorm:"not null int(11) 'coach'"`
	// 注意事项
	Precautions string `xorm:"not null varchar(256) 'precautions'"`
	// 是否新课程
	IsNewCourse int32 `xorm:"not null tinyint(1) 'is_new_course'"`
	// 新课程更新时间
	NewCourseTime int64 `xorm:"not null int(11) 'new_course_time'"`
}

// TableName 获取表名
func (DBCourse) TableName() string {
	return "course_library"
}

// Save 保存数据
func (c *DBCourse) Save() error {
	c.CreateTime = time.Now().Unix()
	c.UpdateTime = c.CreateTime

	_, err := databases.GetEngineMaster().Insert(c)
	return err
}

// Update 更新数据
func (c *DBCourse) Update() error {
	c.UpdateTime = time.Now().Unix()

	_, err := databases.GetEngineMaster().ID(c.ID).Update(c)
	return err
}

type clibrary struct {
}

var TbCourseLibrary clibrary

// GetItem 获取课程详情
func (s *clibrary) GetItem(courseID int64) *DBCourse {
	var table DBCourse
	ok, err := databases.GetEngine().Where("id = ?", courseID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetItemByName 获取课程详情
func (s *clibrary) GetListByTrainNowID(trainNowID string) []*DBCourse {
	var tables []*DBCourse

	err := databases.GetEngine().Where("train_now_id = ? and is_online = ?", trainNowID, library.Yes).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

type QueryParam struct {
	IDs      []int64
	IsVIP    int
	Levels   []libcourse.LevelInt
	IsOnline int32
	PageSize int
	Page     int
}

func (s *clibrary) GetListByQuery(query *QueryParam) []*DBCourse {
	var tables []*DBCourse
	session := databases.GetEngine().NewSession()
	defer session.Close()
	session.Where("is_online = ?", library.Yes)
	if len(query.IDs) > 0 {
		session.In("id", query.IDs)
	}
	if len(query.Levels) > 0 {
		session.In("level", query.Levels)
	}
	if query.IsVIP > 0 {
		session.And("is_vip = ?", query.IsVIP)
	}
	if query.IsOnline > 0 {
		session.And("is_online = ?", query.IsOnline)
	}
	if query.Page > 0 && query.PageSize > 0 {
		offset := (query.Page - 1) * query.PageSize
		session.Limit(query.PageSize, offset)
	}
	err := session.Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (s *clibrary) CheckCourseIds(ids []int64) bool {
	course := new(DBCourse)
	session := databases.GetEngine().NewSession()
	defer session.Close()
	count, err := session.In("id", ids).Where("is_online = ?", library.Yes).Count(course)
	if err != nil {
		logger.Error(err)
		return false
	}
	if count != int64(len(ids)) {
		return false
	}
	return true
}

func (s *clibrary) GetListByDuration(start, end int) []*DBCourse {
	var tables []*DBCourse
	err := databases.GetEngine().Where("duration > ? and duration <= ? and is_online = ?",
		start, end, library.Yes).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (s *clibrary) GetListByLevel(level int) []*DBCourse {
	var tables []*DBCourse
	err := databases.GetEngine().Where("level = ? and is_online = ?", level, library.Yes).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (s *clibrary) GetListByIsNeedApparatus(apparatus int) []*DBCourse {
	var tables []*DBCourse
	err := databases.GetEngine().Where("is_need_apparatus = ? and is_online = ?", apparatus, library.Yes).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (s *clibrary) GetListByIDArr(idArr []int64) []*DBCourse {
	var tables []*DBCourse
	err := databases.GetEngine().In("id", idArr).And("is_online = ?", library.Yes).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (s *clibrary) GetListByIDArrNotImport(idArr []int64) []*DBCourse {
	var tables []*DBCourse
	err := databases.GetEngine().In("id", idArr).And("is_online = ?",
		library.Yes).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (s *clibrary) GetListAll() []*DBCourse {
	var tables []*DBCourse
	err := databases.GetEngine().Where("is_online = ?", library.Yes).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (s *clibrary) GetCourseIDsByQuery(params *QueryParam) (courseIDs []int64) {
	query := databases.GetEngine().NewSession()
	defer query.Close()
	query.Table("course_library").Distinct("id")
	query.Where("is_online = ?", library.Yes)
	// 屏蔽跑步标签
	query.Where("`custom_labels` NOT LIKE ?", "%"+library.Target+"%")
	err := query.Find(&courseIDs)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return courseIDs
}

type Label struct {
	CourseID        int64 `xorm:"'course_id'" json:"course_id"` // 课程ID
	LabelID         int64 `xorm:"'label_id'" json:"label_id"`   // 标签ID
	ValAddType      int   `xorm:"not null int(11) 'val_add_type'"`
	ShieldObProgram int   `xorm:"not null int(11) 'shield_ob_program'"`
}

func (s *clibrary) GetListByRecommendID(recommendID string) []*DBCourse {
	var tables []*DBCourse
	err := databases.GetEngine().Where("recommend_id = ? and is_online = ?",
		recommendID, library.Yes).OrderBy("id desc").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (s *clibrary) GetListByValAddType(valAddType int) []*DBCourse {
	var tables []*DBCourse
	err := databases.GetEngine().Where("val_add_type = ?  and is_online = ?",
		valAddType, library.Yes).OrderBy("id desc").Limit(library.Const20).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (s *clibrary) GetListByTrialAction() []*DBCourse {
	var tables []*DBCourse
	err := databases.GetEngine().Where("is_online = ? and trial_action_1 > 0 and trial_action_2 > 0",
		library.Yes).OrderBy("id desc").Limit(library.Const20).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetListByIDs 根据id批量查询，按val_add_type升序,id降序排序
func (s *clibrary) GetListByIDs(ids []int64, optionCourse *libcourse.OptionCourse) []*DBCourse {
	var tables []*DBCourse
	session := databases.GetEngine().NewSession()
	defer session.Close()
	session.In("id", ids)
	if optionCourse.IsOnline > 0 {
		session.And("is_online = ?", optionCourse.IsOnline)
	}
	if optionCourse.Page > 0 && optionCourse.PageSize > 0 {
		offset := (optionCourse.Page - 1) * optionCourse.PageSize
		session.Limit(optionCourse.PageSize, offset)
	}
	err := session.Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
