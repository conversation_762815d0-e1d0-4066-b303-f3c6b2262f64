package course

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libc "gitlab.dailyyoga.com.cn/server/children/library/course"
)

// LabelResource 标签资源列表
type LabelResource struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 资源ID
	ResourceType int64 `xorm:"not null tinyint(4) 'resource_type'"`
	// 资源ID
	ResourceID int64 `xorm:"not null int(11) 'resource_id'"`
	// 标签ID
	LabelID int64 `xorm:"not null int(11) 'label_id'"`
	// 是否删除 1 删除 2 否
	IsDel int32 `xorm:"not null tinyint(1) 'is_del'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
}

// TableName 获取表名
func (LabelResource) TableName() string {
	return "label_resource"
}

// Save 保存数据
func (l *LabelResource) Save() error {
	l.CreateTime = time.Now().Unix()
	l.UpdateTime = l.CreateTime

	_, err := databases.GetEngineMaster().Insert(l)
	return err
}

// Update 更新数据
func (l *LabelResource) Update() error {
	l.UpdateTime = time.Now().Unix()

	_, err := databases.GetEngineMaster().ID(l.ID).Update(l)
	return err
}

// SaveByTran 保存数据
func (l *LabelResource) SaveByTran(session *xorm.Session) error {
	l.CreateTime = time.Now().Unix()
	l.UpdateTime = l.CreateTime

	_, err := session.Insert(l)
	return err
}

type labelResource struct{}

// TbLabelResource 外部引用对象
var TbLabelResource labelResource

// GetListByResourceID 获取资源ID对应的标签ID
func (l *labelResource) GetListByResourceID(resourceID int64, resourceType libc.ResourceTypeInt) []*LabelResource {
	var tables []*LabelResource

	err := databases.GetEngine().Where("resource_id = ? and resource_type = ? and is_del = ?",
		resourceID, resourceType, library.No).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetListByResourceIDArr 获取资源ID列表对应的标签ID
func (l *labelResource) GetListByResourceIDArr(resourceIDArr []int64,
	resourceType libc.ResourceTypeInt) []*LabelResource {
	var tables []*LabelResource
	err := databases.GetEngine().In("resource_id", resourceIDArr).And("resource_type = ? and is_del = ?",
		resourceType, library.No).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetListByResourceID 获取资源ID对应的标签ID
func (l *labelResource) GetList(resourceID int64, resourceType libc.ResourceTypeInt) []*LabelResource {
	var tables []*LabelResource

	err := databases.GetEngine().Where("resource_id = ? and resource_type = ? and is_del = ?",
		resourceID, resourceType, library.No).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetListByLabelID 根据标签ID获取资源
func (l *labelResource) GetListByLabelID(labelID int64, resourceType libc.ResourceTypeInt) []*LabelResource {
	var tables []*LabelResource

	err := databases.GetEngine().Where("label_id = ? and resource_type = ? and is_del = ?",
		labelID, resourceType, library.No).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetListByLabelIDArr 根据标签ID获取资源
func (l *labelResource) GetListByLabelIDArr(labelIDArr []int64, resourceType libc.ResourceTypeInt) []*LabelResource {
	var tables []*LabelResource

	err := databases.GetEngine().In("label_id", labelIDArr).And("resource_type = ? and is_del = ?",
		resourceType, library.No).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
