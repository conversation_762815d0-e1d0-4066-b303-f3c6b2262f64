package course

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
)

type Recommend struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 动作标题
	Title string `xorm:"not null varchar(255) 'title'"`
	// 配置
	Conf string `xorm:"not null varchar(2048) 'conf'"`
	// 是否在线
	IsOnline int `xorm:"not null tinyint(1) 'is_online'"`
	// 是否删除 1 删除 2 否
	IsDel int32 `xorm:"not null tinyint(1) 'is_del'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (Recommend) TableName() string {
	return "course_recommend"
}

// Save 保存数据
func (r *Recommend) Save() error {
	r.CreateTime = time.Now().Unix()
	r.UpdateTime = r.CreateTime
	_, err := databases.GetEngineMaster().Insert(r)
	return err
}

// Update 更新数据
func (r *Recommend) Update() error {
	r.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(r.ID).Update(r)
	return err
}

type recommend struct{}

var TbRecommend recommend

func (r *recommend) GetItemByID(id int64) *Recommend {
	var table Recommend
	ok, err := databases.GetEngine().Where("id = ?", id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (r *recommend) List(page, pageSize int) []*Recommend {
	var tables []*Recommend
	offset := (page - 1) * pageSize
	table := databases.GetEngine().Where("is_del = ?", library.No)
	err := table.Desc("id").Limit(pageSize, offset).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (r *recommend) GetLastItem() *Recommend {
	var table Recommend
	ok, err := databases.GetEngine().Where("is_del = ? and is_online = ?", library.No, library.Yes).
		OrderBy("id desc").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
