package course

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
)

// LabelConfig 标签配置列表
type LabelConfig struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 父级ID
	Pid int64 `xorm:"not null int(11) 'pid'"`
	// 层级
	Level int32 `xorm:"not null tinyint(1) 'level'"`
	// 标签名称
	Title string `xorm:"not null varchar(255) 'title'"`
	// 图标
	IconList string `xorm:"not null varchar(5120) 'icon_list'"`
	// 标签名称
	SubTitle string `xorm:"not null varchar(255) 'sub_title'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (LabelConfig) TableName() string {
	return "label_config"
}

// Save 保存数据
func (l *LabelConfig) Save() error {
	l.CreateTime = time.Now().Unix()
	l.UpdateTime = l.CreateTime

	_, err := databases.GetEngineMaster().Insert(l)
	return err
}

// Update 更新数据
func (l *LabelConfig) Update() error {
	l.UpdateTime = time.Now().Unix()

	_, err := databases.GetEngineMaster().ID(l.ID).Update(l)
	return err
}

type labelConfig struct{}

// TbLabelConfig 外部引用对象
var TbLabelConfig labelConfig

// GetItemByID 通过标签ID获取标签列表
func (l *labelConfig) GetItemByID(labelID int64) *LabelConfig {
	var table LabelConfig
	ok, err := databases.GetEngine().ID(labelID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetListByIDList 通过标签ID获取标签列表
func (l *labelConfig) GetListByIDList(labelIDs []int) []LabelConfig {
	var tables []LabelConfig

	err := databases.GetEngine().In("id", labelIDs).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetListByPIDList 通过父标签ID获取标签列表
func (l *labelConfig) GetListByPIDList(pid []int64) []*LabelConfig {
	var tables []*LabelConfig
	err := databases.GetEngine().In("pid", pid).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetList 获取标签列表
func (l *labelConfig) GetList() []*LabelConfig {
	var tables []*LabelConfig
	err := databases.GetEngine().Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
