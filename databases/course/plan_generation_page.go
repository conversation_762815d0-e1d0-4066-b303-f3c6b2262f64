package course

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
)

type PagePlanGeneration struct {
	ID              int64  `xorm:"pk autoincr not null comment('自增id') int(11) 'id'"`
	ImageInfo       string `xorm:"not null comment('图片信息json') text 'image_info'"`
	CreateTime      int64  `xorm:"not null comment('创建时间') int(11) 'create_time'"`
	UpdateTime      int64  `xorm:"not null comment('状态修改时间') int(11) 'update_time'"`
	BackgroundColor string `xorm:"varchar(20) default '' not null 'background_color'"`
	IsDel           int64  `xorm:"not null default 0 comment('状态:1-删除;2-正常') tinyint(1) 'is_del'"`
}

func (p *PagePlanGeneration) TableName() string {
	return "page_plan_generation"
}

type pagePlanGeneration struct{}

var TbPlanPageGeneration pagePlanGeneration

func (c *pagePlanGeneration) GetByID(id int64) *PagePlanGeneration {
	var table PagePlanGeneration
	ok, err := databases.GetEngine().Where("id = ?", id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
