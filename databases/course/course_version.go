package course

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
)

// CVersion 课程版本
type DBCourseVersion struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 课程id
	CourseID int64 `xorm:"not null int(11) 'course_id'"`
	// 版本id
	VersionID int64 `xorm:"not null int(11) 'version_id'"`
	// 课程视频地址
	VideoURL string `xorm:"not null varchar(255) 'video_url'"`
	// 课程时长
	Duration float64 `xorm:"not null decimal(10,3) 'duration'"`
	// 音色
	VoicePackage string `xorm:"not null varchar(512) 'voice_package'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (DBCourseVersion) TableName() string {
	return "course_version"
}

// Save 保存数据
func (c *DBCourseVersion) Save() error {
	c.CreateTime = time.Now().Unix()
	c.UpdateTime = c.CreateTime

	_, err := databases.GetEngineMaster().Insert(c)
	return err
}

// Update 更新数据
func (c *DBCourseVersion) Update() error {
	c.UpdateTime = time.Now().Unix()

	_, err := databases.GetEngineMaster().ID(c.ID).Update(c)
	return err
}

type cversion struct {
}

var TbCVersion cversion

// GetItem 获取版本信息
func (s *cversion) GetItem(courseID, versionID int64, voicePackage string) *DBCourseVersion {
	var table DBCourseVersion
	ok, err := databases.GetEngine().Where("course_id = ? and version_id = ? and voice_package = ?",
		courseID, versionID, voicePackage).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (s *cversion) GetItemByVoicePackage(courseID int64, voicePackage string) *DBCourseVersion {
	var table DBCourseVersion
	ok, err := databases.GetEngine().Where("course_id = ? and voice_package = ?",
		courseID, voicePackage).OrderBy("id desc").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
