package conf

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
)

type ObProcessResource struct {
	ID               int64  `xorm:"not null autoincr pk int(11) unsigned 'id'" json:"id"`        // 主键
	Name             string `xorm:"not null varchar(64) 'name'" json:"name"`                     // 流程名称
	PageList         string `xorm:"not null text  'page_list'" json:"page_list"`                 // 图片编号顺序json
	TransitionVideos string `xorm:"not null text  'transition_videos'" json:"transition_videos"` // 图片编号顺序json
	IsDel            int32  `xorm:"not null tinyint(1) 'is_del'" json:"is_del"`                  // 是否删除 1是 2否
	CreateTime       int64  `xorm:"not null int(11) 'create_time'" json:"create_time"`           // 创建时间
	UpdateTime       int64  `xorm:"not null int(11) 'update_time'" json:"update_time"`           // 更新时间
	PlanPageID       int64  `xorm:"not null default 0 int(11) 'plan_page_id'"`                   // 计划生成页
	ObType           int    `xorm:"not null default 1 int(11) 'ob_type'"`                        // ob 类型
}

func (o *ObProcessResource) TableName() string {
	return "ob_process_resource"
}

type obconf struct {
}

var TbObProcessResource obconf

func (*obconf) GetItemByID(id int64) *ObProcessResource {
	var table ObProcessResource
	ok, err := databases.GetEngine().ID(id).Where("is_del = ?", library.No).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
