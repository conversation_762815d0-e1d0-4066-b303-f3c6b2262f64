package admin

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
)

type TradeComplain struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// app_id
	AppID string `xorm:"not null comment('app_id') varchar(255) 'app_id'"`
	// 投诉id
	ComplaintID string `xorm:"not null comment('投诉id') varchar(255) 'complaint_id'"`
	// 投诉状态
	ComplaintStatus string `xorm:"not null comment('投诉状态') varchar(255) 'complaint_status'"`
	// 订单id
	OrderID string `xorm:"not null comment('订单id') varchar(255) 'order_id'"`
	// 1:瑜伽,2:热汗,3:硬汉
	Project int `xorm:"not null default 1 comment('1:瑜伽,2:热汗,3:硬汉') tinyint(3) 'project'"`
	// 1:微信,2:支付宝
	PayType int `xorm:"not null default 1 comment('1:微信,2:支付宝') tinyint(3) 'pay_type'"`
	// 返回内容
	Msg string `xorm:"not null comment('返回内容') varchar(5000) 'msg'"`
	// 处理状态 0:待处理,1:已处理
	ProcessStatus int `xorm:"not null default 0 comment('0:待处理,1:已处理') tinyint(3) 'process_status'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

func (tc *TradeComplain) TableName() string {
	return "trade_complain"
}

// Save 保存数据
func (tc *TradeComplain) Save() error {
	tc.CreateTime = time.Now().Unix()
	tc.UpdateTime = tc.CreateTime
	_, err := databases.GetYoGaCsMaster().Insert(tc)
	return err
}

// Update 更新数据
func (tc *TradeComplain) Update() error {
	tc.UpdateTime = time.Now().Unix()
	_, err := databases.GetYoGaCsMaster().ID(tc.ID).Update(tc)
	return err
}

type tradeComplain struct{}

// TbTradeComplain 外部引用对象
var TbTradeComplain tradeComplain

// GetItemByComplaintID 通过
func (tc *tradeComplain) GetItemByComplaintID(complaintID string) *TradeComplain {
	var table TradeComplain
	ok, err := databases.GetYoGaCs().Where("complaint_id = ?", complaintID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (tc *tradeComplain) GetItemByOrderID(orderID string) *TradeComplain {
	var table TradeComplain
	ok, err := databases.GetYoGaCs().Where("order_id = ?", orderID).OrderBy("id desc").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (tc *tradeComplain) GetItemList(startTime, endTime int64) []*TradeComplain {
	table := make([]*TradeComplain, 0)
	err := databases.GetYoGaCs().Where("update_time >= ? and update_time <= ? and process_status = ?",
		startTime, endTime, library.Yes).OrderBy("id desc").Find(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return table
}
