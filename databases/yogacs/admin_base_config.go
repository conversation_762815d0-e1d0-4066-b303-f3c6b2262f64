package admin

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
)

type BaseConfig struct {
	ID         int64  `xorm:"pk autoincr not null comment('主键ID') int(11) 'id'"`
	Project    int32  `xorm:"not null default 1 comment('1:瑜伽,2:热汗,3:硬汉') tinyint(3) 'project'"`
	Key        string `xorm:"not null comment('配置项') varchar(255) 'key'"`
	Value      string `xorm:"not null comment('配置内容') varchar(5000) 'value'"`
	CreateTime int64  `xorm:"not null default 0 comment('创建时间') int(11) 'create_time'"`
	UpdateTime int64  `xorm:"not null default 0 comment('更新时间') int(11) 'update_time'"`
}

func (a *BaseConfig) TableName() string {
	return "admin_base_config"
}

// Save 保存数据
func (a *BaseConfig) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetYoGaCsMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *BaseConfig) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetYoGaCsMaster().ID(a.ID).Update(a)
	return err
}

type baseConfig struct{}

// TbBaseConfig 外部引用对象
var TbBaseConfig baseConfig

// GetItemByKey 通过
func (a *baseConfig) GetItemByKey(project int32, key string) *BaseConfig {
	var table BaseConfig
	ok, err := databases.GetYoGaCs().Where("`project` = ? AND `key` = ?", project, key).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
