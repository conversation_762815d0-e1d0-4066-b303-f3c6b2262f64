package collect

import (
	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	libcollect "gitlab.dailyyoga.com.cn/server/children/library/collect"
)

type Collect struct {
	ID           uint64 `xorm:"pk autoincr not null comment('收藏自增ID') bigint(20) 'id'"`
	UID          int64  `xorm:"not null bigint(20) 'uid'"`
	ResourceID   int64  `xorm:"not null comment('资源ID') bigint(20) 'resource_id'"`
	ResourceType int    `xorm:"not null default 1 comment('源分类，1为course_library表') tinyint(2) 'resource_type'"`
	CreateTime   int64  `xorm:"not null default 0 comment('创建时间') int(11) 'create_time'"`
}

func (c *Collect) TableName() string {
	return "collect"
}

type collect struct {
}

var TbCollect collect

func (c *collect) BatchInsertByTran(session *xorm.Session, collects []*Collect) error {
	_, err := session.Insert(&collects)
	return err
}

func (c *collect) BatchDelByTran(session *xorm.Session, uid int64, resourceType int, resourceID []int64) error {
	table := &Collect{}
	_, err := session.Where("uid=? AND resource_type=?", uid, resourceType).
		In("resource_id", resourceID).Delete(table)
	return err
}

func (c *collect) GetList(uid int64, resourceType, page, pageSize int) []*Collect {
	list := make([]*Collect, 0)
	session := databases.GetEngine().NewSession()
	defer session.Close()
	session.Cols("resource_id").Where("uid = ? And resource_type=?", uid, resourceType)
	offset := (page - 1) * pageSize
	err := session.Limit(pageSize, offset).Desc("id").Find(&list)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return list
}

func (c *collect) GetListByPage(uid int64, page, pageSize int) []*Collect {
	list := make([]*Collect, 0)
	session := databases.GetEngine().NewSession()
	defer session.Close()
	session.Where("uid = ?", uid)
	offset := (page - 1) * pageSize
	err := session.Limit(pageSize, offset).Desc("id").Find(&list)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return list
}

func (c *collect) GetListAll(uid int64, resourceType int) []*Collect {
	list := make([]*Collect, 0)
	session := databases.GetEngine().NewSession()
	defer session.Close()
	session.Cols("resource_id").Where("uid = ? And resource_type=?", uid, resourceType)
	err := session.Desc("id").Find(&list)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return list
}

func (c *collect) GetListAllSession(session *xorm.Session, uid int64, resourceType int) []*Collect {
	list := make([]*Collect, 0)
	session.Cols("resource_id").Where("uid = ? And resource_type=?", uid, resourceType)
	err := session.Desc("id").Find(&list)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return list
}

func (c *collect) IsCollected(uid int64, resourceType int, resourceID int64) bool {
	table := &Collect{}
	session := databases.GetEngine().NewSession()
	defer session.Close()
	count, err := session.Where("uid=? AND resource_type=? AND resource_id=?", uid, resourceType, resourceID).Count(table)
	if err != nil {
		return false
	}
	return count > 0
}

func (c *collect) IsCollectedByIds(uid int64, resourceType int, resourceIds []int64) bool {
	table := &Collect{}
	session := databases.GetEngine().NewSession()
	defer session.Close()
	count, err := session.Where("uid=? AND resource_type=?", uid, resourceType).In("resource_id", resourceIds).Count(table)
	if err != nil {
		return false
	}
	return count == int64(len(resourceIds))
}

func (c *collect) GetUserCollectCourse(uid int64) []int64 {
	res := make([]int64, 0)
	if uid == 0 {
		return res
	}
	tables := make([]*Collect, 0)
	err := databases.GetEngine().Where("uid = ? and resource_type = ?",
		uid, libcollect.ResourceTypeEnum.Course).Find(&tables)
	if err != nil {
		return res
	}
	if len(tables) == 0 {
		return res
	}
	for _, v := range tables {
		res = append(res, v.ResourceID)
	}
	return res
}
