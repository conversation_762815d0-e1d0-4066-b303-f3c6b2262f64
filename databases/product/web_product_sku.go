package product

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	db "gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/client"
)

type WebProductSku struct {
	ID                     int64   `xorm:"not null autoincr pk int(11) 'id'" json:"id"`                   // 主键
	TerminalType           int32   `xorm:"not null tinyint(4) 'terminal_type'"`                           // 终端类型1安卓 2IOS
	SkuID                  int64   `xorm:"not null int(11) 'sku_id'" json:"sku_id"`                       // 会员产品主表ID
	GroupID                int64   `xorm:"not null int(11) 'group_id'" json:"group_id"`                   // 分群ID
	Price                  float64 `xorm:"not null decimal(18,2) 'price'" json:"price"`                   // 价格
	Name                   string  `xorm:"not null char(128) 'name'" json:"name"`                         // 会员产品名称
	SubTitle               string  `xorm:"varchar(64) 'sub_title'" json:"sub_title"`                      // 副标题
	IntroduceText          string  `xorm:"varchar(256) 'introduce_text'" json:"introduce_text"`           // 介绍文案
	PhoneBuyButtonImg      string  `xorm:"varchar(255) " json:"phone_buy_button_img"`                     // phone购买按钮图片
	PadBuyButtonImg        string  `xorm:"varchar(255) 'pad_buy_button_img'" json:"pad_buy_button_img"`   // pad购买按钮图片
	OperateImg             string  `xorm:"varchar(255) 'operate_img'" json:"operate_img"`                 // sku运营图片
	Order                  int     `xorm:"tinyint(3) 'order'" json:"order"`                               // sku排序
	SubscriptImg           string  `xorm:"varchar(255) 'subscript_img'" json:"subscript_img"`             // sku角标图片
	SelectBottomImg        string  `xorm:"varchar(255) 'select_bottom_img'" json:"select_bottom_img"`     // sku选中底图
	NoSelectBottomImg      string  `xorm:"varchar(255) 'noselect_bottom_img'" json:"noselect_bottom_img"` // sku未选中底图
	SelectOfferBottomImg   string  `xorm:"varchar(255) 'select_offer_bottom_img'"`                        // sku选中优惠底图
	NoSelectOfferBottomImg string  `xorm:"varchar(255) 'noselect_offer_bottom_img'"`                      // sku未选中优惠底图
	OperateType            int     `xorm:"tinyint(3) 'operate_type'" json:"operate_type"`                 // 运营类型 1小条 2倒计时
	OperateTitleImg        string  `xorm:"varchar(255) 'operate_title_img'" json:"operate_title_img"`     // sku运营小条图片
	CountdownType          int     `xorm:"tinyint(3) 'countdown_type'" json:"countdown_type"`             // 倒计时单位
	CountdownValue         int     `xorm:"int(11) 'countdown_value'" json:"countdown_value"`              // 倒计时值
	IsDel                  int     `xorm:"tinyint(1) 'is_del'" json:"is_del"`                             // 状态:1-删除;2-正常
	CreateTime             int64   `xorm:"int(11) 'create_time'" json:"create_time"`                      // 创建时间
	UpdateTime             int64   `xorm:"int(11) 'update_time'" json:"update_time"`                      // 更新时间
}

// TableName 获取表名
func (WebProductSku) TableName() string {
	return "web_product_sku"
}

// Save 保存数据
func (w *WebProductSku) Save() error {
	w.CreateTime = time.Now().Unix()
	w.UpdateTime = w.CreateTime
	_, err := db.GetEngineMaster().Insert(w)
	return err
}

// Update 更新数据
func (w *WebProductSku) Update() error {
	w.UpdateTime = time.Now().Unix()
	_, err := db.GetEngineMaster().ID(w.ID).Update(w)
	return err
}

// TbWebProductSku 外部引用对象
var TbWebProductSku WebProductSku

// GetItemByID 通过ID获取详情
func (w *WebProductSku) GetItemByID(id int64) *WebProductSku {
	var table WebProductSku
	ok, err := db.GetEngine().Where("id = ?", id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetList 获取商品列表
func (w *WebProductSku) GetList(groupIDs []int64, terminalType int) []*WebProductSku {
	if terminalType == int(client.DeviceTypeEnum.Harmony) {
		terminalType = int(client.DeviceTypeEnum.Android)
	}
	var tables []*WebProductSku
	query := db.GetEngine().
		Where("is_del = ?", library.No).
		Where("(terminal_type = 99 or terminal_type = ?)", terminalType).
		In("group_id", groupIDs)
	err := query.Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
