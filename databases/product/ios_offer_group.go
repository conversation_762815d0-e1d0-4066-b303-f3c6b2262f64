package product

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	df "gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
)

// ios 分组
type IosOfferGroup struct {
	ID         int64  `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	GroupName  string `xorm:"comment('分组名称') not null VARCHAR(64) 'group_name'" json:"group_name"`
	SkuList    string `xorm:"comment('sku list') not null VARCHAR(1024) 'sku_list'" json:"sku_list"`
	IsDel      int32  `xorm:"not null default 2 TINYINT(1) 'is_del'" json:"is_del"`
	CreateTime int64  `xorm:"not null INT(11) 'create_time'" json:"create_time"`
	UpdateTime int64  `xorm:"not null INT(11) 'update_time'" json:"update_time"`
}

func (*IosOfferGroup) TableName() string {
	return "ios_offer_group"
}

// Save 保存数据
func (a *IosOfferGroup) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := df.GetEngineMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *IosOfferGroup) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := df.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

type iosOfferGroup struct{}

var TbIosOfferGroup iosOfferGroup

func (a *iosOfferGroup) GetList() []*IosOfferGroup {
	var tables []*IosOfferGroup
	err := df.GetEngine().Where("is_del = ?", library.No).Find(&tables)
	if err != nil {
		logger.Error("查询配置失败", err)
		return nil
	}
	return tables
}

func (a *iosOfferGroup) GetItemByID(id int64) *IosOfferGroup {
	var table IosOfferGroup
	ok, err := df.GetEngine().Where("id = ?", id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
