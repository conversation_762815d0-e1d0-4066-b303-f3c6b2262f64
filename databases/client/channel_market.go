package client

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
)

type ChannelMarket struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'" json:"id"`
	// 渠道
	Channel string `xorm:"not null varchar(64) 'channel'" json:"channel"`
	// OAID
	OaID string `xorm:"not null varchar(255) 'oaid'" json:"oaid"`
	// 神策匿名id
	AnonymousID string `xorm:"not null varchar(255) 'anonymous_id'" json:"anonymous_id"`
	// 包名
	PkgName string `xorm:"not null varchar(255) 'pkg_name'" json:"pkg_name"`
	// UID
	UID int64 `xorm:"not null bigint(20) 'uid'" json:"uid"`
	// 是否是广告来源，如果是，不上报oppo
	IsAdSource int `xorm:"not null tinyint(1) 'is_ad_source'" json:"is_ad_source"`
	// 首次购买时间，七天内上报，否则不上报
	FirstBuyTime int64 `xorm:"not null int(11) 'first_buy_time'" json:"first_buy_time"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'" json:"create_time"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'" json:"update_time"`
}

// TableName 获取表名
func (ChannelMarket) TableName() string {
	return "channel_market"
}

// Save 保存数据
func (c *ChannelMarket) Save() error {
	c.CreateTime = time.Now().Unix()
	c.UpdateTime = c.CreateTime
	_, err := databases.GetEngineMaster().Insert(c)
	return err
}

// Update 更新数据
func (c *ChannelMarket) Update() error {
	c.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(c.ID).Update(c)
	return err
}

type market struct{}

var TbMarket market

// GetItemByOaID 获取激活信息
func (market) GetItemByAnonymousID(oaid, channel string) *ChannelMarket {
	var table ChannelMarket
	ok, err := databases.GetEngine().Where("`anonymous_id` = ? AND `channel` = ?", oaid, channel).
		Desc("id").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetItemByUID 获取激活信息
func (market) GetItemByUID(uid int64) *ChannelMarket {
	var table ChannelMarket
	ok, err := databases.GetEngine().Where("`uid` = ? ", uid).
		Desc("id").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
