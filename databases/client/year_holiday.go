package client

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
)

type YearHoliday struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'" json:"id"`
	// 标题
	Year string `xorm:"not null char(4) 'year'" json:"year"`
	// 日期
	DateIndex string `xorm:"not null char(8) 'date_index'" json:"date_index"`
	// 日期类型 1 非周末法定假日 2 周末调休
	DateType int32 `xorm:"not null tinyint(1) 'date_type'" json:"date_type"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'" json:"create_time"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'" json:"update_time"`
}

// TableName 获取表名
func (YearHoliday) TableName() string {
	return "year_holiday"
}

type holiday struct {
}

var TbYearHoliday holiday

func (p *holiday) GetListByYear(year string) []*YearHoliday {
	var tables []*YearHoliday
	err := databases.GetEngine().Where("year = ?", year).OrderBy("date_index asc").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
