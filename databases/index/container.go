package index

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
)

// Container 容器模型
type Container struct {
	ID            int64  `xorm:"int(10) unsigned notnull pk autoincr 'id'" json:"id"`       // 容器id
	Name          string `xorm:"varchar(64) notnull 'name'" json:"name"`                    // 容器名称
	BgName        string `xorm:"varchar(64) notnull 'bg_name'" json:"bg_name"`              // 标题
	ContainerType int64  `xorm:"tinyint(3) notnull 'container_type'" json:"container_type"` // 容器类型
	BeginTime     int64  `xorm:"int(10) notnull 'begin_time'" json:"begin_time"`            // 开始时间
	EndTime       int64  `xorm:"int(10) notnull 'end_time'" json:"end_time"`                // 结束时间
	BgImgPhone    string `xorm:"varchar(64) notnull 'bg_img_phone'" json:"bg_img_phone"`    // 背景图phone
	BgImgPad      string `xorm:"varchar(64) notnull 'bg_img_pad'" json:"bg_img_pad"`        // 背景图pad
	ResourceList  string `xorm:"text notnull 'resource_list'" json:"resource_list"`         // 资源列表
	IsDel         int32  `xorm:"tinyint unsigned not null 'is_del'"`
	CreateTime    int64  `xorm:"int(10) notnull 'create_time'" json:"create_time"` // 创建时间
	UpdateTime    int64  `xorm:"int(10) notnull 'update_time'" json:"update_time"` // 修改时间
	ConfigParam   string `xorm:"text notnull 'config_param'" json:"config_param"`  // 配置参数
	Title         string `xorm:"varchar(64) notnull 'title'" json:"title"`         // 标题
}

func (Container) TableName() string {
	return "index_container"
}

// Save 保存数据
func (c *Container) Save() error {
	c.CreateTime = time.Now().Unix()
	c.UpdateTime = c.CreateTime
	_, err := databases.GetEngineMaster().Insert(c)
	return err
}

// Update 更新数据
func (c *Container) Update() error {
	c.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(c.ID).Update(c)
	return err
}

type container struct{}

var TbContainer container

// GetItemByID 通过ID获取详情
func (c *container) GetItemByID(id int64) *Container {
	var table Container
	ok, err := databases.GetEngine().Where("id = ?", id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

type ContainerListReq struct {
	ID            int64  `json:"id"`
	ContainerType int    `json:"container_type"`
	Name          string `json:"name"`
	BgName        string `json:"bg_name"`
	Page          int    `json:"page"`
	PageSize      int    `json:"page_size"`
}

func (c *container) GetList(idList []int64) []*Container {
	var tables []*Container
	err := databases.GetEngine().In("id", idList).And("is_del = ?",
		library.No).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
