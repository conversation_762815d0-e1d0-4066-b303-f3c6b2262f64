package index

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
)

type GroupContainer struct {
	ID           int64 `xorm:"int(10) unsigned notnull pk autoincr 'id'" json:"id"`
	IndexGroupID int64 `xorm:"int(10) unsigned notnull 'index_group_id'" json:"index_group_id"`
	ContainerID  int64 `xorm:"int(10) unsigned notnull 'container_id'" json:"container_id"`
	Sort         int64 `xorm:"smallint(5) unsigned notnull 'sort'" json:"sort"`
	IsDel        int32 `xorm:"tinyint(2) unsigned notnull 'is_del'" json:"is_del"`
	CreateTime   int64 `xorm:"int(11) unsigned notnull 'create_time'" json:"create_time"`
	UpdateTime   int64 `xorm:"int(11) unsigned notnull 'update_time'" json:"update_time"`
	ResourceTab  int64 `xorm:"int unsigned not null 'resource_tab'"`
}

func (*GroupContainer) TableName() string {
	return "index_group_container"
}

// Save 保存数据
func (g *GroupContainer) Save() error {
	g.CreateTime = time.Now().Unix()
	g.UpdateTime = g.CreateTime
	_, err := databases.GetEngineMaster().Insert(g)
	return err
}

// Update 更新数据
func (g *GroupContainer) Update() error {
	g.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(g.ID).Update(g)
	return err
}

type groupContainer struct{}

var TbGroupContainer groupContainer

// GetItemByID 通过ID获取详情
func (g *groupContainer) GetItemByID(id int64) *GroupContainer {
	var table GroupContainer
	ok, err := databases.GetEngine().Where("id = ?", id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (g *groupContainer) GetList(groupID, resourceTab int64) []*GroupContainer {
	var tables []*GroupContainer
	err := databases.GetEngine().Where("index_group_id = ?",
		groupID).And("is_del = ?", library.No).And("resource_tab = ?", resourceTab).OrderBy("sort asc").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
