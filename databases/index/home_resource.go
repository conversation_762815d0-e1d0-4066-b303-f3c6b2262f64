package index

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
)

type HomeContainer struct {
	ID           int64  `xorm:"int(10) unsigned notnull pk autoincr 'id'" json:"id"` // 容器id
	Name         string `xorm:"varchar(64) notnull 'name'" json:"name"`              // 容器名称
	ResourceList string `xorm:"text notnull 'resource_list'" json:"resource_list"`   // 资源列表
	IsDel        int32  `xorm:"tinyint unsigned not null 'is_del'"`
	CreateTime   int64  `xorm:"int(10) notnull 'create_time'" json:"create_time"` // 创建时间
	UpdateTime   int64  `xorm:"int(10) notnull 'update_time'" json:"update_time"` // 修改时间
}

func (HomeContainer) TableName() string {
	return "index_home_container"
}

// Save 保存数据
func (c *HomeContainer) Save() error {
	c.CreateTime = time.Now().Unix()
	c.UpdateTime = c.CreateTime
	_, err := databases.GetEngineMaster().Insert(c)
	return err
}

// Update 更新数据
func (c *HomeContainer) Update() error {
	c.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(c.ID).Update(c)
	return err
}

type homeContainer struct{}

var TbHomeContainer homeContainer

// GetItemByID 通过ID获取详情
func (c *homeContainer) GetItemByID(id int64) *HomeContainer {
	var table HomeContainer
	ok, err := databases.GetEngine().Where("id = ?", id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

type HomeContainerListReq struct {
	ID       int64  `json:"id"`
	Name     string `json:"name"`
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
}

func (c *homeContainer) GetList(req *HomeContainerListReq) []*HomeContainer {
	var tables []*HomeContainer
	sess := databases.GetEngine().NewSession()
	defer sess.Close()
	sess.Where("is_del = ?", library.No)
	if req.ID > 0 {
		sess.And("id = ?", req.ID)
	}
	if req.Name != "" {
		sess.And("name LIKE ?", "%"+req.Name+"%")
	}
	offset := (req.Page - 1) * req.PageSize
	sess.OrderBy("id desc").Limit(req.PageSize, offset)
	err := sess.Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (c *homeContainer) GetALL() []*HomeContainer {
	var tables []*HomeContainer
	sess := databases.GetEngine().NewSession()
	defer sess.Close()
	sess.Where("is_del = ?", library.No)
	err := sess.Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
