package index

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
)

type UserGroup struct {
	ID          int64  `xorm:"int not null pk 'id'"`
	UserGroupID int64  `xorm:"int unsigned not null 'user_group_id'"`
	Priority    int64  `xorm:"int unsigned not null 'priority'"`
	Remark      string `xorm:"varchar(255) not null 'remark'"`
	IsOnline    int32  `xorm:"tinyint unsigned not null 'is_online'"`
	IsDel       int32  `xorm:"tinyint unsigned not null 'is_del'"`
	CreateTime  int64  `xorm:"int unsigned not null 'create_time'"`
	UpdateTime  int64  `xorm:"int unsigned not null 'update_time'"`
	ResourceTab int    `xorm:"int unsigned not null 'resource_tab'"`
}

func (UserGroup) TableName() string {
	return "index_user_group"
}

// Save 保存数据
func (u *UserGroup) Save() error {
	u.CreateTime = time.Now().Unix()
	u.UpdateTime = u.CreateTime
	_, err := databases.GetEngineMaster().Insert(u)
	return err
}

// Update 更新数据
func (u *UserGroup) Update() error {
	u.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(u.ID).Update(u)
	return err
}

type userGroup struct{}

var TbUserGroup userGroup

// GetItemByID 通过ID获取详情
func (a *userGroup) GetItemByID(id int64) *UserGroup {
	var table UserGroup
	ok, err := databases.GetEngine().Where("id = ?", id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (a *userGroup) GetListByStatus(isOnline int, resourceTab int64) []*UserGroup {
	var tables []*UserGroup
	err := databases.GetEngine().Where("is_online= ? and is_del = ? and resource_tab = ?", isOnline,
		library.No, resourceTab).OrderBy("priority desc,id desc").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (a *userGroup) GetRepeatItem(userGroupID, currentID int64) *UserGroup {
	var table UserGroup
	ok, err := databases.GetEngine().Where("user_group_id = ? and id != ?", userGroupID, currentID).
		And("is_del = ?", library.No).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
