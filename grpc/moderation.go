package grpc

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/grpc"
	moderation "gitlab.dailyyoga.com.cn/protogen/moderation-go"
	"gitlab.dailyyoga.com.cn/server/children/config"
)

var moderationClient moderation.ModerationClient

func GetModerationClient() moderation.ModerationClient {
	return moderationClient
}

func initModeration(ms *microservice.Microservice) error {
	cfgEnv := config.Get().Service.Env
	serviceName := "moderation"
	if cfgEnv == microservice.Mirror {
		serviceName = "moderation-mirro"
	}
	cc, err := grpc.NewClient(ms, serviceName)
	if err != nil {
		return err
	}
	moderationClient = moderation.NewModerationClient(cc)
	return nil
}
