package grpc

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/grpc"
	proto "gitlab.dailyyoga.com.cn/protogen/yoga-cs-go/yoga-cs"
	"gitlab.dailyyoga.com.cn/server/children/config"
)

var yoGaCsClient proto.YoGaCSClient

func GetYoGaCsClientClient() proto.YoGaCSClient {
	return yoGaCsClient
}

func initYoGaCs(ms *microservice.Microservice) error {
	cfgEnv := config.Get().Service.Env
	serviceName := "yoga-cs"
	if cfgEnv == microservice.Mirror {
		serviceName = "yoga-cs-mirro"
	}

	cc, err := grpc.NewClient(ms, serviceName)
	if err != nil {
		return err
	}
	yoGaCsClient = proto.NewYoGaCSClient(cc)
	return nil
}
