package grpc

import (
	"fmt"

	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
)

func Starter(ms *microservice.Microservice) {
	fmt.Println("grpc.Starter", ms.GetConf().GetViperConf())
	if err := initMessage(ms); err != nil {
		logger.Error("initMessage", "error", err)
	}
	if err := initModeration(ms); err != nil {
		logger.Error("initModeration", "error", err)
	}
	if err := initChildrenGroup(ms); err != nil {
		logger.Error("initChildrenGroup", "error", err)
	}
	if err := initChildrenRop(ms); err != nil {
		logger.Error("initChildrenRop", "error", err)
	}
	if err := initIPGeo(ms); err != nil {
		logger.Error("initIPGeo", "error", err)
	}
	if err := initKafka(ms); err != nil {
		logger.Error("initKafka", "error", err)
	}
	if err := initPayBridge(ms); err != nil {
		logger.Error("initPayBridge", "error", err)
	}
	if err := initYoGaCs(ms); err != nil {
		logger.Error("initYoGaCs", "error", err)
	}
}
