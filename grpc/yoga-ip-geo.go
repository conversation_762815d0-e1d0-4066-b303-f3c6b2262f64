package grpc

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/grpc"
	"gitlab.dailyyoga.com.cn/server/children/config"

	proto "gitlab.dailyyoga.com.cn/protogen/yoga-ip-geo-go/yoga-ip-geo"
)

var ipGeoClient proto.IpGeoClient

func GetIPGeoClient() proto.IpGeoClient {
	return ipGeoClient
}

func initIPGeo(ms *microservice.Microservice) error {
	cfgEnv := config.Get().Service.Env
	serviceName := "yoga-ip-geo"
	if cfgEnv == microservice.Mirror {
		serviceName = "yoga-ip-geo-mirro"
	}

	conn, err := grpc.NewClient(ms, serviceName)
	if err != nil {
		return err
	}
	ipGeoClient = proto.NewIpGeoClient(conn)
	return nil
}
