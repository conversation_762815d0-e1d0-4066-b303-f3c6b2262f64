package grpc

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/grpc"
	"gitlab.dailyyoga.com.cn/server/children/config"

	kafka "gitlab.dailyyoga.com.cn/protogen/srv-kafka-go"
)

var kafkaClient kafka.KafkaClient

func GetKafkaClient() kafka.KafkaClient {
	return kafkaClient
}

// initKafka 初始化队列
func initKafka(ms *microservice.Microservice) error {
	cfgEnv := config.Get().Service.Env
	serviceName := "srv-kafka"
	if cfgEnv == microservice.Mirror {
		serviceName = "srv-kafka-mirro"
	}

	cc, err := grpc.NewClient(ms, serviceName)
	if err != nil {
		return err
	}
	kafkaClient = kafka.NewKafkaClient(cc)
	return nil
}
