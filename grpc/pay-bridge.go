package grpc

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/grpc"
	"gitlab.dailyyoga.com.cn/protogen/pay-bridge-go/paybridge"
	"gitlab.dailyyoga.com.cn/server/children/config"
)

var payBridgeClient paybridge.PayBridgeClient

func GetPayBridgeClient() paybridge.PayBridgeClient {
	return payBridgeClient
}

func initPayBridge(ms *microservice.Microservice) error {
	cfgEnv := config.Get().Service.Env
	serviceName := "pay-bridge"
	if cfgEnv == microservice.Mirror {
		serviceName = "pay-bridge-mirro"
	}

	conn, err := grpc.NewClient(ms, serviceName)
	if err != nil {
		return err
	}
	payBridgeClient = paybridge.NewPayBridgeClient(conn)
	return nil
}
