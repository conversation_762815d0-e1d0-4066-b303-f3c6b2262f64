package grpc

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/grpc"
	"gitlab.dailyyoga.com.cn/protogen/children-user-group-go/childrenusergroup"
	"gitlab.dailyyoga.com.cn/server/children/config"
)

var childrenGroupClient childrenusergroup.GroupClient

func GetChildrenGroupClient() childrenusergroup.GroupClient {
	return childrenGroupClient
}

func initChildrenGroup(ms *microservice.Microservice) error {
	cfgEnv := config.Get().Service.Env
	serviceName := "children-user-group"
	if cfgEnv == microservice.Mirror {
		serviceName = "children-user-group-mirror"
	}
	cc, err := grpc.NewClient(ms, serviceName)
	if err != nil {
		return err
	}
	childrenGroupClient = childrenusergroup.NewGroupClient(cc)
	return nil
}
