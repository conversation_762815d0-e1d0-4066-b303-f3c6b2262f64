package grpc

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/grpc"
	"gitlab.dailyyoga.com.cn/server/children/config"

	rop "gitlab.dailyyoga.com.cn/protogen/children-rop-go/children-rop"
)

var ropChildrenClient rop.ChildrenRopClient

func GetChildrenRopClient() rop.ChildrenRopClient {
	return ropChildrenClient
}

func initChildrenRop(ms *microservice.Microservice) error {
	cfgEnv := config.Get().Service.Env
	serviceName := "children-rop"
	if cfgEnv == microservice.Mirror {
		serviceName = "children-rop-mirror"
	}
	cc, err := grpc.NewClient(ms, serviceName)
	if err != nil {
		return err
	}
	ropChildrenClient = rop.NewChildrenRopClient(cc)
	return nil
}
