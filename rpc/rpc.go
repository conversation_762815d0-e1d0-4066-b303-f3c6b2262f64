package rpc

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/grpc"
	"gitlab.dailyyoga.com.cn/protogen/children-go/children"
	googlegrpc "google.golang.org/grpc"
)

type ChildrenService struct {
	*children.UnimplementedChildrenServer
}

func Starter(ms *microservice.Microservice) {
	grpc.NewServer(ms, func(server *googlegrpc.Server) {
		children.RegisterChildrenServer(server, &ChildrenService{})
	})
}
