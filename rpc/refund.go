package rpc

import (
	"context"

	pb "gitlab.dailyyoga.com.cn/protogen/children-go/children"
	"gitlab.dailyyoga.com.cn/server/children/databases/order"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	"gitlab.dailyyoga.com.cn/server/children/service/refund"
	"gitlab.dailyyoga.com.cn/server/children/service/sensor"
)

func (u *ChildrenService) AfterCompleteRefund(ctx context.Context,
	req *pb.CompleteReq) (*pb.OrderRefundRes, error) {
	refundItem := order.TbRefund.GetRefundByID(req.RefundID)
	res := &pb.OrderRefundRes{}
	if refundItem == nil {
		res.ErrorCode = int32(errorcode.InvalidParams)
		res.Msg = errorcode.ErrorCodeMessage[errorcode.InvalidParams]
		return res, nil
	}
	if req.OnlyReport == library.Yes {
		// 神策上报退款事件
		sensor.ReportRefundOrder(refundItem)
		return res, nil
	}
	refund.AfterCompleteRefund(refundItem)
	return res, nil
}
