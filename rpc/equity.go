package rpc

import (
	"context"
	"errors"
	"strconv"
	"strings"
	"sync"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	pb "gitlab.dailyyoga.com.cn/protogen/children-go/children"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	dbuser "gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	"gitlab.dailyyoga.com.cn/server/children/library/pay"
	lbp "gitlab.dailyyoga.com.cn/server/children/library/product"
	"gitlab.dailyyoga.com.cn/server/children/library/user"
	"gitlab.dailyyoga.com.cn/server/children/service/equity"
	"gitlab.dailyyoga.com.cn/server/children/service/order"
)

// 同步处理最多100个 超过100异步处理
const MaxSyncUserID = 100

func (u *ChildrenService) ChangeVipEquity(ctx context.Context,
	req *pb.ChangeVipEquityReq) (*pb.ChangeVipEquityRes, error) {
	uidStr := req.UIDStrList
	operateType := req.OperateType
	durationType := req.DurationType
	durationValue := req.DurationValue
	changeReason := req.ChangeReasonType
	vipType := req.VipType
	desc := req.Remark
	adminName := req.AdminName
	uidStrList := strings.Split(uidStr, ",")
	if vipType == 0 {
		vipType = lbp.ProductVipTypeVIP
	}
	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		for _, v := range uidStrList {
			uid, err := strconv.ParseInt(v, 10, 64)
			if err != nil {
				logger.Warn(err)
				continue
			}
			if uid <= 0 {
				continue
			}
			OperateVipByAdmin(&OperateVipParam{
				UID:           uid,
				DurationType:  int(durationType),
				DurationValue: int(durationValue),
				ChangeReason:  user.VipChangeReason(changeReason),
				OperateType:   user.VipOperateType(operateType),
				Desc:          desc,
				AdminName:     adminName,
				VipType:       int(vipType),
			})
		}
	}()
	if len(uidStrList) <= MaxSyncUserID {
		wg.Wait()
	}
	return &pb.ChangeVipEquityRes{}, nil
}

type OperateVipParam struct {
	UID           int64
	DurationType  int
	DurationValue int
	ChangeReason  user.VipChangeReason
	OperateType   user.VipOperateType
	Desc          string
	AdminName     string
	VipType       int
}

// 后台操作用户会员
func OperateVipByAdmin(param *OperateVipParam) bool {
	session := databases.GetEngineMaster().NewSession()
	defer session.Close()
	if err := session.Begin(); err != nil {
		logger.Error(err)
		return false
	}
	var err error
	defer func() {
		if err != nil {
			logger.Error(err)
			if err = session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()
	record := dbuser.AdminOperateVipRecord{
		UID:           param.UID,
		DurationType:  param.DurationType,
		DurationValue: param.DurationValue,
		OperateType:   int(param.OperateType),
		Desc:          param.Desc,
		AdminName:     param.AdminName,
		ChangeReason:  int(param.ChangeReason),
		VipType:       param.VipType,
	}
	ok, historyID := equity.OperateEquity(session, &equity.OperateEquityParam{
		UID:           param.UID,
		DurationType:  param.DurationType,
		DurationValue: param.DurationValue,
		SourceType:    user.VipSourceTypeEnum.Admin,
		ChangeReason:  param.ChangeReason,
		OperateType:   param.OperateType,
		VipType:       param.VipType,
	})
	if !ok {
		logger.Error("后台充值会员失败", *param)
		err = errors.New("后台充值会员失败")
		return false
	}
	record.RecordID = historyID
	if err = record.SaveByTran(session); err != nil {
		return false
	}
	if err = session.Commit(); err != nil {
		return false
	}
	return true
}

func (u *ChildrenService) Unsubscribe(ctx context.Context,
	req *pb.UnsubscribeReq) (*pb.UnsubscribeRes, error) {
	res := &pb.UnsubscribeRes{}
	subItem := dbuser.TbWPSubU.GetItemByContractID(req.ContractID)
	if subItem == nil {
		res.ErrorCode = int32(errorcode.DBError)
		res.Msg = errorcode.ErrorCodeMessage[errorcode.DBError]
		return res, nil
	}
	if !order.UnsubscribeByOrderID(subItem.OrderID, pay.UnsubscribeTypeEnum.Admin) {
		res.ErrorCode = int32(errorcode.DBError)
		res.Msg = errorcode.ErrorCodeMessage[errorcode.DBError]
		return res, nil
	}
	return res, nil
}

// UnsubscribeByOrderID 取消订阅
func (u *ChildrenService) UnsubscribeByOrderID(_ context.Context,
	req *pb.UnsubscribeByOrderIDReq) (*pb.UnsubscribeByOrderIDRes, error) {
	res := &pb.UnsubscribeByOrderIDRes{}

	if req.OrderID == "" {
		res.ErrorCode = int32(errorcode.InvalidParams)
		res.Msg = errorcode.ErrorCodeMessage[errorcode.InvalidParams]
		return res, nil
	}
	status := order.UnsubscribeByOrderID(req.OrderID, pay.UnsubscribeTypeEnum.Admin)
	if !status {
		res.ErrorCode = int32(errorcode.InvalidParams)
		res.Msg = errorcode.ErrorCodeMessage[errorcode.InvalidParams]
		return res, nil
	}
	return res, nil
}
