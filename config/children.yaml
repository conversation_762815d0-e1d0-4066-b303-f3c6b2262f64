http:
  addr: "0.0.0.0:28410"
grpc:
  port: "23510"
service:
  domain: "apiqa.childrenworkout.com.cn"
  sensors_data_path: "/var/log/sc/api-track"
  apple_passwd: "f4657f1ec2d14a8b86a13a205d54c60a"
  passwd_aes_key: "0dbc38558edcdafe"

api_config:
  enable_failed_log: true
  status_code: 403
  white_lists:
    uris:
    paths:
      - /children/callback
  block_lists:
    user_agent:
      - "Go-http-client"
  sign_keys:
    1: "d65b61a4c21b19c9320e8b65e288a4b3"
wechat_pay:
  app_id: "wxf28d63df28e8fcee"
  app_secret: "1045bfccc3fc8828216eab5d794084b8"
  app_key: ""
  mch_id: "1714976568"
  certificate_number: "0117149765682025042800191742001001"
  apiv3_key: "nXXX8kgB99FcWGEAz0H8IUyCHiPO6kUm"
  api_private_key_path: "/wechat_cert/tls.key"

jiguang_key: 
  app_key: "09119e254daec370fe0ab3e7"
  master_secret: "3e8005203cbf2c5f2a28ff1e"
  private_key_path: "/var/pem/jiguang_privatekey.pem"
qiniu_cdn:
  access_key: "cWMLkIyGOc6M2dFKxuXPS6FYqOA8s8ECFANf6i5F"
  access_secret: "4frArJz78N5hz-WdZGd_T_pDZyKyk_zUExfOpQZt"
xiaomi_market:
  app_id: ""
  customer_id: ""
  secret:
    - conv_type: APP_ACTIVE
      sign_key: 
      encrypt_key: 
    - conv_type: APP_REGISTER
      sign_key: 
      encrypt_key: 
    - conv_type: APP_FIRST_PAY
      sign_key: 
      encrypt_key: 
apple_ads_config:
  client_id: ""
  team_id: ""
  key_id: ""
  audience: "https://appleid.apple.com"
  alg: "ES256"
  org_name: "小树苗运动"
app_store:
  key_id: ""
  bundle_id: ""
  issuer: ""
vivo_ocpx:
  client_id: ""
  secret: ""
  advertiser_id: ""
tiktok:
  - name: "children"
    app_id: ""
    app_secret: ""
    token: ""
    salt: ""
    key_version: "1"
    mch_id: ""
wechat_miniapp:
  - name: "children"
    app_id: ""
    app_secret: ""
    app_key: ""
    mch_id: ""
    certificate_number: ""
    apiv3_key: ""
    api_private_key_path: "/wechat_cert/tls.key"
domain_microapp: ""