package config

import (
	"encoding/json"
	"fmt"
	"os"
	"sync"

	"gitlab.dailyyoga.com.cn/gokit/conf"
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/gokit/middlewares/auth"
	"gitlab.dailyyoga.com.cn/server/children/library/tools"
)

var gConfig *Conf

func Get() *Conf {
	return gConfig
}

type Service struct {
	Env             microservice.Env `json:"env"`
	Name            string           `json:"name"`
	Domain          string           `json:"domain"`
	SensorsDataPath string           `json:"sensors_data_path"`
	PassWdAesKey    string           `json:"passwd_aes_key"`
	AlipayKeyPath   string           `json:"alipay_key_path"`
	ApplePasswd     string           `json:"apple_passwd"`
}

type Conf struct {
	Service        Service    `json:"service"`
	Ji<PERSON>uang<PERSON><PERSON>ey `json:"jiguang_key"`
	QiniuCDN       QiniuCDN   `json:"qiniu_cdn"`
	AlipaySecret   map[string]*AlipaySecret
	OppoOcpx       OppoOcpxConfig  `json:"oppo_ocpx"`
	WechatPay      WeChatPay       `json:"wechat_pay"`
	XiaomiMarket   XiaomiMarket    `json:"xiaomi_market"`
	AppleAdsConfig AppleAdsConfig  `json:"apple_ads_config"`
	YogaCsAddress  string          `json:"yoga_cs_address"`
	AppStore       AppleStore      `json:"app_store"`
	VivoOcpx       vivoOcpx        `json:"vivo_ocpx"`
	TikTok         []DouYinAccess  `json:"tiktok"`
	WechatMiNiApp  []WechatMiNiApp `json:"wechat_miniapp"`
	APIConfig      auth.APIConfig  `json:"api_config"`
	DBChildren     Database        `json:"db_children"`
	DBYoGaCs       Database        `json:"db_yoga_cs"`
}

type Database struct {
	Master DatabaseConf   `json:"master"`
	Slaves []DatabaseConf `json:"slaves"`
}

type DatabaseConf struct {
	Address   string `json:"address"`
	User      string `json:"user"`
	Password  string `json:"password"`
	Databases string `json:"databases"`
	Charset   string `json:"charset"`
}

type WeChatPay struct {
	AppID             string `json:"app_id"`
	AppSecret         string `json:"app_secret"`
	MchID             string `json:"mch_id"`
	CertificateNumber string `json:"certificate_number"` // 商户证书序列号有效期2022/09/30至2027/09/29
	APIV3Key          string `json:"apiv3_key"`
}

type vivoOcpx struct {
	ClientID     string `json:"client_id"`
	Secret       string `json:"secret"`
	AdvertiserID string `json:"advertiser_id"`
}

type AlipaySecret struct {
	AppID                string `json:"app_id"`
	PrivateKey           string `json:"private_key"`
	IsCertMode           bool   `json:"is_cert_mode"`
	AlipayPublicKey      string `json:"alipay_public_key"`
	AliPayRootCertKey    string `json:"ali_pay_root_cert_key"`
	AliPayPublicCertKey  string `json:"ali_pay_public_cert_key"`
	AppCertPath          string `yaml:"app_cert_path"`
	AliPayRootCertPath   string `yaml:"ali_pay_root_cert_path"`
	AliPayPublicCertPath string `yaml:"ali_pay_public_cert_path"`
}

type OppoOcpxConfig struct {
	AesKey string `json:"aes_key"`
	Salt   string `json:"salt"`
}

type QiniuCDN struct {
	AccessKey    string `json:"access_key"`
	AccessSecret string `json:"access_secret"`
}
type JiGuangKey struct {
	AppKey         string `json:"app_key"`
	MasterSecret   string `json:"master_secret"`
	PrivateKeyPath string `json:"private_key_path"`
}

type XiaomiMarket struct {
	APPID      string               `json:"app_id"`
	CustomerID string               `json:"customer_id"`
	Secret     []XiaomiMarketSecret `json:"secret"`
}

type XiaomiMarketSecret struct {
	ConvType   string `json:"conv_type"`
	SignKey    string `json:"sign_key"`
	EncryptKey string `json:"encrypt_key"`
}

var mu sync.Mutex

func (c *Conf) GetAlipayKeyByAppID(appID string) *AlipaySecret {
	if appID == "" {
		logger.Error("未配置支付宝当前使用的商户号")
		return nil
	}
	mu.Lock()
	defer mu.Unlock()
	if c.AlipaySecret == nil {
		c.AlipaySecret = make(map[string]*AlipaySecret)
	}
	as, ok := c.AlipaySecret[appID]
	if !ok {
		key := c.loadAlipayKey(appID)
		if key == nil || key.AlipayPublicKey == "" || key.PrivateKey == "" {
			logger.Error("获取支付宝商户密钥失败", appID)
			return nil
		}
		c.AlipaySecret[appID] = key
		return key
	}
	return as
}

// loadAlipayKey 根据APPID加载支付宝密钥
func (c *Conf) loadAlipayKey(appID string) *AlipaySecret {
	res := &AlipaySecret{
		AppID: appID,
	}
	publicKeyPath := fmt.Sprintf("/alipay/%s-public-key", appID)
	publicKeyByte, err := tools.ReadFile(publicKeyPath)
	if err != nil && len(publicKeyByte) == 0 {
		publicKeyPath = fmt.Sprintf("/alipay/%s-app-cert-public-key", appID)
		publicKeyByte, err = tools.ReadFile(publicKeyPath)
		if err != nil || len(publicKeyByte) == 0 {
			logger.Error("读取支付宝公钥失败", err)
			return nil
		}
		res.IsCertMode = true
	}
	res.AlipayPublicKey = string(publicKeyByte)
	privateKeyPath := fmt.Sprintf("/alipay/%s-private-key", appID)
	privateKeyByte, err := tools.ReadFile(privateKeyPath)
	if err != nil || len(privateKeyByte) == 0 {
		logger.Error("读取支付宝api私钥失败", err)
		return nil
	}
	res.PrivateKey = string(privateKeyByte)
	aliPayRootCertPath := fmt.Sprintf("/alipay/%s-alipay-root-cert", appID)
	if _, err := os.Stat(aliPayRootCertPath); !os.IsNotExist(err) {
		aliPayRootCert, err := tools.ReadFile(aliPayRootCertPath)
		if err != nil || len(aliPayRootCert) == 0 {
			logger.Error("读取支付宝api私钥失败", err)
			return nil
		}
		res.AliPayRootCertKey = string(aliPayRootCert)
	}

	aliPayPublicCertPath := fmt.Sprintf("/alipay/%s-alipay-cert-public-key-rsa2", appID)
	if _, err := os.Stat(aliPayPublicCertPath); !os.IsNotExist(err) {
		aliPayPublicCert, err := tools.ReadFile(aliPayPublicCertPath)
		if err != nil || len(aliPayPublicCert) == 0 {
			logger.Error("读取支付宝api私钥失败", err)
			return nil
		}
		res.AliPayPublicCertKey = string(aliPayPublicCert)
	}

	return res
}

type AppleAdsConfig struct {
	ClientID string `json:"client_id"`
	TeamID   string `json:"team_id"`
	KeyID    string `json:"key_id"`
	Audience string `json:"audience"`
	Alg      string `json:"alg"`
	OrgName  string `json:"org_name"`
}

type AppleStore struct {
	KeyID    string `json:"key_id"`
	BundleID string `json:"bundle_id"`
	Issuer   string `json:"issuer"`
}

type DouYinAccess struct {
	Name       string `json:"name"`
	AppID      string `json:"app_id"`
	AppSecret  string `json:"app_secret"`
	Token      string `json:"token"`
	SALT       string `json:"salt"`
	KeyVersion string `json:"key_version"`
	MchID      string `json:"mch_id"`
}

var tikTokAccessMap map[string]*DouYinAccess

func (c *Conf) GetTikTokAccess(appID string) *DouYinAccess {
	if len(tikTokAccessMap) > 0 {
		if cfg, ok := tikTokAccessMap[appID]; ok {
			return cfg
		}
	}
	cfgList := c.TikTok
	if cfgList == nil {
		return nil
	}
	tikTokAccessMap = make(map[string]*DouYinAccess)
	for i := range cfgList {
		tikTokAccessMap[cfgList[i].AppID] = &cfgList[i]
	}
	if cfg, ok := tikTokAccessMap[appID]; ok {
		return cfg
	}
	return &cfgList[0]
}

type WechatMiNiApp struct {
	Name              string `json:"name"`
	AppID             string `json:"app_id"`
	AppSecret         string `json:"app_secret"`
	MchID             string `json:"mch_id"`
	AppKey            string `json:"app_key"`
	CertificateNumber string `json:"certificate_number"` // 商户证书序列号有效期2022/09/30至2027/09/29
	APIV3Key          string `json:"apiv3_key"`
	APIPrivateKeyPath string `json:"api_private_key_path"`
}

var wechatAccessMap map[string]*WechatMiNiApp

func (c *Conf) GetWxMiNiAppAccess(appID string) *WechatMiNiApp {
	if len(wechatAccessMap) > 0 {
		if cfg, ok := wechatAccessMap[appID]; ok {
			return cfg
		}
	}
	cfgList := c.WechatMiNiApp
	if cfgList == nil {
		return nil
	}
	wechatAccessMap = make(map[string]*WechatMiNiApp)
	for i := range cfgList {
		wechatAccessMap[cfgList[i].AppID] = &cfgList[i]
	}
	if cfg, ok := wechatAccessMap[appID]; ok {
		return cfg
	}
	return &cfgList[0]
}

type TikTokPemPath struct {
	AppID           string
	TikTokPublicKey string
	PrivateKey      string
}

func (c *Conf) GetTikTokPemPath(appID string) *TikTokPemPath {
	res := &TikTokPemPath{
		AppID: appID,
	}
	publicKeyPath := fmt.Sprintf("/tiktok/%s-public-key.pem", appID)
	publicKeyByte, err := tools.ReadFile(publicKeyPath)
	if err != nil || len(publicKeyByte) == 0 {
		logger.Error("读取抖音公钥失败", err)
		return nil
	}
	res.TikTokPublicKey = publicKeyPath
	privateKeyPath := fmt.Sprintf("/tiktok/%s-private-key.pem", appID)
	privateKeyByte, err := tools.ReadFile(privateKeyPath)
	if err != nil || len(privateKeyByte) == 0 {
		logger.Error("读取抖音api私钥失败", err)
		return nil
	}
	res.PrivateKey = privateKeyPath
	return res
}

func InitConf(ms *microservice.Microservice) {
	confInfo := ms.GetConf().GetViperConf()
	var err error
	gConfig, err = conf.Parse[Conf](confInfo)
	if err != nil {
		panic(err)
	}
	aa, _ := json.Marshal(gConfig)
	logger.Info("InitConf", string(aa))
}
