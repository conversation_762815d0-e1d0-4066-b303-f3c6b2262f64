package main

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/server/children/config"
	srvcron "gitlab.dailyyoga.com.cn/server/children/service/cron"
	"gitlab.dailyyoga.com.cn/server/children/service/sensor"
	srvuser "gitlab.dailyyoga.com.cn/server/children/service/user"
	util "gitlab.dailyyoga.com.cn/server/children/service/util"
)

// nolint
func cronHandle(s *util.Server) {
	if config.Get().Service.Env == microservice.Mirror {
		// Mirror 规定不允许跑定时任务
		return
	}
	s.Handle("sensors_channel_report", "@every 3m", sensor.ReportSignUpProfileCron)
	// 支付宝订阅处理
	s.Handle("alipay_contract_charge", "*/5 9-20/1 * * *", srvcron.AlipayContractCharge)
	// 支付宝优惠订阅处理
	s.Handle("alipay_contract_offer_charge", "*/5 9-20/1 * * *", srvcron.AlipayOfferContractCharge)
	// 支付宝订阅检查 如果20分钟没有订阅成功的，则报警
	//s.Handle("alipay_contract_warning", "*/20 * * * *", srvcron.AlipayContractWarning)
	// IOS 客户端上报重试
	s.Handle("ios_client_receipt_retry", "@every 3m", srvcron.IOSClientReceiptRetry)
	// IOS 续订
	s.Handle("ios_renew_check", "@every 3m", srvcron.IOSRenewCheck)
	// apple-asa 延迟队列
	//s.Handle("handle_apple_ads", "@every 5m", srvcron.HandleAppleAds)
	// 用户标签修改
	s.Handle("user_vip_expired", "@every 10m", srvuser.ProfileUserVipExpired)
	// change user vip 延迟队列
	s.Handle("handle_change_user_vip", "@every 2m", srvuser.HandleChangeUserVip)
	// 每日扣款计划初始化
	//s.Handle("DailyChargePlan", "0 8 * * *", srvcron.DailyChargePlan)
	// 每日扣款报表发布
	//s.Handle("DailyChargePlanReport", "5 15 * * *", srvcron.DailyChargePlanReport)
	// 阶梯扣款
	//s.Handle("step_subscribe_charge", "*/3 8-21/1 * * *", srvcron.DelayStepCharge)
	// 过期会员上报
	s.Handle("sensor_overdue_report", "30 0 * * *", srvcron.SensorVipOverdue)
	// 课程最近三十天的播放量
	s.Handle("course_play_total_30days", "@every 1h", srvcron.HandleCoursePlayTotal30Days)
}

func CronStarter(ms *microservice.Microservice) {
	cronServer := util.NewCronServer()
	cronHandle(cronServer)
	cronServer.Start()
}
