run:
  timeout: 5m
  tests: false
  skip-dirs:
    - doc
    - gen

output:
  format: tab
  print-issued-lines: true
  print=linter-name: true

linters-settings:
  unused:
    check-exported: false
  unparam:
    check-exported: false
  gomnd:
    settings:
      mnd:
        excludes: artifact.GetRandomString
        checks:
          - argument
          - case
          - condition
          - return
  funlen:
    lines: 100
    statements: 50
  goconst:
    min-len: 2
    min-occurrences: 2
  gocyclo:
    min-complexity: 15
  gocritic:
    enabled-tags:
      - diagnostic
      - experimental
      - opinionated
      - performance
      - style
    disabled-checks:
      - dupImport
      - ifElseChain
      - octalLiteral
      - whyNoLint
      - wrapperFunc
  lll:
    line-length: 150
  misspell:
    locale: US

linters:
  disable-all: true
  enable:
    - bodyclose
    - deadcode
    - dogsled
    - errcheck
    - exhaustive
    - funlen
    - goconst
    - gocritic
    - gocyclo
    - gofmt
    - goimports
    - golint
    - gomnd
    - goprintffuncname
    - gosec
    - gosimple
    - govet
    - ineffassign
    - interfacer
    - lll
    - misspell
    - nakedret
    - noctx
    - rowserrcheck
    - scopelint
    - staticcheck
    - structcheck
    - stylecheck
    - typecheck
    - unconvert
    - unparam
    - unused
    - varcheck
    - whitespace
