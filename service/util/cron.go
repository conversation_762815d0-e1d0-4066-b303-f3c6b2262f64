package util

import (
	"strings"

	"github.com/pkg/errors"
	robcron "github.com/robfig/cron/v3"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
)

type task struct {
	spec    string
	command func()
}

type Server struct {
	pool map[string]task
	c    *robcron.Cron
}

func (s *Server) Handle(name, spec string, command func()) {
	if _, ok := s.pool[name]; ok {
		return
	}
	s.pool[name] = task{
		spec:    spec,
		command: command,
	}
}

func (s *Server) Start() {
	if len(s.pool) == 0 {
		return
	}

	for k, v := range s.pool {
		if entryID, err := s.c.AddFunc(v.spec, v.command); err != nil {
			logger.Error(err)
		} else {
			logger.Infof("cron: task %s 添加成功，执行周期：%s，entry_id: %d", k, v.spec, entryID)
		}
	}
	s.c.Start()
}

func NewCronServer() *Server {
	return &Server{
		pool: make(map[string]task),
		c:    robcron.New(robcron.WithLogger(&loggerInternal{})),
	}
}

type loggerInternal struct{}

func (l *loggerInternal) Info(_ string, _ ...interface{}) {
	// 取消定时任务 info 日志打印 实际是需要 Error
	// logger.Infof(l.formatString(len(keysAndValues)), append([]interface{}{msg}, keysAndValues...)...)
}

func (l *loggerInternal) Error(err error, msg string, keysAndValues ...interface{}) {
	length := len(keysAndValues) + 2
	logger.Panic(errors.Errorf(
		l.formatString(length),
		append([]interface{}{msg, "error", err}, keysAndValues...)...))
}

func (l *loggerInternal) formatString(numKeysAndValues int) string {
	var sb strings.Builder
	sb.WriteString("%s")
	if numKeysAndValues > 0 {
		sb.WriteString(", ")
	}
	for i := 0; i < numKeysAndValues/2; i++ {
		if i > 0 {
			sb.WriteString(", ")
		}
		sb.WriteString("%v=%v")
	}
	return sb.String()
}
