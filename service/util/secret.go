package util

import (
	"crypto/aes"
	"crypto/rand"
	"crypto/rsa"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"fmt"
	"os"

	mrand "math/rand"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"golang.org/x/crypto/bcrypt"
)

// B64Encode base64加密
func B64Encode(s string) string {
	return base64.StdEncoding.EncodeToString([]byte(s))
}

// B64Encode base64解密
func B64Decode(s string) string {
	code, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		return ""
	}
	return string(code)
}

// BCryptAndSalt 加密密码
func BCryptAndSalt(pwd string) string {
	bytePwd := []byte(pwd)
	hash, err := bcrypt.GenerateFromPassword(bytePwd, bcrypt.MinCost)
	if err != nil {
		return ""
	}
	return string(hash)
}

// BcryptValidatePwd 验证密码
func BcryptValidatePwd(hashedPwd, plainPwd string) bool {
	byteHash := []byte(hashedPwd)
	bytePlainPwd := []byte(plainPwd)
	err := bcrypt.CompareHashAndPassword(byteHash, bytePlainPwd)
	return err == nil
}

// RSA解密
func RSADecrypt(cipherText []byte, path string) []byte {
	buf := ReadByFilePath(path)
	if len(buf) == 0 {
		return nil
	}
	// pem解码
	block, _ := pem.Decode(buf)
	// X509解码 密钥为PKCS#8标准
	privateKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		logger.Error(err)
		return nil
	}
	// 对密文进行解密
	plainText, err := rsa.DecryptPKCS1v15(rand.Reader, privateKey.(*rsa.PrivateKey), cipherText)
	if err != nil {
		logger.Error(err)
		return nil
	}
	// 返回明文
	return plainText
}

// GetRandomString 生成随机字符串
// nolint
func GetRandomString(length int) string {
	str := "0123456789AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz"
	bytes := []byte(str)
	result := make([]byte, 0)
	r := mrand.New(mrand.NewSource(time.Now().UnixNano()))
	for i := 0; i < length; i++ {
		result = append(result, bytes[r.Intn(len(bytes))])
	}
	return string(result)
}

// AesEncryptECB OPPO OPCX接口需要AES-128-ECB加密
func AesEncryptECB(origData, key []byte) (encrypted []byte) {
	cipher, _ := aes.NewCipher(generateKey(key))
	length := (len(origData) + aes.BlockSize) / aes.BlockSize
	plain := make([]byte, length*aes.BlockSize)
	copy(plain, origData)
	pad := byte(len(plain) - len(origData))
	for i := len(origData); i < len(plain); i++ {
		plain[i] = pad
	}
	encrypted = make([]byte, len(plain))
	// 分组分块加密
	for bs, be := 0, cipher.BlockSize(); bs <= len(origData); bs, be = bs+cipher.BlockSize(), be+cipher.BlockSize() {
		cipher.Encrypt(encrypted[bs:be], plain[bs:be])
	}
	return []byte(base64.StdEncoding.EncodeToString(encrypted))
}

// nolint
func generateKey(key []byte) (genKey []byte) {
	genKey = make([]byte, 16)
	copy(genKey, key)
	for i := 16; i < len(key); {
		for j := 0; j < 16 && i < len(key); j, i = j+1, i+1 {
			genKey[j] ^= key[i]
		}
	}
	return genKey
}

func GetPKCS1PrivateKey(keyFile string) (*rsa.PrivateKey, error) {
	keyBytes, err := os.ReadFile(keyFile)
	if err != nil {
		logger.Error(err)
		return nil, err
	}

	// 解析PEM格式的私钥
	block, _ := pem.Decode(keyBytes)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block %s", keyFile)
	}

	// 解析RSA私钥
	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		logger.Error("无法解析PKCS8格式的私钥")
		return nil, err
	}
	return privateKey, nil
}

func GetPKCS1PublicKey(keyFile string) (*rsa.PublicKey, error) {
	keyBytes, err := os.ReadFile(keyFile)
	if err != nil {
		logger.Error(err)
		return nil, err
	}
	// 解析PEM格式的私钥
	block, _ := pem.Decode(keyBytes)
	if block == nil {
		return nil, fmt.Errorf("failed to decode PEM block %s", keyFile)
	}
	// 解析RSA
	publicKey, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		logger.Error("无法解析PKCS8格式的私钥")
		return nil, err
	}
	switch key := publicKey.(type) {
	case *rsa.PublicKey:
		return key, nil
	default:
		return nil, fmt.Errorf("not rsa public key")
	}
}
