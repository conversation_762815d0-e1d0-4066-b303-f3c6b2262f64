package util

import (
	"encoding/json"
	"net/http"
	"net/url"
	"strings"
	"sync"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libclient "gitlab.dailyyoga.com.cn/server/children/library/client"
)

type ImageSize struct {
	Size   int64
	Width  int64
	Height int64
	Image  string
}

// 格式化图片信息
func FormatImageInfo(img string) *library.ImageInfo {
	if img == "" {
		return nil
	}
	size := GetImageSizeByQiNiuURL(img)
	if size == nil || size.Width == 0 || size.Height == 0 {
		logger.Error("查询图片信息失败", img)
		return nil
	}
	res := &library.ImageInfo{
		URL:    img,
		Width:  int(size.Width),
		Height: int(size.Height),
	}
	return res
}

// 格式化图片信息,输出为json字符串
func FormatImageInfoStr(img string) string {
	if img == "" {
		return ""
	}
	res := FormatImageInfo(img)
	resByte, err := json.Marshal(res)
	if err != nil {
		return ""
	}
	return string(resByte)
}

// FormatImageListStr 将逗号隔开的图片字符串格式化
func FormatImageListStr(imgListStr string) []*library.ImageInfo {
	imageList := strings.Split(imgListStr, ",")
	res := make([]*library.ImageInfo, 0)
	if len(imageList) == 0 {
		return res
	}
	for _, v := range imageList {
		if v == "" {
			continue
		}
		img := FormatImageInfo(v)
		if img == nil {
			continue
		}
		res = append(res, img)
	}
	return res
}

func UnmarshalImageStr(imgStr string) *library.ImageInfo {
	res := &library.ImageInfo{}
	if imgStr == "" {
		return res
	}
	if err := json.Unmarshal([]byte(imgStr), res); err != nil {
		logger.Warn(err)
	}
	if res.URL != "" {
		res.URL = GetOnlineImageURL(res.URL)
	}
	return res
}

// GetOnlineImageURL 又拍云图片加速
func GetOnlineImageURL(filename string) string {
	if filename == "" {
		return filename
	}
	parsedURL, err := url.Parse(filename)
	if err != nil {
		logger.Warnf("GetOnlineImageUrl 地址:%s 错误:%s", filename, err)
		return filename
	}
	domain := parsedURL.Hostname()
	if domain != "" {
		filename = strings.ReplaceAll(filename, domain, libclient.QiniuResourceDomainTmp)
	}
	return filename
}

var imageSizeMap map[string]*ImageSize
var imageSizeMapOnce sync.Once

// GetImageSizeByQiNiuURL 通过七牛地址获取图片大小
func GetImageSizeByQiNiuURL(rURL string) *ImageSize {
	if strings.HasSuffix(rURL, ".pag") {
		return new(ImageSize)
	}
	imageSizeMapOnce.Do(func() {
		imageSizeMap = make(map[string]*ImageSize)
	})
	if _, ok := imageSizeMap[rURL]; !ok {
		header := http.Header{}
		header.Add("Content-Type", "application/json")
		address := rURL + "?imageInfo"
		body, err := HTTPRequest(address, "GET", header, nil)
		if err != nil {
			logger.Warnf("获取图片尺寸出错 地址:%s 错误:%s", address, err)
			return nil
		}
		var image ImageSize
		if err := json.Unmarshal(body, &image); err != nil {
			logger.Warn(err)
			return nil
		}
		imageSizeMap[rURL] = &image
		return imageSizeMap[rURL]
	}
	return imageSizeMap[rURL]
}
