package util

import (
	"crypto/md5" // #nosec
	"encoding/hex"
	"fmt"
	"math/rand"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/server/children/library"
)

func ToBool(n int) bool {
	return n == library.Yes
}

func ToFloat64(v interface{}) float64 {
	str := ""
	if i, ok := v.([]byte); ok {
		str = string(i)
	} else {
		str = fmt.Sprint(v)
	}

	i, _ := strconv.ParseFloat(str, 64)
	return i
}

func ToFloat32(i interface{}) float32 {
	return float32(ToFloat64(i))
}

func ToInt(i interface{}) int {
	return int(ToFloat64(i))
}

func ToInt64(i interface{}) int64 {
	return int64(ToInt(i))
}

func ToString(i interface{}) string {
	return fmt.Sprint(i)
}

func Byte2Md5(b []byte) string {
	ctx := md5.New() // #nosec
	_, _ = ctx.Write(b)
	cipher := ctx.Sum(nil)

	return hex.EncodeToString(cipher)
}

func Division(dividend, divisor interface{}) float64 {
	df := ToFloat64(divisor)
	if df == 0 {
		return 0
	}
	return ToFloat64(dividend) / df
}

// MicsSlice 数组中随机取指定数量的元素
// nolint
func MicsSlice(origin []int64, count int) []int64 {
	if len(origin) < count {
		count = len(origin)
	}
	tmpOrigin := make([]int64, len(origin))
	copy(tmpOrigin, origin)
	rand.Seed(time.Now().UnixNano())
	rand.Shuffle(len(tmpOrigin), func(i int, j int) {
		tmpOrigin[i], tmpOrigin[j] = tmpOrigin[j], tmpOrigin[i]
	})

	result := make([]int64, 0, count)
	for index, value := range tmpOrigin {
		if index == count {
			break
		}
		result = append(result, value)
	}
	return result
}
