package util

import "gitlab.dailyyoga.com.cn/server/children/databases/order"

func GetRetryTimeByOrderID(orderID string) int {
	charge := order.TbAlipayCharge.GetItemByOrderID(orderID)
	// 没有扣款记录，则重试次数为0
	if charge == nil {
		return 0
	}
	lastSuccess := order.TbAlipayCharge.GetLastSuccessCharge(charge.ContractCode, charge.ID)
	var minID int64 = 0
	if lastSuccess != nil {
		minID = lastSuccess.ID
	}
	chargeList := order.TbAlipayCharge.GetListByIDRange(charge.ContractCode, minID, charge.ID)
	return len(chargeList)
}
