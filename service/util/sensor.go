package util

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"

	"gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/library"
)

type ScResponse struct {
	UID                   string `json:"second_id"`
	UtmSource             string `json:"$utm_source"`
	UtmMedium             string `json:"$utm_medium"`
	FirstChannelUtmSource string `json:"$first_channel_utm_source"`
	FirstChanneUtmMedium  string `json:"$first_channel_utm_medium"`
	Aso                   string `json:"aso"`
}

// GetSensorUserInfoMap 请求神策 查询utmSource字段
// nolint
func GetSensorUserInfoMap(uids []string, formType int) map[string]*ScResponse {
	sensorRes := make(map[string]*ScResponse)
	if len(uids) == 0 {
		return sensorRes
	}
	var v = url.Values{}
	// 请求不同环境的通过project字段区分
	cfgEnv := config.Get().Service.Env
	v.Set("project", "production")
	v.Set("token", library.SensorToken)
	if cfgEnv == microservice.Test || cfgEnv == microservice.Dev {
		v.Set("project", "default")
	}
	v.Set("format", "json")
	if formType == library.FormTypeByUID {
		v.Set("q", fmt.Sprintf(
			"select second_id,$first_channel_utm_source,$first_channel_utm_medium,$utm_source, $utm_medium, aso from users where second_id IN (%s)", getInSQLByIntArr(uids)))
	} else {
		v.Set("q", fmt.Sprintf(
			"select second_id,,$first_channel_utm_source,$first_channel_utm_medium, $utm_source, $utm_medium, aso from users where first_id IN (%s)", getInSQLByIntArr(uids)))
	}
	address := library.SensorQueryAPI + "?" + v.Encode()
	header := http.Header{}
	header.Add("Content-Type", "application/x-www-form-urlencoded")
	body, err := HTTPRequest(address, "GET", header, nil)
	if err != nil {
		logger.Warnf("神策查询注册渠道$utm_source地址:%s 错误:%s", address, err)
		return sensorRes
	}
	arr := strings.SplitAfter(string(body), "}")
	for _, resp := range arr {
		if strings.Trim(resp, " ") == "" || !strings.Contains(resp, "{") {
			continue
		}
		resp = strings.Replace(strings.Trim(resp, " "), "\n", "", -1)
		sensorResTemp := &ScResponse{}
		if err := json.Unmarshal([]byte(resp), sensorResTemp); err != nil {
			logger.Warn("神策返回数据不能解析！", resp, err.Error())
			continue
		}
		if sensorResTemp.UtmSource == "" {
			sensorResTemp.UtmSource = sensorResTemp.FirstChannelUtmSource
		}
		if sensorResTemp.UtmMedium == "" {
			sensorResTemp.UtmMedium = sensorResTemp.FirstChanneUtmMedium
		}
		if sensorResTemp.UID == "" {
			continue
		}
		sensorRes[sensorResTemp.UID] = sensorResTemp
	}
	return sensorRes
}

func getInSQLByIntArr(uids []string) string {
	uidStr := ""
	for i := range uids {
		uidStr += "'" + uids[i] + "'"
		if i != len(uids)-1 {
			uidStr += ","
		}
	}
	return uidStr
}
