package util

import (
	"strconv"
	"strings"

	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/client"
)

type version struct{}

var UVersion version

type VRes struct {
	Success bool
	ResMsg  string
	Version int64
}

const VersionEmptyErr = "版本号不能为空"
const VersionFormatErr = "输入数据格式错误"
const VersionInputErr = "版本号填写有问题"

type Place int64

func IntToEnumType(e int) Place {
	return Place(e)
}

// PlaceEnum 位数类型列表
var PlaceEnum = struct {
	First Place
	Two   Place
	Three Place
	Last  Place
}{

	First: 0,
	Two:   1,
	Three: 2,
}

var PlaceEnumValueFormat6 = map[Place]int64{
	PlaceEnum.First: 10000,
	PlaceEnum.Two:   100,
	PlaceEnum.Three: 1,
}

const PlaceVersion = 3

func (v *version) GtVersion(clientParams *library.AppClient, androidVersion, iosVersion int64) bool {
	cv := v.Format(clientParams.Version)
	if clientParams.OsType == int(client.DeviceTypeEnum.Android) ||
		clientParams.OsType == int(client.DeviceTypeEnum.Harmony) {
		return cv.Version >= androidVersion
	}
	if clientParams.OsType == int(client.DeviceTypeEnum.IOS) {
		return cv.Version >= iosVersion
	}
	return true
}

func (v *version) LtVersion(clientParams *library.AppClient, androidVersion, iosVersion int64) bool {
	cv := v.Format(clientParams.Version)
	if clientParams.OsType == int(client.DeviceTypeEnum.Android) ||
		clientParams.OsType == int(client.DeviceTypeEnum.Harmony) {
		return cv.Version < androidVersion
	}
	if clientParams.OsType == int(client.DeviceTypeEnum.IOS) {
		return cv.Version < iosVersion
	}
	return true
}

func (v *version) LeVersion(clientParams *library.AppClient, androidVersion, iosVersion int64) bool {
	cv := v.Format(clientParams.Version)
	if clientParams.OsType == int(client.DeviceTypeEnum.Android) ||
		clientParams.OsType == int(client.DeviceTypeEnum.Harmony) {
		return cv.Version <= androidVersion
	}
	if clientParams.OsType == int(client.DeviceTypeEnum.IOS) {
		return cv.Version <= iosVersion
	}
	return true
}

func (v *version) Format(i string) *VRes {
	res := &VRes{
		Success: true,
	}
	i = strings.Replace(i, "cs-", "", 1)
	if i == "" {
		res.Success = false
		res.ResMsg = VersionEmptyErr
		return res
	}
	if !strings.Contains(i, ".") {
		res.Success = false
		res.ResMsg = VersionFormatErr
		return res
	}
	arr := strings.Split(i, ".")
	if len(arr) > PlaceVersion {
		res.Success = false
		res.ResMsg = "传入版本号为8位请检查"
		return res
	}
	res.Version = v.formatPlace(arr)
	return res
}

// 处理版本号
func (v *version) formatPlace(arr []string) int64 {
	var vs int64
	for k, v := range arr {
		tmp, _ := strconv.ParseInt(v, 10, 64)
		vs += tmp * PlaceEnumValueFormat6[IntToEnumType(k)]
	}
	return vs
}

// BuildVersion 反向生成版本号
func (v *version) BuildVersion(version int64) string {
	var builder strings.Builder
	for i := 0; i < PlaceVersion; i++ {
		if i > 0 {
			builder.WriteString(".")
		}
		tmp := version / PlaceEnumValueFormat6[IntToEnumType(i)]
		str := strconv.FormatInt(tmp, 10)
		builder.WriteString(str)
		version -= tmp * PlaceEnumValueFormat6[IntToEnumType(i)]
	}
	return builder.String()
}
