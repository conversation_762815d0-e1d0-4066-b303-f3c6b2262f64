package util

import (
	"context"
	"crypto/rand"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net"
	nhttp "net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/twmb/murmur3"
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/client"
)

// 获取客户端公共参数
func GetClientInfo(t *http.Context) *library.AppClient {
	channelStr := t.Request.Header.Get("cs-channel")
	c := &library.AppClient{
		DeviceID:   t.Request.Header.Get("cs-deviceid"),
		Version:    t.Request.Header.Get("cs-version"),
		SubChannel: t.Request.Header.Get("cs-subchannel"),
		IPAds:      RemoteIP(t),
		Brand:      t.Request.Header.Get("cs-brand"),
	}
	if t.Request.Header.Get("cs-isgray") == "true" {
		c.IsGray = true
	}
	channel, err := strconv.Atoi(channelStr)
	if err == nil {
		c.Channel = channel
	}
	screenType := t.Request.Header.Get("cs-screentype")
	st, err := strconv.Atoi(screenType)
	if err == nil {
		c.ScreenType = st
	}
	osType := t.Request.Header.Get("cs-devicetype")
	ot, err := strconv.Atoi(osType)
	if err == nil {
		c.OsType = ot
	}
	if c.Channel == 0 {
		c.Channel = t.GetRequestIntD("cs_channel", 0)
	}
	if c.DeviceID == "" {
		c.DeviceID = t.GetRequestStringD("cs_deviceid", "")
	}
	if c.OsType == 0 {
		c.OsType = t.GetRequestIntD("cs_devicetype", 0)
	}
	if c.Version == "" {
		c.Version = t.GetRequestStringD("cs_version", "")
	}
	if c.ScreenType == 0 {
		c.ScreenType = t.GetRequestIntD("cs_screentype", 0)
	}
	if !c.IsGray {
		c.IsGray = t.GetReuqestBoolD("cs_isgray", false)
	}
	if c.SubChannel == "" {
		c.SubChannel = t.GetRequestStringD("cs_sub_channel", "")
	}
	if c.Brand == "" {
		c.Brand = t.GetRequestStringD("cs_brand", "")
	}
	return c
}

// ReadByFilePath 从路径中读取文件内容
func ReadByFilePath(path string) []byte {
	// 打开文件
	file, err := os.Open(path)
	if err != nil {
		logger.Error(err)
		return nil
	}
	defer file.Close()
	// 获取文件内容
	info, err := file.Stat()
	if err != nil {
		logger.Error(err)
		return nil
	}
	buf := make([]byte, info.Size())
	_, err = file.Read(buf)
	if err != nil {
		return nil
	}
	return buf
}

type WorkWechatContent struct {
	MsgType string                `json:"msgtype"`
	Text    WorkWechatContentText `json:"text"`
}

type WorkWechatContentText struct {
	Content             string   `json:"content"`
	MentionedMobileList []string `json:"mentioned_mobile_list,omitempty"`
}

func WorkWechatContentMsg(address, content string, mentionList []string) error {
	if address == "" || content == "" {
		return errors.New("参数不全")
	}
	header := nhttp.Header{}
	header.Add("Content-Type", "application/json")
	msg := &WorkWechatContent{
		MsgType: "text",
		Text: WorkWechatContentText{
			Content:             content,
			MentionedMobileList: mentionList,
		},
	}
	msgByte, err := json.Marshal(msg)
	if err != nil {
		return err
	}
	_, err = HTTPRequest(address, "POST", header, strings.NewReader(string(msgByte)))
	if err != nil {
		return err
	}
	return nil
}

type Rate struct {
	ID      interface{}
	Percent uint32
	min     uint64
	max     uint64
}

// nolint
func RateMatch(seed uint64, identify string, config []*Rate) interface{} {
	var bucketSize uint64 = 100
	sum := murmur3.SeedStringSum64(seed, identify)
	index := sum % bucketSize
	// convert percent to range，for example:
	// a:20, b:30 convert to a:[0-19], b:å[20-49]
	for i, v := range config {
		if i == 0 {
			v.max = uint64(v.Percent - 1)
		} else {
			v.max = config[i-1].max + uint64(v.Percent)
		}
		v.min = v.max - uint64(v.Percent) + 1
		// determination index range in
		if index >= v.min && index <= v.max {
			return v.ID
		}
	}
	return 0
}

// GetNonceStr 获取随机字符串
// nolint
func GetNonceStr() string {
	dictionary := "abcdefghijklmnopqrstuvwxyz0123456789"
	var b = make([]byte, 32)
	_, _ = rand.Read(b)
	for k, v := range b {
		b[k] = dictionary[v%byte(len(dictionary))]
	}
	return string(b)
}

const (
	XForwardedFor = "X-Forwarded-For"
	XRealIP       = "X-Real-IP"
)

// RemoteIP 返回远程客户端的 IP，如 ***********
func RemoteIP(t *http.Context) string {
	remoteAddr := t.Request.RemoteAddr
	if ip := t.Request.Header.Get(XRealIP); ip != "" {
		remoteAddr = ip
	} else if ip = t.Request.Header.Get(XForwardedFor); ip != "" {
		remoteAddr = ip
	} else {
		remoteAddr, _, _ = net.SplitHostPort(remoteAddr)
	}
	if remoteAddr == "::1" {
		remoteAddr = "127.0.0.1" // nolint
	}
	notLocalhost := func(ip string) bool {
		return ip != "127.0.0.1" && ip != "0.0.0.0"
	}
	formatIP := strings.Split(remoteAddr, ",")
	last := make([]string, 0)
	for _, oIP := range formatIP {
		ip := strings.TrimSpace(oIP)
		if notLocalhost(ip) {
			last = append(last, ip)
		}
	}
	return strings.Join(last, ",")
}

// nolint
func WriteRespose(t *http.Context, r string) {
	rw := t.Writer
	var err error
	_, err = rw.Write([]byte(r))
	if err != nil {
		logger.Warn("Write返回错误", err)
	}
}
func UnmarshalVideoStr(videoStr string) *library.VideoInfo {
	res := &library.VideoInfo{}
	if videoStr == "" {
		return res
	}
	if err := json.Unmarshal([]byte(videoStr), res); err != nil {
		logger.Warn(err)
	}
	return res
}

type SlowAPIDebug struct {
	UID  int64
	Time time.Time
	Logs []string
}

func LogFormat(uid int64, logs []string) {
	for _, v := range logs {
		logger.Infof("trace_id:%d Message:%s", uid, v)
	}
}

func GetTikTokAccessByAppClient(appClient *library.AppClient) *config.DouYinAccess {
	conf := config.Get().GetTikTokAccess("")
	return conf
}

func GetWxMiNiAppAccessByAppClient(appClient *library.AppClient) *config.WechatMiNiApp {
	conf := &config.WechatMiNiApp{}
	switch strconv.Itoa(appClient.Channel) {
	case client.ChannelWechatMiNiApp:
		conf = config.Get().GetWxMiNiAppAccess("")
	default:
	}
	return conf
}

// HTTPRequest HTTP 请求
// nolint
func HTTPRequest(url, method string, header nhttp.Header, body io.Reader) ([]byte, error) {
	// validate
	if url == "" || method == "" || header == nil {
		return nil, fmt.Errorf("请检查请求参数")
	}
	method = strings.ToUpper(method)
	client := new(nhttp.Client)
	tr := &nhttp.Transport{
		DisableKeepAlives: true,
	}
	client.Transport = tr
	r, err := nhttp.NewRequestWithContext(context.Background(), method, url, body)
	if err != nil {
		return nil, err
	}
	r.Header = header
	resp, err := client.Do(r)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	return io.ReadAll(resp.Body)
}
