package util

import "reflect"

// slice求差集
func SliceDiff(slice1, slice2 []int) (result []int) {
	set := make(map[int]bool)
	for _, v := range slice2 {
		set[v] = true
	}
	for _, v := range slice1 {
		if !set[v] {
			result = append(result, v)
		}
	}
	return result
}

// slice去重
func SliceInt64Unique(input []int64) []int64 {
	m := make(map[int64]bool)
	for _, v := range input {
		if !m[v] {
			m[v] = true
		}
	}
	result := make([]int64, 0, len(m))
	for v := range m {
		result = append(result, v)
	}
	return result
}

// DelInt64FromSlice 从int64切片中删除指定元素
func DelInt64FromSlice(source []int64, item int64) []int64 {
	var result []int64
	for _, v := range source {
		if v != item {
			result = append(result, v)
		}
	}
	return result
}

// slice去重
func SliceIntUnique(input []int) []int {
	m := make(map[int]bool)
	for _, v := range input {
		if !m[v] {
			m[v] = true
		}
	}
	result := make([]int, 0, len(m))
	for v := range m {
		result = append(result, v)
	}
	return result
}

// MaxNum 定义一个函数来获取整数切片中的最大值
func MaxNum(nums []int64) int64 {
	if len(nums) == 0 {
		return 0
	}
	max := nums[0]
	for _, num := range nums {
		if num > max {
			max = num
		}
	}
	return max
}

// ContainsElement 使用反射检查数组中是否包含目标元素
func ContainsElement(arr, target interface{}) bool {
	// 使用反射获取数组的值
	arrValue := reflect.ValueOf(arr)
	// 确保 arr 是一个数组
	if arrValue.Kind() != reflect.Array && arrValue.Kind() != reflect.Slice {
		return false
	}
	// 遍历数组，逐一检查元素
	for i := 0; i < arrValue.Len(); i++ {
		// 使用反射获取数组中的元素
		element := arrValue.Index(i).Interface()
		// 使用反射比较元素是否等于目标值
		if reflect.DeepEqual(element, target) {
			return true
		}
	}
	// 没有找到匹配的元素
	return false
}

// SliceDiffInt64 获取切片a与切片b的差集
func SliceDiffInt64(a, b []int64) (result []int64) {
	bMap := make(map[int64]struct{}, len(b))
	for _, v := range b {
		bMap[v] = struct{}{}
	}

	var diff []int64
	for _, v := range a {
		if _, found := bMap[v]; !found {
			diff = append(diff, v)
		}
	}
	return diff
}

// 定义一个通用的去重函数
func RemoveDuplicatesByField(slice interface{}, fieldName string) interface{} {
	// 创建一个map，用于存储已经出现过的字段值
	seen := make(map[interface{}]struct{})
	// 获取输入切片的类型信息
	sliceType := reflect.TypeOf(slice)
	// 获取输入切片的值信息
	sliceValue := reflect.ValueOf(slice)
	// 如果切片类型是指针类型，则解引用
	if sliceType.Kind() == reflect.Ptr {
		sliceValue = sliceValue.Elem()
	}
	// 创建一个新的切片，用于存储去重后的结果
	result := reflect.MakeSlice(sliceType, 0, sliceValue.Len())
	// 遍历原始切片
	for i := 0; i < sliceValue.Len(); i++ {
		sliceData := sliceValue.Index(i)
		if sliceData.Kind() == reflect.Ptr {
			sliceData = sliceValue.Index(i).Elem()
		}
		// 获取当前元素的字段值
		fieldValue := sliceData.FieldByName(fieldName).Interface()
		// 如果当前字段值没有出现过，则将其添加到结果中，并标记为已经出现过
		if _, ok := seen[fieldValue]; !ok {
			result = reflect.Append(result, sliceValue.Index(i))
			seen[fieldValue] = struct{}{}
		}
	}

	return result.Interface()
}
