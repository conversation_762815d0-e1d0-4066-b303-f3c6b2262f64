package product

import (
	"fmt"
	"regexp"
	"sort"
	"strconv"
	"strings"

	dbp "gitlab.dailyyoga.com.cn/server/children/databases/product"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libc "gitlab.dailyyoga.com.cn/server/children/library/client"
	"gitlab.dailyyoga.com.cn/server/children/library/pay"
	libp "gitlab.dailyyoga.com.cn/server/children/library/product"
)

type webProductSku struct{}

var SrvWProductSku webProductSku

type SkuUnit struct {
	ID            int64               `json:"id"`
	Price         string              `json:"price"`
	Name          string              `json:"name"`
	PayType       []int               `json:"pay_type,omitempty"`
	PayTypeInfo   []*pay.TypeInfoItem `json:"pay_type_info,omitempty"`
	IsSubscribe   bool                `json:"is_subscribe"`
	SubscribeDesc string              `json:"subscribe_desc"`
	Selected      bool                `json:"selected"`
	IOSProductID  string              `json:"ios_product_id"`
	DurationDesc  string              `json:"duration_desc"`
	DurationType  int                 `json:"duration_type"`
	DurationValue int                 `json:"duration_value"`
}

type SkuConfig struct {
	ID                int64              `json:"id"`                  // 主表ID
	SkuID             int64              `json:"sku_id"`              // 会员产品主表ID
	Price             float64            `json:"price"`               // 价格
	GroupID           int64              `json:"group_id"`            // 分群ID
	Name              string             `json:"name"`                // 会员产品名称
	SubTitle          string             `json:"sub_title"`           // 副标题
	IntroduceText     string             `json:"introduce_text"`      // 介绍文案
	BuyButtonImg      string             `json:"buy_button_img"`      // 购买按钮图片
	OperateImg        *library.ImageInfo `json:"operate_img"`         // sku运营图片
	Order             int                `json:"order"`               // sku排序
	SubscriptImg      string             `json:"subscript_img"`       // sku角标图片
	SelectBottomImg   string             `json:"select_bottom_img"`   // sku选中底图
	NoSelectBottomImg string             `json:"noselect_bottom_img"` // sku未选中底图
	OperateType       int                `json:"operate_type"`        // 运营类型 1小条 2倒计时
	OperateTitleImg   string             `json:"operate_title_img"`   // sku运营小条图片
	CountdownType     int                `json:"countdown_type"`      // 倒计时单位
	CountdownValue    int                `json:"countdown_value"`     // 倒计时值
}

type VipItem struct {
	Title    string               `json:"title"`     // 标题
	SubTitle string               `json:"sub_title"` // 副标题
	IsShow   int                  `json:"is_show"`   // 是否展示红包 1是 2否
	Priority int32                `json:"priority"`  // 优先级
	Link     *library.LinkContent `json:"link"`      // 链接
}

// FormatVipCenterSkuItem 整理付费方案页的sku信息
// nolint
func (w *webProductSku) FormatVipCenterSkuItem(product *dbp.WebProduct,
	appClient *library.AppClient, obChannel *ObUserChannel) *SkuUnit {
	if product == nil {
		return &SkuUnit{}
	}
	sku := &SkuUnit{
		ID:            product.ID,
		Name:          product.Name,
		Price:         strconv.FormatFloat(product.Price, 'f', 2, 64),
		IsSubscribe:   product.IsSubscribe == library.Yes,
		IOSProductID:  product.IOSProductID,
		DurationDesc:  product.GetDurationDesc(),
		DurationType:  product.DurationType,
		DurationValue: product.DurationValue,
	}
	if strings.Contains(sku.Price, ".") {
		sku.Price = strings.TrimRight(strings.TrimRight(sku.Price, "0"), ".")
	}
	if sku.IsSubscribe && (strconv.Itoa(appClient.Channel) == libc.ChannelHuawei ||
		strconv.Itoa(appClient.Channel) == libc.ChannelHarmonyHuawei) {
		sku.SubscribeDesc = product.SubscribeDesc
	}
	if sku.IsSubscribe && appClient.OsType == int(libc.DeviceTypeEnum.IOS) && obChannel.Compliance {
		desc := product.GetDurationDesc()
		re := regexp.MustCompile(`[0-9]+`) // nolint
		if !re.MatchString(desc) {
			desc = "一" + desc
		}
		sku.SubscribeDesc = fmt.Sprintf(libp.IosSubscribeDesc,
			product.GetDurationDesc(), product.GetDurationDesc(), desc)
	}
	if sku.IsSubscribe && appClient.OsType == int(libc.DeviceTypeEnum.Android) &&
		obChannel.AboutAudit.IsInAudit {
		sku.SubscribeDesc = libp.AndroidSubscribeDesc
	}
	payTypeList := strings.Split(product.PayType, ",")
	if len(payTypeList) == 0 {
		return &SkuUnit{}
	}
	sku.PayType = w.FormatSkuPayType(sku, payTypeList, appClient, obChannel)
	for _, v := range sku.PayType {
		if t, ok := pay.TypeInfoMap[v]; ok {
			sku.PayTypeInfo = append(sku.PayTypeInfo, t)
		}
	}
	return sku
}

func (w *webProductSku) FormatSkuPayType(skuItem *SkuUnit, payTypeList []string, appClient *library.AppClient,
	obChannel *ObUserChannel) []int {
	valid := w.getValidPayType(payTypeList, appClient)
	// IOS 审核状态下
	if appClient.OsType == int(libc.DeviceTypeEnum.IOS) {
		if obChannel.Compliance || obChannel.AboutAudit.IsInAudit {
			valid = []int{pay.PayTypeApple}
		} else {
			valid = w.GetIosPayType(valid, skuItem, appClient, obChannel)
		}
		if len(valid) == 0 {
			valid = []int{pay.PayTypeApple}
		}
	}
	// 没有安装支付宝 走微信
	if (appClient.OsType == int(libc.DeviceTypeEnum.Android) ||
		appClient.OsType == int(libc.DeviceTypeEnum.Harmony)) && !obChannel.IsInstallAp {
		return []int{pay.PayTypeWechat}
	}
	sort.Slice(valid, func(i, j int) bool {
		return valid[i] > valid[j]
	})
	return valid
}

func (w *webProductSku) GetIosPayType(valid []int, skuItem *SkuUnit, appClient *library.AppClient,
	obChannel *ObUserChannel) []int {
	// 非渠道用户或者ios渠道走iap开关开着 直接返回
	if obChannel.UserChannel == libc.UserChanneDef || obChannel.AboutAudit.IsIOSChannelIAP {
		return []int{pay.PayTypeApple}
	}
	if skuItem.IsSubscribe {
		if !obChannel.IsInstallAp || obChannel.AboutAudit.IsInAudit {
			return []int{pay.PayTypeApple}
		}
		// 渠道用户IOS IAP开关判断是否返回支付宝
		if !obChannel.Compliance {
			for _, v := range valid {
				if v == pay.PayTypeAlipay {
					return []int{pay.PayTypeAlipay}
				}
			}
		}
		return []int{pay.PayTypeApple}
	}
	resPay := make([]int, 0)
	for _, v := range valid {
		if v == pay.PayTypeAlipay || v == pay.PayTypeWechat {
			if !obChannel.IsInstallAp && v == pay.PayTypeAlipay {
				continue
			}
			resPay = append(resPay, v)
		}
	}
	return resPay
}

func (w *webProductSku) getValidPayType(payTypeList []string, appClient *library.AppClient) []int {
	valid := make([]int, 0)
	for _, v := range payTypeList {
		iv, err := strconv.Atoi(v)
		if err != nil || iv == 0 {
			continue
		}
		if pl, ok := libc.DeviceTypeToPayType[libc.DeviceTypeInt(appClient.OsType)]; ok {
			for _, v := range pl {
				if v == iv {
					valid = append(valid, iv)
				}
			}
		}
	}
	return valid
}
