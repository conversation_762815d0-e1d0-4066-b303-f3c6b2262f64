package product

import (
	"fmt"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"sync"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/server/children/databases/obpay"
	dbp "gitlab.dailyyoga.com.cn/server/children/databases/product"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libc "gitlab.dailyyoga.com.cn/server/children/library/client"
	"gitlab.dailyyoga.com.cn/server/children/library/pay"
	libp "gitlab.dailyyoga.com.cn/server/children/library/product"
	"gitlab.dailyyoga.com.cn/server/children/service/audit"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type webProduct struct{}

var SrvWProduct webProduct

func (w *webProduct) GetPayPageSku(productID int64, appClient *library.AppClient,
	obChannel *ObUserChannel) *library.SkuItem {
	product := dbp.TbWebProduct.GetItemByID(productID)
	if product == nil {
		return nil
	}
	sku := &library.SkuItem{
		ID:           product.ID,
		Name:         product.Name,
		Price:        strconv.FormatFloat(product.Price, 'f', 2, 64),
		IsSubscribe:  product.IsSubscribe == library.Yes,
		IOSProductID: product.IOSProductID,
		DurationDesc: product.GetDurationDesc(),
		ProductType:  product.ProductType,
		VipType:      product.VipType,
		DurationDay:  product.GetDurationDay(),
	}
	payTypeList := strings.Split(product.PayType, ",")
	if len(payTypeList) == 0 {
		return &library.SkuItem{}
	}
	sku.PayType = w.FormatSkuPayType(sku, payTypeList, appClient, obChannel)
	for _, v := range sku.PayType {
		if t, ok := pay.TypeInfoMap[v]; ok {
			t.Icon = util.GetOnlineImageURL(t.Icon)
			sku.PayTypeInfo = append(sku.PayTypeInfo, t)
		}
	}
	return sku
}

type ObUserChannel struct {
	UserChannel int
	IsInstallAp bool
	UID         int64
	AboutAudit  *audit.AboutAudit
	Compliance  bool
}

func GetObUserChannel(appClient *library.AppClient, uid int64, isInstallAp bool) *ObUserChannel {
	var userChannel int
	var aboutAudit *audit.AboutAudit
	var compliance bool
	var wg sync.WaitGroup
	wg.Add(3) //nolint
	safelygo.GoSafelyByTraceID(func() {
		defer wg.Done()
		userChannel = audit.GetChannel(appClient, uid)
	})
	safelygo.GoSafelyByTraceID(func() {
		defer wg.Done()
		aboutAudit = audit.GetAuditInfo(appClient)
	})
	safelygo.GoSafelyByTraceID(func() {
		defer wg.Done()
		compliance = audit.GetCompliance(uid, appClient)
	})
	wg.Wait()
	return &ObUserChannel{
		UserChannel: userChannel,
		IsInstallAp: isInstallAp,
		UID:         uid,
		AboutAudit:  aboutAudit,
		Compliance:  compliance,
	}
}

func AppleSupportOtherPay(uid int64, appClient *library.AppClient, compliance bool) bool {
	if appClient.OsType != int(libc.DeviceTypeEnum.IOS) {
		return true
	}
	if compliance {
		return false
	}
	if audit.GetChannel(appClient, uid) == libc.UserChanneDef || audit.GetAuditInfo(appClient).IsIOSChannelIAP {
		return false
	}
	return true
}

func (w *webProduct) FormatSkuPayType(skuItem *library.SkuItem, payTypeList []string, appClient *library.AppClient,
	obChannel *ObUserChannel) []int {
	if appClient.OsType == int(libc.DeviceTypeEnum.Harmony) {
		return []int{pay.PayTypeAlipay}
	}
	valid := w.getValidPayType(payTypeList, appClient)
	// IOS 审核状态下
	if appClient.OsType == int(libc.DeviceTypeEnum.IOS) {
		if obChannel.Compliance || obChannel.AboutAudit.IsInAudit {
			valid = []int{pay.PayTypeApple}
		} else {
			valid = w.GetIosPayType(valid, skuItem, appClient, obChannel)
		}
		if len(valid) == 0 {
			valid = []int{pay.PayTypeApple}
		}
	}
	logger.Infof("付费方案页：appClient %+v obChannel  %+v", appClient, obChannel)
	// 没有安装支付宝 走微信
	if (appClient.OsType == int(libc.DeviceTypeEnum.Android) ||
		appClient.OsType == int(libc.DeviceTypeEnum.Harmony)) && !obChannel.IsInstallAp {
		return []int{pay.PayTypeWechat}
	}
	sort.Slice(valid, func(i, j int) bool {
		return valid[i] > valid[j]
	})
	return valid
}

func (w *webProduct) GetIosPayType(valid []int, skuItem *library.SkuItem, appClient *library.AppClient,
	obChannel *ObUserChannel) []int {
	// 非渠道用户或者ios渠道走iap开关开着 直接返回
	if obChannel.UserChannel == libc.UserChanneDef || obChannel.AboutAudit.IsIOSChannelIAP {
		return []int{pay.PayTypeApple}
	}
	if skuItem.IsSubscribe {
		if !obChannel.IsInstallAp || obChannel.AboutAudit.IsInAudit {
			return []int{pay.PayTypeApple}
		}
		// 渠道用户IOS IAP开关判断是否返回支付宝
		if !obChannel.Compliance {
			for _, v := range valid {
				if v == pay.PayTypeAlipay {
					return []int{pay.PayTypeAlipay}
				}
			}
		}
		return []int{pay.PayTypeApple}
	}
	resPay := make([]int, 0)
	for _, v := range valid {
		if v == pay.PayTypeAlipay || v == pay.PayTypeWechat {
			if !obChannel.IsInstallAp && v == pay.PayTypeAlipay {
				continue
			}
			resPay = append(resPay, v)
		}
	}
	return resPay
}

func (w *webProduct) getValidPayType(payTypeList []string, appClient *library.AppClient) []int {
	valid := make([]int, 0)
	for _, v := range payTypeList {
		iv, err := strconv.Atoi(v)
		if err != nil || iv == 0 {
			continue
		}
		if pl, ok := libc.DeviceTypeToPayType[libc.DeviceTypeInt(appClient.OsType)]; ok {
			for _, v := range pl {
				if v == iv {
					valid = append(valid, iv)
				}
			}
		}
	}
	return valid
}

// FormatPagPageSkuItem 整理付费方案页的sku信息
// nolint
func (w *webProduct) FormatPagPageSkuItem(psku *obpay.PageSku, product *dbp.WebProduct,
	appClient *library.AppClient, obChannel *ObUserChannel) *library.SkuItem {
	if product == nil || psku.ProductID <= 0 {
		return &library.SkuItem{}
	}
	sku := &library.SkuItem{
		ID:           psku.ProductID,
		Img:          util.UnmarshalImageStr(psku.Img),
		SelectImg:    util.UnmarshalImageStr(psku.SelectImg),
		Selected:     psku.IsSelected == library.Yes,
		IOSProductID: product.IOSProductID,
		ProductType:  product.ProductType,
		VipType:      product.VipType,
		DurationDay:  product.GetDurationDay(),
	}
	sku.Name = product.Name
	sku.Price = strconv.FormatFloat(product.Price, 'f', 2, 64)
	if strings.Contains(sku.Price, ".") {
		sku.Price = strings.TrimRight(strings.TrimRight(sku.Price, "0"), ".")
	}
	sku.IsSubscribe = product.IsSubscribe == library.Yes
	if sku.IsSubscribe && strconv.Itoa(appClient.Channel) == libc.ChannelHuawei {
		sku.SubscribeDesc = product.SubscribeDesc
	}
	if sku.IsSubscribe && appClient.OsType == int(libc.DeviceTypeEnum.IOS) && obChannel.Compliance {
		desc := product.GetDurationDesc()
		re := regexp.MustCompile(`[0-9]+`) // nolint
		if !re.MatchString(desc) {
			desc = "一" + desc
		}
		sku.SubscribeDesc = fmt.Sprintf(libp.IosSubscribeDesc,
			product.GetDurationDesc(), product.GetDurationDesc(), desc)
	}
	if sku.IsSubscribe && appClient.OsType == int(libc.DeviceTypeEnum.Android) &&
		obChannel.AboutAudit.IsInAudit {
		sku.SubscribeDesc = libp.AndroidSubscribeDesc
	}
	sku.DurationDesc = product.GetDurationDesc()
	payTypeList := strings.Split(product.PayType, ",")
	if len(payTypeList) == 0 {
		return &library.SkuItem{}
	}
	sku.PayType = w.FormatSkuPayType(sku, payTypeList, appClient, obChannel)
	for _, v := range sku.PayType {
		if t, ok := pay.TypeInfoMap[v]; ok {
			sku.PayTypeInfo = append(sku.PayTypeInfo, t)
		}
	}
	return sku
}
