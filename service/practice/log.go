package practice

import (
	"gitlab.dailyyoga.com.cn/server/children/databases/course"
	"gitlab.dailyyoga.com.cn/server/children/databases/practice"
)

type log struct{}

var SrvLog log

type PlayLog struct {
	ID                int64  `json:"id"`
	CourseID          int64  `json:"course_id"`
	Title             string `json:"title"`
	PracticeStartTime int64  `json:"practice_start_time"`
	PracticeSeconds   int    `json:"practice_seconds"`
	PracticeCalorie   int    `json:"practice_calorie"`
}

func (l *log) FormatPracticeLog(plog *practice.Log) *PlayLog {
	if plog == nil {
		return nil
	}
	item := &PlayLog{
		ID:                plog.ID,
		CourseID:          plog.CourseID,
		PracticeStartTime: plog.PracticeStartTime,
		PracticeSeconds:   plog.PlayTime,
		PracticeCalorie:   plog.Calories,
	}
	courseItem := course.TbCourseLibrary.GetItem(plog.CourseID)
	if courseItem != nil {
		item.Title = courseItem.Title
	}
	return item
}
