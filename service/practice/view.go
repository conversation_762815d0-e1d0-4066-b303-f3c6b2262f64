package practice

import (
	"fmt"
	"math"
	"time"

	"gitlab.dailyyoga.com.cn/server/children/databases/practice"
	"gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type view struct{}

var SrvView view

type IntervalViewItem struct {
	DateValueText   string `json:"date_value_text"`
	StartTime       int64  `json:"start_time"`
	EndTime         int64  `json:"end_time"`
	Calorie         int64  `json:"calorie"`
	PracticeMinutes int64  `json:"practice_minutes"`
	PracticeSecond  int64  `json:"practice_second"`
	PracticeCount   int64  `json:"practice_count"`
	PracticeDays    int64  `json:"practice_days"`
}

// 获取练习日期看板
func (*view) GetPracticeDate(uid int64, isApp bool) []*IntervalViewItem {
	dataRange := getDateByRange(getOneYearAgo(uid), time.Now().Unix())
	res := make([]*IntervalViewItem, 0)
	start, end := getStartEndFromInterval(dataRange)
	practiceMap := practice.TbPlayDay.GetMapByTimeRange(uid, start, end)
	for k, v := range dataRange {
		item := &IntervalViewItem{
			DateValueText: fmt.Sprintf("%s日", time.Unix(v.StartTime, 0).Format("1.2")),
			StartTime:     v.StartTime,
			EndTime:       v.EndTime,
		}
		practiceItem, ok := practiceMap[time.Unix(v.StartTime, 0).Format("20060102")]
		if ok && practiceItem != nil {
			item.Calorie = int64(practiceItem.Calorie)
			item.PracticeMinutes = int64(math.Floor(float64(practiceItem.PlayTime) / util.SecondsPerMinute))
			item.PracticeSecond = practiceItem.PlayTime
			item.PracticeCount = practiceItem.PracticeCount
		} else if k != len(dataRange)-1 {
			continue
		}
		res = append(res, item)
	}
	// 判断最近七天是否有数据
	sevenHasData := false
	sevenDayAgo := util.FormatStartTime(time.Now().Unix() - 6*86400)
	for _, v := range res {
		if v.StartTime >= sevenDayAgo && v.PracticeMinutes > 0 {
			sevenHasData = true
		}
	}
	if isApp && !sevenHasData {
		return nil
	}
	return res
}

// 获取练习周看板
func (*view) GetPracticeWeek(uid int64) []*IntervalViewItem {
	dataRange := getWeekByRange(getOneYearAgo(uid), time.Now().Unix())
	res := make([]*IntervalViewItem, 0)
	start, end := getStartEndFromInterval(dataRange)
	practiceMap := practice.TbPlayDay.GetMapByTimeRange(uid, start, end)
	for k, v := range dataRange {
		item := &IntervalViewItem{
			DateValueText: fmt.Sprintf("%s-%s", time.Unix(v.StartTime, 0).Format("1.2"),
				time.Unix(v.EndTime, 0).Format("1.2")),
			StartTime: v.StartTime,
			EndTime:   v.EndTime,
		}
		weekStart := v.StartTime
		for weekStart <= v.EndTime {
			practiceItem, ok := practiceMap[time.Unix(weekStart, 0).Format("20060102")]
			if ok && practiceItem != nil {
				item.Calorie += int64(practiceItem.Calorie)
				item.PracticeMinutes += int64(math.Floor(float64(practiceItem.PlayTime) / util.SecondsPerMinute))
				item.PracticeCount += practiceItem.PracticeCount
				item.PracticeDays++
			}
			weekStart += 86400
		}
		if item.PracticeDays <= 0 && k != len(dataRange)-1 {
			continue
		}
		res = append(res, item)
	}
	return res
}

// 获取练习月看板
func (*view) GetPracticeMonth(uid int64) []*IntervalViewItem {
	dataRange := getMonthByRange(getOneYearAgo(uid), time.Now().Unix())
	res := make([]*IntervalViewItem, 0)
	start, end := getStartEndFromInterval(dataRange)
	practiceMap := practice.TbPlayDay.GetMapByTimeRange(uid, start, end)
	for k, v := range dataRange {
		item := &IntervalViewItem{
			DateValueText: fmt.Sprintf("%s月", time.Unix(v.StartTime, 0).Format("1")),
			StartTime:     v.StartTime,
			EndTime:       v.EndTime,
		}
		monthStart := v.StartTime
		for monthStart <= v.EndTime {
			practiceItem, ok := practiceMap[time.Unix(monthStart, 0).Format("20060102")]
			if ok && practiceItem != nil {
				item.Calorie += int64(practiceItem.Calorie)
				item.PracticeMinutes += int64(math.Floor(float64(practiceItem.PlayTime) / util.SecondsPerMinute))
				item.PracticeCount += practiceItem.PracticeCount
				item.PracticeDays++
			}
			monthStart += 86400
		}
		if item.PracticeDays <= 0 && k != len(dataRange)-1 {
			continue
		}
		res = append(res, item)
	}
	return res
}

// 获取练习月看板
func (*view) GetPracticeYear(uid, startTime int64) []*IntervalViewItem {
	if startTime == 0 {
		startTime = getOneYearAgo(uid)
	}
	dataRange := getYearByRange(startTime, time.Now().Unix())
	res := make([]*IntervalViewItem, 0)
	start, end := getStartEndFromInterval(dataRange)
	practiceMap := practice.TbPlayDay.GetMapByTimeRange(uid, start, end)
	for k, v := range dataRange {
		item := &IntervalViewItem{
			DateValueText: fmt.Sprintf("%s年", time.Unix(v.StartTime, 0).Format("2006")),
			StartTime:     v.StartTime,
			EndTime:       v.EndTime,
		}
		monthStart := v.StartTime
		for monthStart <= v.EndTime {
			practiceItem, ok := practiceMap[time.Unix(monthStart, 0).Format("20060102")]
			if ok && practiceItem != nil {
				item.Calorie += int64(practiceItem.Calorie)
				item.PracticeMinutes += int64(math.Floor(float64(practiceItem.PlayTime) / util.SecondsPerMinute))
				item.PracticeCount += practiceItem.PracticeCount
				item.PracticeDays++
			}
			monthStart += 86400
		}
		if item.PracticeDays <= 0 && k != len(dataRange)-1 {
			continue
		}
		res = append(res, item)
	}
	return res
}

func getStartEndFromInterval(dataRange []IntervalRange) (start, end int64) {
	for _, v := range dataRange {
		if start == 0 || v.StartTime < start {
			start = v.StartTime
		}
		if end == 0 || v.EndTime > end {
			end = v.EndTime
		}
	}
	return start, end
}

// nolint
func getWeekByRange(startTimestamp, endTimestamp int64) []IntervalRange {
	startTime := time.Unix(startTimestamp, 0)
	endTime := time.Unix(endTimestamp, 0)

	var weekRange []IntervalRange
	startWeekMonday := startTime.AddDate(0, 0, -int(startTime.Weekday())+1)
	// 遍历两个时间戳之间的每一周
	currentTime := startWeekMonday
	for !currentTime.After(endTime) {
		weekStart := currentTime.Unix()
		weekEnd := currentTime.Add(6 * 24 * time.Hour).Unix()
		weekRange = append(weekRange, IntervalRange{StartTime: weekStart, EndTime: util.FormatEndTime(weekEnd)})
		// 将时间向后推移一周
		currentTime = currentTime.AddDate(0, 0, 7)
	}
	return weekRange
}

// nolint
func getMonthByRange(startTimestamp, endTimestamp int64) []IntervalRange {
	startTime := time.Unix(startTimestamp, 0)
	endTime := time.Unix(endTimestamp, 0)
	var monthRange []IntervalRange
	loc, _ := time.LoadLocation(library.TimeZoneBeijing)
	// 获取开始时间所在的月的第一天
	startMonthFirstDay := time.Date(startTime.Year(), startTime.Month(), 1, 0, 0, 0, 0, loc)
	// 遍历两个时间戳之间的每个月
	currentTime := startMonthFirstDay
	for !currentTime.After(endTime) {
		monthStart := currentTime.Unix()
		// 获取下个月的第一天
		nextMonth := currentTime.AddDate(0, 1, 0)
		nextMonthFirstDay := time.Date(nextMonth.Year(), nextMonth.Month(), 1, 0, 0, 0, 0, loc)
		// 上一个时间单位就是这个时间的下一个时间单位减一
		monthEnd := nextMonthFirstDay.Add(-time.Nanosecond).Unix()
		monthRange = append(monthRange, IntervalRange{StartTime: monthStart, EndTime: monthEnd})

		// 将时间推移到下个月的第一天
		currentTime = nextMonthFirstDay
	}
	return monthRange
}

// nolint
func getYearByRange(startTimestamp, endTimestamp int64) []IntervalRange {
	startTime := time.Unix(startTimestamp, 0)
	endTime := time.Unix(endTimestamp, 0)
	var yearRange []IntervalRange
	loc, _ := time.LoadLocation(library.TimeZoneBeijing)
	// 获取开始时间所在年的第一天
	startYearFirstDay := time.Date(startTime.Year(), time.January, 1, 0, 0, 0, 0, loc)
	// 遍历两个时间戳之间的每一年
	currentTime := startYearFirstDay
	for !currentTime.After(endTime) {
		yearStart := currentTime.Unix()
		// 获取下一年的第一天
		nextYear := currentTime.AddDate(1, 0, 0)
		nextYearFirstDay := time.Date(nextYear.Year(), time.January, 1, 0, 0, 0, 0, loc)
		// 上一个时间单位就是这个时间的下一个时间单位减一
		yearEnd := nextYearFirstDay.Add(-time.Nanosecond).Unix()
		yearRange = append(yearRange, IntervalRange{StartTime: yearStart, EndTime: yearEnd})

		// 将时间推移到下一年的第一天
		currentTime = nextYearFirstDay
	}
	return yearRange
}

func getOneYearAgo(uid int64) int64 {
	accItem := user.TbAccount.GetUserByID(uid)
	currentTime := time.Now().Unix()
	oneYearAgo := currentTime - (365 * 24 * 60 * 60) // 365天 * 24小时 * 60分钟 * 60秒
	oneYearAgoTime := util.FormatStartTime(oneYearAgo)
	if accItem != nil && accItem.CreateTime > oneYearAgoTime {
		oneYearAgoTime = util.FormatStartTime(accItem.CreateTime)
	}
	return oneYearAgoTime
}

type IntervalRange struct {
	StartTime int64 // 天、周、月、年的开始时间戳
	EndTime   int64
}

// nolint
func getDateByRange(startTimestamp, endTimestamp int64) []IntervalRange {
	startTime := time.Unix(startTimestamp, 0)
	endTime := time.Unix(endTimestamp, 0)
	var dateRange []IntervalRange
	currentTime := startTime
	for !currentTime.After(endTime) {
		currentStart := currentTime.Unix()
		currentEnd := util.FormatEndTime(currentStart)
		dateRange = append(dateRange, IntervalRange{StartTime: currentStart, EndTime: currentEnd})
		// 将时间向后推移一天
		currentTime = currentTime.AddDate(0, 0, 1)
	}
	return dateRange
}
