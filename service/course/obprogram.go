package course

import (
	"context"
	"encoding/json"
	"sort"
	"strconv"
	"sync"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/protogen/children-user-group-go/childrenusergroup"
	dbclient "gitlab.dailyyoga.com.cn/server/children/databases/client"
	dbcourse "gitlab.dailyyoga.com.cn/server/children/databases/course"
	"gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/grpc"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/client"
	libc "gitlab.dailyyoga.com.cn/server/children/library/course"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	libuser "gitlab.dailyyoga.com.cn/server/children/library/user"
	"gitlab.dailyyoga.com.cn/server/children/service/rop"
	"gitlab.dailyyoga.com.cn/server/children/service/sensor"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type ObProgramDetail struct {
	ProgramID   int64      `json:"program_id"`
	CourseCount int32      `json:"course_count"`
	FinishCount int32      `json:"finish_count"`
	StartTime   int64      `json:"start_time"`
	TodayTime   int64      `json:"today_time"`
	TemplateID  int64      `json:"schedule_template_id"`
	DayList     []*DayList `json:"day_list,omitempty"`
}
type DayList struct {
	PracticeDate    int64         `json:"practice_date"`
	IsToday         bool          `json:"is_today"`
	PracticeDateStr string        `json:"practice_date_str"`
	IsPractice      int32         `json:"is_practice"`
	OrderDay        int32         `json:"order_day"`
	CourseItem      *ItemCourse   `json:"course_item,omitempty"`
	FinishCourseIDs []int64       `json:"finish_course_ids,omitempty"`
	CourseList      []*ItemCourse `json:"course_list,omitempty"`
}

// nolint
func GetObProgramDetail(uid int64, appClient *library.AppClient, constsCheduleID int64) *ObProgramDetail {
	inProcessProgram := dbcourse.TbObProgram.GetInProcessProgram(uid)
	if inProcessProgram == nil {
		if err := CreateObProgram(uid, appClient, &ObUserChoice{}, constsCheduleID); err != errorcode.Success {
			return nil
		}
		inProcessProgram = dbcourse.TbObProgram.GetInProcessProgram(uid)
	}
	if inProcessProgram == nil {
		return nil
	}
	programCourseList := dbcourse.TbObProgramCourse.GetList(inProcessProgram.UID, inProcessProgram.ID)
	sort.Slice(programCourseList, func(i, j int) bool {
		return programCourseList[i].Day < programCourseList[j].Day
	})
	if programCourseList[len(programCourseList)-1].IsPractice == library.Yes &&
		programCourseList[len(programCourseList)-1].PracticeDate == util.FormatStartTime(time.Now().Unix()-86400) {
		RestartObProgram(uid, inProcessProgram.ID)
		inProcessProgram = dbcourse.TbObProgram.GetInProcessProgram(uid)
	}
	choiceTmp := ObUserChoice{}
	scheduleID := int64(0)
	if err := json.Unmarshal([]byte(inProcessProgram.ChoiceData), &choiceTmp); err == nil {
		scheduleID = choiceTmp.ScheduleID
	}
	res := &ObProgramDetail{
		TodayTime:   time.Now().Unix(),
		ProgramID:   inProcessProgram.ID,
		CourseCount: 28,
		FinishCount: inProcessProgram.FinishCount,
		StartTime:   util.FormatStartTime(inProcessProgram.BeginTime),
		TemplateID:  scheduleID,
	}
	programCourseList = dbcourse.TbObProgramCourse.GetList(inProcessProgram.UID, inProcessProgram.ID)
	sort.Slice(programCourseList, func(i, j int) bool {
		return programCourseList[i].Day < programCourseList[j].Day
	})
	var (
		maxDay           int32
		pushBeginDay     int32
		needPushSecond   int64
		programCourseMap = make(map[int32][]*dbcourse.ObProgramCourse) // 第几天对应的课程
	)
	for _, v := range programCourseList {
		programCourseMap[v.Day] = append(programCourseMap[v.Day], v)
		if v.Day > maxDay {
			maxDay = v.Day
		}
	}
	// 对map进行排序
	keys := make([]int32, 28)
	for i := range keys {
		keys[i] = int32(i + 1)
	}
	for _, k := range keys {
		// 如果一天内的两节课有一节练习了，就不推移
		if courses, ok := programCourseMap[k]; ok {
			isPush := true
			for _, c := range courses {
				if c.IsPractice == library.Yes {
					isPush = false
				}
			}
			if isPush && courses[0].PracticeDate < util.FormatStartTime(time.Now().Unix()) {
				needPushSecond = util.FormatStartTime(time.Now().Unix()) - courses[0].PracticeDate
				pushBeginDay = courses[0].Day
				break
			}
		}
	}
	// 如果需要推移，则推移
	if needPushSecond > 0 && pushBeginDay > 0 {
		if err := dbcourse.TbObProgramCourse.PushPracticeDate(inProcessProgram.UID, inProcessProgram.ID, pushBeginDay, needPushSecond); err != nil {
			logger.Error(err)
			return res
		}
		for k := range programCourseMap {
			if courses, ok := programCourseMap[k]; ok {
				for i := range courses {
					if programCourseMap[k][i].Day >= pushBeginDay {
						programCourseMap[k][i].PracticeDate += needPushSecond
					}
				}
			}
		}
	}
	res.DayList = GetObProgramCourseList(uid, programCourseMap, maxDay, appClient)
	replaceCurriculumInfo(res)
	return res
}

func replaceCurriculumInfo(obDetail *ObProgramDetail) {
	if len(obDetail.DayList) == 0 || obDetail.TemplateID == 0 {
		return
	}
	obProSchTable := dbcourse.TbObProSchTable.GetItemByID(obDetail.TemplateID)
	sessionMap := make(map[int][]*DaySession)
	sessionArr := make([]*SessionArr, 0)
	if err := json.Unmarshal([]byte(obProSchTable.SessionList), &sessionArr); err != nil {
		return
	}
	for i, v := range sessionArr {
		if _, ok := sessionMap[i]; !ok {
			sessionMap[i] = make([]*DaySession, 0)
		}
		sessionMap[i] = append(sessionMap[i], v.DaySessionList...)
	}
	for k := range obDetail.DayList {
		if obDetail.DayList[k].OrderDay == 0 {
			continue
		}
		sessionList, ok := sessionMap[int(obDetail.DayList[k].OrderDay)-1]
		if !ok {
			continue
		}
		if len(obDetail.DayList[k].CourseList) != len(sessionList) {
			continue
		}
		for i := range obDetail.DayList[k].CourseList {
			if sessionList[i].SessionID != obDetail.DayList[k].CourseList[i].ID {
				continue
			}
			if sessionList[i].Alias != "" {
				obDetail.DayList[k].CourseList[i].Title = sessionList[i].Alias
			}
			if sessionList[i].CoverImg != "" {
				obDetail.DayList[k].CourseList[i].CoverURL = sessionList[i].CoverImg
			}
		}
	}
}

// nolint
func GetObProgramCourseList(uid int64, programCourseMap map[int32][]*dbcourse.ObProgramCourse, maxDay int32,
	appClient *library.AppClient) []*DayList {
	res := make([]*DayList, 0)
	if len(programCourseMap) == 0 {
		return res
	}
	// 对map进行排序
	keys := make([]int32, 28)
	for i := range keys {
		keys[i] = int32(i + 1)
	}
	courseIDArr := make([]int64, 0)
	for k := range programCourseMap {
		if courses, ok := programCourseMap[k]; ok {
			for _, v := range courses {
				courseIDArr = append(courseIDArr, v.CourseID)
			}
		}
	}
	srvCourse := &SrvOptionCourse{}
	courseList := srvCourse.BatchGetCourseDetail(uid, courseIDArr, appClient, &libc.OptionCourse{
		IsOnline: library.No,
	})
	courseListMap := make(map[int64]*ItemCourse)
	for _, v := range courseList {
		courseListMap[v.ID] = v
	}
	programCourseMapSort := make(map[int32][]*dbcourse.ObProgramCourse)
	var wg sync.WaitGroup
	var lock sync.Mutex
	for _, k := range keys {
		val := k
		wg.Add(1)
		safelygo.GoSafelyByTraceID(
			func() {
				defer wg.Done()
				courses, ok := programCourseMap[val]
				if !ok {
					return
				}
				sort.Slice(courses, func(i, j int) bool {
					return courses[i].ID < courses[j].ID
				})
				lock.Lock()
				defer lock.Unlock()
				programCourseMapSort[val] = courses
			})
	}
	wg.Wait()
	for _, kk := range keys {
		dayData := &DayList{
			PracticeDate:    util.FormatStartTime(programCourseMapSort[kk][0].PracticeDate),
			PracticeDateStr: time.Unix(programCourseMapSort[kk][0].PracticeDate, 0).Format("2006-01-02"),
		}
		startTime := programCourseMapSort[kk][0].PracticeDate
		if util.FormatStartTime(programCourseMapSort[kk][0].PracticeDate) == util.FormatStartTime(time.Now().Unix()) {
			dayData.IsToday = true
		}
		for _, k := range keys {
			if courses, ok := programCourseMapSort[k]; ok {
				practiceFound := false
				for _, v := range courses {
					if util.FormatStartTime(v.PracticeDate) == util.FormatStartTime(startTime) {
						dayData.OrderDay = v.Day
						courseItem := courseListMap[v.CourseID]
						if courseItem == nil {
							continue
						}
						dayData.CourseList = append(dayData.CourseList, courseItem)
						if v.IsPractice == library.Yes {
							practiceFound = true
							dayData.FinishCourseIDs = append(dayData.FinishCourseIDs, v.CourseID)
							if dayData.CourseItem == nil {
								dayData.IsPractice = v.IsPractice
								dayData.CourseItem = courseItem
							}
						}
					}
				}
				if !practiceFound && len(courses) > 0 {
					v := courses[0]
					courseItem := courseListMap[v.CourseID]
					if courseItem == nil {
						continue
					}
					if util.FormatStartTime(v.PracticeDate) == util.FormatStartTime(startTime) {
						dayData.IsPractice = v.IsPractice
						dayData.CourseItem = courseItem
					}
				}
			}
		}
		res = append(res, dayData)
	}
	return res
}

type ObUserChoice struct {
	Character      string                 `json:"character"`
	Constitution   string                 `json:"constitution"`
	Dining         int                    `json:"dining"`
	Gender         int                    `json:"gender"`
	Height         int                    `json:"height"`
	Posture        string                 `json:"posture"`
	Run            int                    `json:"run"`
	SID            string                 `json:"sid"`
	Sign           string                 `json:"sign"`
	Sleep          int                    `json:"sleep"`
	SportFrequency int                    `json:"sport_frequency"`
	SportProblem   string                 `json:"sport_problem"`
	SportPurpose   libc.FitnessPurposeInt `json:"sport_purpose"`
	Time           int64                  `json:"time"`
	Timezone       int                    `json:"timezone"`
	UID            int64                  `json:"uid"`
	Uttanasana     int                    `json:"uttanasana"`
	Weight         int                    `json:"weight"`
	Birthday       string                 `json:"birthday"`
	ScheduleID     int64                  `json:"schedule_id"`
	Age            int                    `json:"age"`
}

// UpdateAccountByObChoice 根据OB的选择更新用户信息
func UpdateAccountByObChoice(uid int64, choice *ObUserChoice) bool {
	// 更新用户信息
	userDetail := user.TbAccount.GetUserByID(uid)
	if userDetail == nil || userDetail.ID == 0 {
		return false
	}
	isNeedUpdate := false
	if choice.Gender > 0 {
		userDetail.Gender = choice.Gender
		isNeedUpdate = true
	}
	if isNeedUpdate {
		if err := userDetail.Update(); err != nil {
			logger.Error(err)
			return false
		}
	}
	return true
}

// CreateObProgram 创建Ob课表
// nolint
func CreateObProgram(uid int64, appClient *library.AppClient, choice *ObUserChoice, constsCheduleID int64) errorcode.ErrorCode {
	logger.Info("CreateObProgram", uid, constsCheduleID)
	scheduleID := int64(0)
	inProcessProgram := dbcourse.TbObProgram.GetInProcessProgram(uid)
	if inProcessProgram != nil && inProcessProgram.ID > 0 {
		choiceTmp := ObUserChoice{}
		if err := json.Unmarshal([]byte(inProcessProgram.ChoiceData), &choiceTmp); err == nil {
			scheduleID = choiceTmp.ScheduleID
		}
	}
	var obScheduleDataUserGroup, obScheduleData []*dbcourse.ObProgramCourse
	var scheduleIDUserGroup, changeType int64
	var wg sync.WaitGroup
	wg.Add(1)
	safelygo.GoSafelyByTraceID(func() {
		defer wg.Done()
		obScheduleDataUserGroup, scheduleIDUserGroup = ObProgramSchedule(ObProgramScheduleByUserGroup(uid), scheduleID)
	})
	wg.Wait()
	if len(obScheduleDataUserGroup) != 0 && len(obScheduleData) == 0 && changeType != library.No {
		obScheduleData = obScheduleDataUserGroup
		scheduleID = scheduleIDUserGroup
	}
	if constsCheduleID != 0 {
		logger.Info("CreateObProgram-constsCheduleID", constsCheduleID)
		obScheduleData, scheduleID = ObProgramSchedule(constsCheduleID, 0)
	}
	if len(obScheduleData) == 0 {
		logger.Info("CreateObProgram-obScheduleData", obScheduleData)
		return errorcode.SystemError
	}
	choice.ScheduleID = scheduleID
	choiceData, err := json.Marshal(choice)
	if err != nil {
		logger.Error(err)
	}
	if inProcessProgram != nil {
		inProcessProgram.Days = int64(obScheduleData[len(obScheduleData)-1].Day)
		inProcessProgram.CourseCount = int32(len(obScheduleData))
		inProcessProgram.BeginTime = util.FormatStartTime(time.Now().Unix())
		inProcessProgram.ChoiceData = string(choiceData)
		inProcessProgram.IsValid = library.Yes
		inProcessProgram.FinishCount = 0
		if err := inProcessProgram.UpdateMustCols(); err != nil {
			logger.Error(err)
			return errorcode.DBError
		}
	} else {
		inProcessProgram = &dbcourse.ObProgram{
			UID:         uid,
			Days:        int64(obScheduleData[len(obScheduleData)-1].Day),
			CourseCount: int32(len(obScheduleData)),
			BeginTime:   util.FormatStartTime(time.Now().Unix()),
			ChoiceData:  string(choiceData),
			IsValid:     library.Yes,
		}
		if err := inProcessProgram.Save(); err != nil {
			logger.Error(err)
			return errorcode.DBError
		}
	}
	// 创建ob计划课程
	if len(obScheduleData) > 0 {
		startTime := util.FormatStartTime(time.Now().Unix())
		obList := make([]*dbcourse.ObProgramCourse, 0)
		historyScheduleList := dbcourse.TbObProgramCourse.GetList(uid, inProcessProgram.ID)
		for k, v := range obScheduleData {
			if k >= 1 && obScheduleData[k].Day != obScheduleData[k-1].Day {
				startTime += 86400
			}
			item := &dbcourse.ObProgramCourse{}
			if len(historyScheduleList) > k {
				item = historyScheduleList[k]
			}

			item.ProgramID = inProcessProgram.ID
			item.UID = inProcessProgram.UID
			item.CourseID = v.CourseID
			item.Day = v.Day
			item.PracticeDate = startTime
			item.IsPractice = library.No
			if item.CreateTime == 0 {
				item.CreateTime = time.Now().Unix()
			}
			item.UpdateTime = time.Now().Unix()
			obList = append(obList, item)
		}

		if len(historyScheduleList) == 0 {
			if err := dbcourse.TbObProgramCourse.BatchInsert(uid, obList); err != nil {
				logger.Error(err)
			}
		} else {
			var wg sync.WaitGroup
			for _, v := range obList {
				item := v
				wg.Add(1)
				safelygo.GoSafelyByTraceID(func() {
					defer wg.Done()
					if item.ID == 0 {
						if err := item.Save(); err != nil {
							logger.Error(err)
						}
					} else {
						if err := item.Update(); err != nil {
							logger.Error(err)
						}
					}
				})
			}
			if len(historyScheduleList) > len(obScheduleData) {
				deleteList := historyScheduleList[len(obScheduleData):]
				for _, v := range deleteList {
					v.UID = inProcessProgram.UID
					dItem := v
					wg.Add(1)
					safelygo.GoSafelyByTraceID(func() {
						defer wg.Done()
						if err := dItem.Delete(); err != nil {
							logger.Error(err)
						}
					})
				}
			}
			wg.Wait()
		}
	}
	safelygo.GoSafelyByTraceID(
		func() {
			e := &sensor.ScheduleStatus{
				CommData:           sensor.InitCommDataByClient(appClient),
				ScheduleTemplateID: scheduleID,
			}
			e.Track(strconv.FormatInt(uid, 10))
		})
	return errorcode.Success
}

// nolint
func GenerateObProgramSchedule(uid int64, appClient *library.AppClient,
	choice *ObUserChoice) []*dbcourse.ObProgramCourse {
	obScheduleData, _ := ObProgramSchedule(libc.ObProSchID, 0)
	if len(obScheduleData) == 0 {
		return nil
	}
	var (
		dayCount int = 28
	)
	if len(obScheduleData) != dayCount {
		logger.Error("用户生成ob课程列表错误", uid)
		return nil
	}
	res := make([]*dbcourse.ObProgramCourse, 0)
	for i := 0; i < dayCount; i++ {
		item := &dbcourse.ObProgramCourse{
			CourseID: obScheduleData[i].CourseID,
			Day:      int32(i + 1),
		}
		res = append(res, item)
	}
	return res
}

// 未登录时展示默认的ob课表
func GetDefaultDisplayObProgram(appClient *library.AppClient) *ObProgramDetail {
	res := &ObProgramDetail{
		CourseCount: libc.TotalObProgramCourseNum,
		StartTime:   util.FormatStartTime(time.Now().Unix()),
		TodayTime:   util.FormatStartTime(time.Now().Unix()),
		DayList:     make([]*DayList, 0),
	}
	scheduleData := GenerateObProgramSchedule(0, appClient, &ObUserChoice{})
	if len(scheduleData) == 0 {
		return res
	}
	secondsPerDay := 86400
	var (
		maxDay          int32 = 28
		scheduleDataMap       = make(map[int32][]*dbcourse.ObProgramCourse)
	)
	for _, v := range scheduleData {
		scheduleDataMap[v.Day] = append(scheduleDataMap[v.Day], v)
	}
	for k, v := range scheduleData {
		if params, ok := scheduleDataMap[v.Day]; ok {
			if len(params) >= 1 {
				params[0].PracticeDate = util.FormatStartTime(time.Now().Unix()) + int64(k*secondsPerDay)
			}
		}
	}
	res.DayList = GetObProgramCourseList(0, scheduleDataMap, maxDay, appClient)
	return res
}

func RestartObProgramSrv(uid int64) {
	inProcessProgram := dbcourse.TbObProgram.GetInProcessProgram(uid)
	if inProcessProgram == nil {
		return
	}
	RestartObProgram(uid, inProcessProgram.ID)
}

// RestartObProgram 重新开始Ob课表
func RestartObProgram(uid, obProgramID int64) errorcode.ErrorCode {
	inProcessProgram := dbcourse.TbObProgram.GetItem(uid, obProgramID)
	if inProcessProgram == nil {
		return errorcode.DBError
	}
	inProcessProgram.IsValid = library.Yes
	inProcessProgram.BeginTime = util.FormatStartTime(time.Now().Unix())
	inProcessProgram.FinishCount = 0
	if err := inProcessProgram.UpdateMustCols(); err != nil {
		logger.Error(err)
		return errorcode.DBError
	}
	obScheduleData := dbcourse.TbObProgramCourse.GetList(inProcessProgram.UID, obProgramID)
	startTime := util.FormatStartTime(time.Now().Unix())
	for k := range obScheduleData {
		if k >= 1 && obScheduleData[k].Day != obScheduleData[k-1].Day {
			startTime += 86400
		}
		obScheduleData[k].UID = inProcessProgram.UID
		obScheduleData[k].PracticeDate = startTime
		obScheduleData[k].IsPractice = library.No
	}
	var wg sync.WaitGroup
	for _, v := range obScheduleData {
		item := v
		wg.Add(1)
		safelygo.GoSafelyByTraceID(func() {
			defer wg.Done()
			if err := item.Update(); err != nil {
				logger.Error(err)
			}
		})
	}
	wg.Wait()
	return errorcode.Success
}

// nolint
func getDefaultCourseIDs() (scheduleData []int64) {
	obKey := client.ConfigKeyOBDefaultProgram
	obConfig := dbclient.TbConfig.GetItemByKey(obKey)
	if obConfig != nil && obConfig.ID > 0 {
		if obConfig.Value != "" {
			err := json.Unmarshal([]byte(obConfig.Value), &scheduleData)
			if err != nil {
				logger.Error(err)
			}
		}
	}
	return scheduleData
}

func ObProgramScheduleByUserGroup(uid int64) int64 {
	if uid == 0 {
		return 0
	}
	groupList := dbcourse.TbObProSch.GetList()
	if len(groupList) == 0 {
		return 0
	}
	groupIDList := make([]int64, 0)
	for _, v := range groupList {
		if v.UserGroupID == 0 {
			continue
		}
		groupIDList = append(groupIDList, v.UserGroupID)
	}
	groupClient := grpc.GetChildrenGroupClient()
	rsp, err := groupClient.ValidGroup(context.Background(), &childrenusergroup.ValidGroupRequest{
		UID:           uid,
		GroupLabelIDs: groupIDList,
	})
	if err != nil {
		logger.Error(err)
		return 0
	}
	validGroupList := rsp.GetGroupLabelID()
	validGroupList = append(validGroupList, 0)
	logger.Info("ObProgramScheduleByUserGroup-validGroupList", validGroupList, groupList)
	for _, v := range groupList {
		if library.InArray(v.UserGroupID, validGroupList) {
			if v.ChangeType == library.Yes {
				return v.ID
			}
			return 0
		}
	}
	return 0
}

type SessionArr struct {
	SessionIDs     []int64       `json:"session_ids"`
	DaySessionList []*DaySession `json:"day_session_list"`
}

type DaySession struct {
	SessionID   int64  `json:"session_id"`
	SessionName string `json:"session_name"`
	Alias       string `json:"alias"`
	CoverImg    string `json:"cover_img"`
}

type Item struct {
	ID          int64         `json:"id"`
	CycleSort   int64         `json:"cycle_sort"`
	Name        string        `json:"name"`
	SessionList []*SessionArr `json:"session_list"`
}

type Schedule struct {
	ID            int64   `json:"id"`
	Name          string  `json:"name"`
	UserGroupID   int64   `json:"user_group_id"`
	Priority      int64   `json:"priority"`
	ChangeType    int32   `json:"change_type"`
	CreateTime    int64   `json:"create_time"`
	ObProgramList []*Item `json:"obprogram_list"`
}

// nolint
func ObProgramSchedule(id, scheduleID int64) ([]*dbcourse.ObProgramCourse, int64) {
	logger.Info("ObProgramSchedule", id, scheduleID)
	resp := make([]*dbcourse.ObProgramCourse, 0)
	if id == 0 {
		return resp, 0
	}
	obProSch := formatObProSch(id)
	if obProSch == nil || len(obProSch.ObProgramList) == 0 {
		return resp, 0
	}
	index := 0
	for i, v := range obProSch.ObProgramList {
		if scheduleID == v.ID {
			index = i + 1
			break
		}
	}
	obProgramItem := obProSch.ObProgramList[(index)%len(obProSch.ObProgramList)]
	if obProgramItem == nil {
		return nil, 0
	}
	for day, v := range obProgramItem.SessionList {
		for _, sessionID := range v.SessionIDs {
			resp = append(resp, &dbcourse.ObProgramCourse{
				CourseID: sessionID,
				Day:      int32(day + 1),
			})
		}
	}
	return resp, obProgramItem.ID
}

func formatObProSch(id int64) *Schedule {
	item := dbcourse.TbObProSch.GetItemByID(id)
	if item == nil {
		return nil
	}
	resp := &Schedule{
		ID:            item.ID,
		Name:          item.Name,
		UserGroupID:   item.UserGroupID,
		Priority:      item.Priority,
		ChangeType:    item.ChangeType,
		CreateTime:    item.CreateTime,
		ObProgramList: make([]*Item, 0),
	}
	for _, v := range dbcourse.TbObProSchTable.GetListByScheduleID(item.ID) {
		sessionList := make([]*SessionArr, 0)
		if err := json.Unmarshal([]byte(v.SessionList), &sessionList); err != nil {
			logger.Error(err)
			continue
		}
		scheduleItem := &Item{
			ID:          v.ID,
			CycleSort:   v.CycleSort,
			Name:        v.Name,
			SessionList: sessionList,
		}
		resp.ObProgramList = append(resp.ObProgramList, scheduleItem)
	}
	return resp
}

func CleanUserObProgram(uid int64) {
	list := dbcourse.TbObProgram.GetList(uid)
	if len(list) == 0 {
		return
	}
	for _, v := range list {
		if err := v.Delete(); err == nil {
			item := &dbcourse.ObProgramCourse{UID: v.UID, ProgramID: v.ID}
			if err := item.Delete(); err != nil {
				logger.Error(err)
			}
		}
	}
}

type ProgramFinishInfo struct {
	ID              int64       `json:"id"`
	VersionTab      int64       `json:"version_tab"`
	BackgroundColor string      `json:"background_color"`
	UnitMaleList    []*UnitInfo `json:"unit_list"`
}

type UnitInfo struct {
	Sort  int64              `json:"sort"`
	Type  int64              `json:"type"`
	URL   *library.ImageInfo `json:"url,omitempty"`
	Scope int64              `json:"scope,omitempty"`
}

type ProgramFinishPageInfo struct {
	Sort        int64  `json:"sort"`
	MaleType    int64  `json:"male_type"`
	FemaleType  int64  `json:"female_type"`
	ImgFemale   string `json:"image_female"`
	ImgMale     string `json:"image_male"`
	FemaleScope int64  `json:"female_scope"`
	MaleScope   int64  `json:"male_scope"`
}

// nolint
func GetProgramFinishInfo(planPage int64, genderType int, uid int64, appClient *library.AppClient) *ProgramFinishInfo {
	result := &ProgramFinishInfo{
		UnitMaleList: make([]*UnitInfo, 0),
	}
	if planPage == 0 {
		planPage, _ = rop.Serv.GetPlanGenerationPage(uid, appClient)
	}
	imageList := make([]*ProgramFinishPageInfo, 0)
	planDetail := dbcourse.TbPlanPageGeneration.GetByID(planPage)
	if planDetail == nil {
		planDetail = dbcourse.TbPlanPageGeneration.GetByID(libc.PlanPageDef)
	}
	if planDetail == nil {
		return result
	}
	result.BackgroundColor = planDetail.BackgroundColor
	if err := json.Unmarshal([]byte(planDetail.ImageInfo), &imageList); err != nil {
		logger.Error("json.Unmarshal失败", err)
		return result
	}
	if len(imageList) == 0 {
		return result
	}
	unitMaleList := make([]*UnitInfo, 0)
	unitFemaleList := make([]*UnitInfo, 0)
	for _, image := range imageList {
		unitMale := &UnitInfo{
			Sort:  image.Sort,
			Type:  image.MaleType,
			URL:   util.UnmarshalImageStr(image.ImgMale),
			Scope: image.MaleScope,
		}
		if unitMale.Type != libc.PlanPageGenerationComponent {
			unitMale.Scope = 0
		}
		// 女
		unitFemale := &UnitInfo{
			Sort:  image.Sort,
			Type:  image.FemaleType,
			URL:   util.UnmarshalImageStr(image.ImgFemale),
			Scope: image.FemaleScope,
		}
		if unitFemale.Type != libc.PlanPageGenerationComponent {
			unitFemale.Scope = 0
		}
		unitMaleList = append(unitMaleList, unitMale)
		unitFemaleList = append(unitFemaleList, unitFemale)
	}
	sort.Slice(unitMaleList, func(i, j int) bool {
		return unitMaleList[i].Sort < unitMaleList[j].Sort
	})
	sort.Slice(unitFemaleList, func(i, j int) bool {
		return unitFemaleList[i].Sort < unitFemaleList[j].Sort
	})
	if genderType == libuser.GenderMan {
		result.UnitMaleList = unitMaleList
	} else {
		result.UnitMaleList = unitFemaleList
	}
	result.ID = planPage
	return result
}
