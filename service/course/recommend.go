package course

import (
	dbcourse "gitlab.dailyyoga.com.cn/server/children/databases/course"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libcourse "gitlab.dailyyoga.com.cn/server/children/library/course"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

func GuessUserLike(uid int64, sceneType int32, courseID int64, appClient *library.AppClient) []*ItemCourse {
	courseIDArr := GetIndexUserLike(uid, courseID)
	srvCourse := SrvOptionCourse{}
	recommendNum := libcourse.GuessUserLikeRecommendNum
	if len(courseIDArr) < libcourse.GuessUserLikeRecommendNum {
		recommendNum = len(courseIDArr)
	}
	if len(courseIDArr) == 0 {
		return make([]*ItemCourse, 0)
	}
	return srvCourse.BatchGetCourseDetail(uid, util.MicsSlice(courseIDArr,
		recommendNum), appClient, &libcourse.OptionCourse{
		IsOnline: library.Yes,
	}, WithLabel())
}

func GetIndexUserLike(uid, courseID int64) []int64 {
	// 猜你喜欢只取成品课
	query := dbcourse.QueryParam{
		IsOnline: library.Yes,
	}
	courseList := dbcourse.TbCourseLibrary.GetListByQuery(&query)
	if len(courseList) == 0 {
		return nil
	}
	courseIDList := make([]int64, 0)
	for _, v := range courseList {
		if v.ID == courseID {
			continue
		}
		courseIDList = append(courseIDList, v.ID)
	}
	return courseIDList
}
