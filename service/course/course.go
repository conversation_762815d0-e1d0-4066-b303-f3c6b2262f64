package course

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"sync"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	dbcollect "gitlab.dailyyoga.com.cn/server/children/databases/collect"
	dbcourse "gitlab.dailyyoga.com.cn/server/children/databases/course"
	"gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libcache "gitlab.dailyyoga.com.cn/server/children/library/cache"
	libcourse "gitlab.dailyyoga.com.cn/server/children/library/course"
	libuser "gitlab.dailyyoga.com.cn/server/children/library/user"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type ItemCourse struct {
	ID                 int64    `json:"id"`
	Title              string   `json:"title"`
	Desc               string   `json:"desc"`
	VideoURL           string   `json:"video_url"`
	Duration           float64  `json:"duration"`
	CoverURL           string   `json:"cover_url"`
	HorizontalCoverURL string   `json:"horizontal_cover_url"`
	IsVIP              int32    `json:"is_vip"`
	PracticeNum        int64    `json:"practice_num"`
	Level              int32    `json:"level"`
	PracticeLabels     []string `json:"practice_labels,omitempty"`
	PraPartLabels      []string `json:"pra_part_labels,omitempty"`
	LevelDesc          string   `json:"level_desc"`
	CustomLabels       []string `json:"custom_labels"`
	Coach              int64    `json:"coach"`
	Precautions        string   `json:"precautions"`
	IsNewCourse        int32    `json:"is_new_course"`
	CourseType         int32    `json:"course_type"`
	IsCollect          uint8    `json:"is_collect"`
	CourseTypeDesc     string   `json:"course_type_desc"`
}

type ItemLabel struct {
	Title    string `json:"title"`
	SubTitle string `json:"sub_title"`
	Icon     string `json:"icon"`
}

type UserInfoForCourse struct {
	UID       int64
	Gender    int32
	AppDevice *library.AppClient
}

type SrvOptionCourse struct {
	WithLabel bool
}

func (s *SrvOptionCourse) GetCourseDetail(uid, courseID int64, optionCourse *libcourse.OptionCourse, appClient *library.AppClient,
	opts ...Option) *ItemCourse {
	for _, fn := range opts {
		fn(s)
	}
	courseItem := dbcourse.TbCourseLibrary.GetItem(courseID)
	if courseItem == nil {
		return nil
	}
	originConfig := &OriginCourseInfo{
		CourseConfig:      courseItem,
		UserInfo:          initUserInfoForCourse(uid),
		UserCollectCourse: dbcollect.TbCollect.GetUserCollectCourse(uid),
	}
	// 课程标签
	originConfig.LabelConfig = dbcourse.TbLabelConfig.GetListByPIDList([]int64{libcourse.LabelCourseEffectPID,
		libcourse.LabelCourseTypePID, libcourse.LabelCourseLevelPID,
	})
	originConfig.CourseLabelList = dbcourse.TbLabelResource.GetListByResourceID(courseID,
		libcourse.ResourceTypeEnum.Course)
	result := s.FormatCourseInfo(originConfig, appClient)
	return result
}

type OriginCourseInfo struct {
	CourseConfig      *dbcourse.DBCourse
	CourseLabelList   []*dbcourse.LabelResource
	LabelConfig       []*dbcourse.LabelConfig
	UserInfo          *UserInfoForCourse
	UserCollectCourse []int64
}

func (s *SrvOptionCourse) FormatCourseInfo(config *OriginCourseInfo, appClient *library.AppClient) *ItemCourse {
	res := &ItemCourse{}
	// 课程基础信息
	res.BuildBaseCourse(config)
	return res
}

func (s *SrvOptionCourse) BatchGetCourseDetail(uid int64, courseIDArr []int64,
	appClient *library.AppClient, optionCourse *libcourse.OptionCourse, opts ...Option) []*ItemCourse {
	sortList := make([]*ItemCourse, 0)
	if len(courseIDArr) == 0 {
		return sortList
	}
	for _, fn := range opts {
		fn(s)
	}
	courseList := dbcourse.TbCourseLibrary.GetListByIDs(courseIDArr, optionCourse)
	if len(courseList) == 0 {
		return nil
	}
	uCollect := dbcollect.TbCollect.GetUserCollectCourse(uid)
	uInfo := initUserInfoForCourse(uid)
	labelConfig := dbcourse.TbLabelConfig.GetList()
	res := make(map[int64]*ItemCourse)
	dataChan := make(chan *ItemCourse, len(courseList))
	done := make(chan struct{}) // 用于通知所有任务完成
	var wg sync.WaitGroup
	safelygo.GoSafelyByTraceID(func() {
		for data := range dataChan {
			res[data.ID] = data
		}
		done <- struct{}{} // 通知所有任务完成
	})

	for _, v := range courseList {
		clItem := v
		wg.Add(1)
		safelygo.GoSafelyByTraceID(
			func() {
				defer wg.Done()
				originConfig := &OriginCourseInfo{
					UserInfo:          uInfo,
					LabelConfig:       labelConfig,
					CourseConfig:      clItem,
					UserCollectCourse: uCollect,
					CourseLabelList: getListByResourceID(clItem.ID,
						libcourse.ResourceTypeEnum.Course),
				}
				temp := s.FormatCourseInfo(originConfig, appClient)
				dataChan <- temp
			})
	}
	wg.Wait()
	close(dataChan)
	<-done // 等待消费goroutine完成
	for _, v := range courseIDArr {
		if c, ok := res[v]; ok {
			sortList = append(sortList, c)
		}
	}
	return sortList
}

func getListByResourceID(resourceID int64, resourceType libcourse.ResourceTypeInt) []*dbcourse.LabelResource {
	list := dbcourse.TbLabelResource.GetListByResourceID(resourceID, resourceType)
	return list
}

// 初始化所需的用户信息及用户设备信息
func initUserInfoForCourse(uid int64) *UserInfoForCourse {
	res := &UserInfoForCourse{
		UID:    uid,
		Gender: libuser.GenderMan,
	}
	if uid == 0 {
		return res
	}
	account := user.TbAccount.GetUserByID(uid)
	if account == nil {
		return res
	}
	res.Gender = int32(account.Gender)
	return res
}

// 构造课程基本信息
func (c *ItemCourse) BuildBaseCourse(config *OriginCourseInfo) {
	course := config.CourseConfig
	if course == nil {
		return
	}
	uCollect := config.UserCollectCourse
	uInfo := config.UserInfo
	labelConfig := config.LabelConfig
	c.ID = course.ID
	c.Title = course.Title
	c.Desc = course.Desc
	c.Duration = course.Duration
	c.PracticeNum = course.PracticeNum
	c.VideoURL = util.GetOnlineVideoURL(course.VideoURL)
	c.CoverURL = util.GetOnlineImageURL(course.CoverURL)
	c.HorizontalCoverURL = util.GetOnlineImageURL(course.HorizontalCoverURL)
	c.IsVIP = course.IsVIP
	c.Coach = course.Coach
	c.Precautions = course.Precautions
	c.IsNewCourse = library.No
	if uInfo != nil && library.InArray(course.ID, uCollect) {
		c.IsCollect = 1
	}
	customLabels := make([]string, 0)
	if err := json.Unmarshal([]byte(course.CustomLabels), &customLabels); err != nil {
		customLabels = make([]string, 0)
	}
	c.CustomLabels = customLabels
	c.BuildCourseLabel(config.CourseLabelList, labelConfig)
	if course.IsNewCourse == library.Yes && course.NewCourseTime > time.Now().Add(-time.Hour*library.DayHour*library.Const7).Unix() {
		c.IsNewCourse = course.IsNewCourse
	}
}

// 构造课程标签相关信息
// nolint
func (c *ItemCourse) BuildCourseLabel(labelList []*dbcourse.LabelResource, labelConfig []*dbcourse.LabelConfig) {
	if len(labelList) == 0 {
		return
	}
	labelMap := make(map[int64]*dbcourse.LabelConfig)
	for _, v := range labelConfig {
		labelMap[v.ID] = v
	}
	for _, v := range labelList {
		label, ok := labelMap[v.LabelID]
		if !ok {
			continue
		}
		switch label.Pid {
		case libcourse.LabelCourseEffectPID:
			c.PracticeLabels = append(c.PracticeLabels, label.Title)
		case libcourse.LabelCourseTypePID:
			c.CourseType = int32(v.LabelID)
			c.CourseTypeDesc = label.Title
		case libcourse.LabelCourseLevelPID:
			level := libcourse.LevelEnum.Primary
			if tLevel, lOK := libcourse.EnumByLevelDBID[int(v.LabelID)]; lOK {
				level = tLevel
			}
			c.Level = int32(level)
			c.LevelDesc = libcourse.LevelDesc[level]
		}
	}
}

type labelIcon struct {
	Gender int    `json:"gender"`
	Icon   string `json:"icon"`
}

// GetLabelIcon 获取课程标签icon
func GetLabelIcon(iconList string, gender int) string {
	if iconList == "" {
		return ""
	}
	tmp := make([]*labelIcon, 0)
	if err := json.Unmarshal([]byte(iconList), &tmp); err != nil {
		logger.Error(err, iconList)
		return ""
	}
	if gender <= libuser.GenderUnknown {
		gender = libuser.GenderWomen
	}
	if len(tmp) > 0 {
		for _, v := range tmp {
			if v.Gender == gender {
				return v.Icon
			}
		}
	}
	return ""
}

type GenerateActions struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	NeedRest bool   `json:"need_rest"`
	Version  string `json:"version"`
}

type GenerateCourse struct {
	CourseID string             `json:"course_id"`
	Name     string             `json:"name"`
	Version  int                `json:"version"`
	Voices   []string           `json:"voices"`
	Actions  []*GenerateActions `json:"actions"`
	WithExp  bool               `json:"with_explanation"`
}

type PracticeStat struct {
	PracticeStart  int32 `json:"practice_start"`
	PracticeExit   int32 `json:"practice_exit"`
	PracticeFinish int32 `json:"practice_finish"`
}
type SessionLabelItem struct {
	LabelID  int64           `json:"label_id"`
	Name     string          `json:"name"`
	SubLabel []*SessionLabel `json:"sub_label"`
}
type SessionLabel struct {
	LabelID int64  `json:"label_id"`
	Name    string `json:"name"`
}

func GetSessionFilterLabelList() []*SessionLabelItem {
	data := dbcourse.TbLabelConfig.GetListByIDList(libcourse.AllSessionFilterLabel)
	if len(data) < 1 {
		return nil
	}
	pidList := make([]int64, 0)
	for _, pItem := range data {
		pidList = append(pidList, pItem.ID)
	}
	pList := dbcourse.TbLabelConfig.GetListByPIDList(pidList)
	pListMap := make(map[int64][]*SessionLabel)
	for _, pv := range pList {
		if _, ok := pListMap[pv.Pid]; !ok {
			pListMap[pv.Pid] = make([]*SessionLabel, 0)
		}
		pListMap[pv.Pid] = append(pListMap[pv.Pid], &SessionLabel{
			LabelID: pv.ID,
			Name:    pv.Title,
		})
	}

	// 创建标签ID到排序位置的映射
	orderMap := make(map[int64]int)
	for i, labelID := range libcourse.AllSessionFilterLabel {
		orderMap[int64(labelID)] = i
	}

	resp := make([]*SessionLabelItem, 0)
	for _, v := range data {
		item := &SessionLabelItem{
			LabelID:  v.ID,
			Name:     v.Title,
			SubLabel: make([]*SessionLabel, 0),
		}
		pVal, ok := pListMap[v.ID]
		if !ok {
			continue
		}
		item.SubLabel = pVal
		resp = append(resp, item)
	}

	// 按照 AllSessionFilterLabel 中的顺序排序
	sort.Slice(resp, func(i, j int) bool {
		orderI, okI := orderMap[resp[i].LabelID]
		orderJ, okJ := orderMap[resp[j].LabelID]
		if !okI && !okJ {
			return resp[i].LabelID < resp[j].LabelID
		}
		if !okI {
			return false
		}
		if !okJ {
			return true
		}
		return orderI < orderJ
	})

	return resp
}

type SrvSelectTabFilter struct {
	LabelID   int64
	Page      int
	PageSize  int
	AppDevice *library.AppClient
	UID       int64
}

func (s *SrvSelectTabFilter) SelectTabFilter() []*ItemCourse {
	courseLabelList := dbcourse.TbLabelResource.GetListByLabelID(s.LabelID,
		libcourse.ResourceTypeEnum.Course)
	if len(courseLabelList) < 1 {
		return nil
	}
	courseIDArr := make([]int64, 0)
	for _, v := range courseLabelList {
		courseIDArr = append(courseIDArr, v.ResourceID)
	}
	courseList := s.sortCourse(courseIDArr)
	if len(courseList) < 1 {
		return nil
	}
	return courseList
}

// - 课程展示：按照近30日播放量倒序排列
// - 新课的话，排序靠前，展示7天
func (s *SrvSelectTabFilter) sortCourse(courseIDArr []int64) []*ItemCourse {
	result := make([]*ItemCourse, 0)
	if len(courseIDArr) == 0 {
		return result
	}
	rdc := cache.GetCRedis().GetClient()
	ctx := context.Background()
	type coursePlayCount struct {
		CourseID  int64
		PlayCount int64
		IsNew     bool
		course    *ItemCourse
	}
	courseList := dbcourse.TbCourseLibrary.GetListByIDs(courseIDArr, &libcourse.OptionCourse{
		IsOnline: library.Yes,
	})
	uCollect := dbcollect.TbCollect.GetUserCollectCourse(s.UID)
	uInfo := initUserInfoForCourse(s.UID)
	labelConfig := dbcourse.TbLabelConfig.GetList()
	courseMap := make(map[int64]*ItemCourse)
	dataChan := make(chan *ItemCourse, len(courseList))
	done := make(chan struct{}) // 用于通知所有任务完成
	var wg sync.WaitGroup
	safelygo.GoSafelyByTraceID(func() {
		for data := range dataChan {
			courseMap[data.ID] = data
		}
		done <- struct{}{} // 通知所有任务完成
	})

	for _, v := range courseList {
		clItem := v
		wg.Add(1)
		safelygo.GoSafelyByTraceID(
			func() {
				defer wg.Done()
				originConfig := &OriginCourseInfo{
					UserInfo:          uInfo,
					LabelConfig:       labelConfig,
					CourseConfig:      clItem,
					UserCollectCourse: uCollect,
					CourseLabelList: getListByResourceID(clItem.ID,
						libcourse.ResourceTypeEnum.Course),
				}
				srv := &SrvOptionCourse{}
				temp := srv.FormatCourseInfo(originConfig, s.AppDevice)
				dataChan <- temp
			})
	}
	wg.Wait()
	close(dataChan)
	<-done // 等待消费goroutine完成
	coursePlayList := make([]*coursePlayCount, 0)
	for _, courseID := range courseIDArr {
		// 获取课程近30天播放量
		playCountStr, err := rdc.HGet(ctx, libcache.CoursePlayTotal30Days, fmt.Sprintf("%d", courseID)).Result()
		playCount := int64(0)
		if err == nil {
			playCount, _ = strconv.ParseInt(playCountStr, 10, 64)
		}
		course, ok := courseMap[courseID]
		if !ok {
			continue
		}
		coursePlayList = append(coursePlayList, &coursePlayCount{
			CourseID:  courseID,
			PlayCount: playCount,
			IsNew:     course.IsNewCourse == library.Yes,
			course:    course,
		})
	}
	// 排序：新课优先，然后按播放量倒序
	sort.Slice(coursePlayList, func(i, j int) bool {
		if coursePlayList[i].IsNew != coursePlayList[j].IsNew {
			return coursePlayList[i].IsNew
		}
		return coursePlayList[i].PlayCount > coursePlayList[j].PlayCount
	})

	for _, item := range coursePlayList {
		result = append(result, item.course)
	}
	return result
}
