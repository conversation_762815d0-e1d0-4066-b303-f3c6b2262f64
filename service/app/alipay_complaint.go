package app

import (
	"context"
	"encoding/json"
	"time"

	"github.com/go-redis/redis/v8"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	lbcache "gitlab.dailyyoga.com.cn/server/children/library/cache"
	lbpay "gitlab.dailyyoga.com.cn/server/children/library/pay"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
)

type ComplaintEvent struct {
	EventID string `json:"complain_event_id"`
}

type TradeComplain struct {
	*ComplaintEvent
	AppID   string `json:"app_id"`
	Project int    `json:"project"`
	PayType int    `json:"pay_type"`
}

func AlipayComplaint(event, appID string) bool {
	if event == "" {
		return false
	}
	logger.Info("支付宝投诉", event)
	cevent := &ComplaintEvent{}
	err := json.Unmarshal([]byte(event), cevent)
	if err != nil {
		logger.Error("支付宝投诉解析失败", err)
		return false
	}
	tradeComplain := TradeComplain{
		ComplaintEvent: cevent,
		AppID:          appID,
		Project:        lbpay.FitnessProjectType,
		PayType:        lbpay.PayTypeAlipay,
	}
	tradeComplainByte, _ := json.Marshal(tradeComplain)
	member := &redis.Z{
		Score:  float64(time.Now().Nanosecond()),
		Member: string(tradeComplainByte),
	}
	rdc := cache.GetYogaRedis().GetClient()
	ret, err := rdc.ZAdd(context.Background(), lbcache.TradeComplainMsg, member).Result()
	if err != nil {
		logger.Error(err, string(tradeComplainByte))
	}
	logger.Info("支付宝投诉==", event)
	if ret == 0 {
		logger.Info("投诉写入失败", string(tradeComplainByte), ret)
	}
	return true
}
