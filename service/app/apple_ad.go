//nolint:gomnd
package app

import (
	"context"
	"crypto/ecdsa"
	"crypto/x509"
	"encoding/json"
	"encoding/pem"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/gokit/sensorsdata"
	"gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
	"gitlab.dailyyoga.com.cn/server/gopay/pkg/jwt"
)

const (
	QueueKey          = "Cs:delay:queue:appleAds"
	QueueDelaySeconds = 600 // 10分钟
	iosOrganic        = "ios_organic"
	iosASA            = "ios_asa"
)

type AdsInfoAdServiceReq struct {
	Data       string `json:"data"`
	DistinctID string `json:"distinctID"`
}

// AdsInfoAdService AdService 框架上报
type AdsInfoAdService struct {
	KeywordID       int    `json:"keywordId"`
	ConversionType  string `json:"conversionType"`
	OrgID           int    `json:"orgId"`
	CampaignID      int    `json:"campaignId"`
	AdGroupID       int    `json:"adGroupId"`
	CountryOrRegion string `json:"countryOrRegion"`
	Attribution     bool   `json:"attribution"`
	ClickDate       string `json:"clickDate"`
	DistinctID      string `json:"distinct_id"`
	IsLogin         bool   `json:"is_login"`
}

// ScAppleAdsInfoEvent 神策事件上报
type ScAppleAdsInfoEvent struct {
	IsFirstDownload        string `json:"asa_is_first_download"`
	Country                string `json:"asa_country"`
	ClickAdTime            string `json:"asa_click_ad_time"`
	MatchType              string `json:"asa_matchtype"`
	AdDownloadAppTime      string `json:"asa_click_ad_download_app_time"`
	AdFirstDownloadAppTime string `json:"asa_click_ad_first_download_app_time"`
}

// EventName 事件名称
func (ScAppleAdsInfoEvent) EventName() string {
	return "ft_ios_asa_appstore_install"
}

func (ScAppleAdsInfoEvent) Prefix() string {
	return ""
}

// UploadScAppleAdsInfoAdService AdService归因
func (a *AdsInfoAdService) UploadScAppleAdsInfoAdService() {
	var handleFalse = true
	defer func() {
		// 如果该方法处理失败了,上报死信队列,下次处理
		if handleFalse {
			logger.Warnf("方法UploadScAppleAdsInfoAdService中触发队列上报，上报数据%v", a)
			if status := a.hasHandleDelayQueue(); !status {
				logger.Errorf("方法UploadScAppleAdsInfoAdService中触发延迟队列上报，上报数据异常 %v", a)
			}
		}
	}()
	// 开始上报神策
	profile := make(map[string]interface{})
	profile["asa_utm_source"] = iosOrganic
	if a.Attribution {
		orgName, retry := getOrgName(a.OrgID)
		if orgName == "" {
			if !retry {
				handleFalse = false
				logger.Warnf("方法UploadScAppleAdsInfoAdService中没有匹配到orgName %v", a)
				return
			}
			logger.Warnf("方法UploadScAppleAdsInfoAdService中没有匹配到orgName触发队列上报 %v", a)
			return
		}
		profile["asa_utm_source"] = iosASA
		// iad-org-name拼iad-org-id字段（下划线拼接name_id），赋值给新增的用户属性asa_utm_medium；
		profile["asa_utm_medium"] = fmt.Sprintf("%s_%d", orgName, a.OrgID)
		//  iad-keyword拼iad-keyword-id字段（下划线拼接name_id），赋值给新增的用户属性 asa_utm_term
		keywordText, matchType := getKeywordAndMatchType(a)
		profile["asa_utm_term"] = fmt.Sprintf("%s_%d", keywordText, a.KeywordID)
		// iad-creativeset-name 拼iad-creativeset-id字段（下划线拼接name_id），赋值给新增的用户属性 asa_utm_content
		profile["asa_utm_content"] = fmt.Sprintf("%s_%s", "", "")
		// iad-campaign-name 拼iad-campaign-id字段（下划线拼接name_id），赋值给新增的用户属性 asa_utm_campaign；
		campaign, err := ServerInstance.GetCampaignByID(a.CampaignID)
		if err != nil {
			logger.Errorf(err.Error())
			return
		}
		profile["asa_utm_campaign"] = fmt.Sprintf("%s_%d", campaign.Data.Name, a.CampaignID)
		// iad-adgroup-name 拼 iad-adgroup-id 字段（下划线拼接name_id），赋值给新增的用户属性 asa_utm_group
		adGroup, err := ServerInstance.GetAdGroupByCampaignIDAdgroupID(a.CampaignID, a.AdGroupID)
		if err != nil {
			logger.Errorf(err.Error())
			return
		}
		profile["asa_utm_group"] = fmt.Sprintf("%s_%d", adGroup.Data.Name, a.AdGroupID)

		clickAdTime := time.Unix(0, 0)
		if a.ClickDate != "" {
			// 苹果官方的响应文档里是这个格式,并不属于标准的 RFC3339 ,但是从 iad 框架中的时间是标准的 RFC3339 ,因此还有待验证
			if clickAdTime, err = time.ParseInLocation("2006-01-02T15:04Z", a.ClickDate, time.Local); err != nil {
				logger.Errorf("apple ads 归因,时间转换错误 %s 原值:%s", err.Error(), a.ClickDate)
				return
			}
		}
		// 事件上报
		err = sensorsdata.Track(a.DistinctID, ScAppleAdsInfoEvent{
			IsFirstDownload: a.ConversionType,
			Country:         a.CountryOrRegion,
			ClickAdTime:     clickAdTime.Format("2006-01-02 15:04:05"),
			MatchType:       matchType,
			// adService 框架回比 iad 少报两个字段,发现神策不能上报空,默认上报 0 的时间戳事件
			AdDownloadAppTime:      time.Unix(0, 0).Format("2006-01-02 15:04:05"),
			AdFirstDownloadAppTime: time.Unix(0, 0).Format("2006-01-02 15:04:05"),
		}, a.IsLogin)
		if err != nil {
			logger.Errorf("神策过期事件上报失败 %v", err)
		}
	}
	logger.Infof("ads temp %+v profile:%+v IsLogin:%+v", a.DistinctID, profile, a.IsLogin)
	// 上报神策用户属性
	err := sensorsdata.ProfileSet(a.DistinctID, profile, a.IsLogin)
	if err != nil {
		logger.Errorf("神策过期事件上报失败 %v", err)
	}
	handleFalse = false
	profileByte, _ := json.Marshal(profile)
	reqByte, _ := json.Marshal(a)
	logger.Info("UploadScAppleAdsInfoAdService", string(reqByte), string(profileByte))
}

func (a *AdsInfoAdService) hasHandleDelayQueue() bool {
	data, err := json.Marshal(a)
	if err != nil {
		logger.Error("apple-asa 延迟队列format json err", err.Error())
		return false
	}
	if err := util.AddToQueue(context.Background(),
		cache.GetYoga01Redis().GetClient(),
		QueueKey, string(data), int64(QueueDelaySeconds)); err != nil {
		logger.Error("apple-asa 延迟队列错误", err.Error())
		return false
	}
	return true
}

func getOrgName(orgID int) (string, bool) {
	token := ServerInstance.GetToken()
	if token == nil {
		logger.Warnf("方法UploadScAppleAdsInfoAdService中没有拿到token，导致orgName为空触发队列上报 %v", token)
		return "", true
	}
	acl, err := ServerInstance.GetACL(token)
	if err != nil {
		logger.Warnf("方法UploadScAppleAdsInfoAdService中调用GetACL出错，导致orgName为空触发队列上报 %v", err.Error())
		return "", true
	}
	logger.Warnf("触发队列上报,得到的最终数据%v", acl.Data)
	org := ""
	for _, item := range acl.Data {
		if item.OrgID == orgID {
			org = item.OrgName
			break
		}
	}
	return org, false
}

var ServerInstance = new(Server)

type Server struct{}

type AdsToken struct {
	Value string
}

// GetToken 获取 token
func (s *Server) GetToken() *AdsToken {
	conf := config.Get().AppleAdsConfig
	const key = "ft_apple_ads_info_token"
	rd := cache.GetCRedis().GetClient()
	cmd := rd.Get(context.Background(), key)
	if cmd.Err() != nil && cmd.Err() != redis.Nil {
		logger.Error(cmd.Err().Error())
		return nil
	}
	if cmd.Err() == nil && cmd.Val() != "" {
		return &AdsToken{Value: cmd.Val()}
	}
	secret := s.getSecret()
	if secret == "" {
		return nil
	}
	// 根据 secret 请求 token
	f := func(secret string) *AdsToken {
		address := "https://appleid.apple.com/auth/oauth2/token"
		v := url.Values{}
		v.Set("grant_type", "client_credentials")
		v.Set("client_id", conf.ClientID)
		v.Set("client_secret", secret)
		v.Set("scope", "searchadsorg")
		address = address + "?" + v.Encode()
		header := http.Header{}
		header.Set("Content-Type", "application/x-www-form-urlencoded")
		body, err := util.HTTPRequest(address, "POST", header, nil)
		if err != nil {
			logger.Error(err.Error())
			return nil
		}
		token := new(struct {
			AccessToken string `json:"access_token"`
			TokenType   string `json:"token_type"`
			ExpiresIn   int    `json:"expires_in"`
		})
		if err := json.Unmarshal(body, token); err != nil {
			logger.Error(err.Error())
			return nil
		}
		if token.AccessToken == "" {
			logger.Error("苹果广告数据上报神策从苹果获取到的 Value 为空,响应体为" + string(body))
			return nil
		}
		return &AdsToken{Value: token.AccessToken}
	}
	// token 不为空进行缓存
	if token := f(secret); token != nil {
		err := rd.Set(context.Background(), key, token.Value, time.Minute*50).Err()
		if err != nil {
			logger.Error("苹果广告数据上报神策存入 Value 失败 ", err.Error())
			return nil
		}
		return token
	}
	return nil
}

// getSecret 根据苹果官网签发的私钥文件生成 secret 用于验签,生成的 secret 最长有效期有 180 天
func (s *Server) getSecret() string {
	const key = "ft_apple_ads_info_secret"
	// 缓存中有则直接拿
	rd := cache.GetCRedis().GetClient()
	cmd := rd.Get(context.Background(), key)
	if cmd.Err() != nil && cmd.Err() != redis.Nil {
		logger.Error(cmd.Err().Error())
		return ""
	}
	if cmd.Err() == nil {
		return cmd.Val()
	}
	// 没有则计算
	conf := config.Get().AppleAdsConfig
	const privateKey = `**********************************************************************************************************************************************************************************************************************************`
	ecdsaKey, err := s.authKeyFromBytes([]byte(privateKey))
	if err != nil {
		logger.Error(err.Error())
		return ""
	}
	now := time.Now()
	jwtInstance := jwt.NewWithClaims(jwt.SigningMethodES256, jwt.MapClaims{
		"sub": conf.ClientID,
		"aud": conf.Audience,
		"iat": now.Unix(),
		"exp": now.Add(time.Hour * 24 * 180).Unix(),
		"iss": conf.TeamID,
	})
	jwtInstance.Header["alg"] = conf.Alg
	jwtInstance.Header["kid"] = conf.KeyID
	secret, err := jwtInstance.SignedString(ecdsaKey)
	if err != nil {
		logger.Errorf("苹果广告数据上报神策生成秘钥失败 %s", err)
		return ""
	}
	// 缓存 180 天
	if err = rd.Set(context.Background(), key, secret, time.Hour*24*180).Err(); err != nil {
		logger.Error(err.Error())
		return ""
	}
	return secret
}

// GetCampaignByIDResponse GetCampaignByID 的响应
type GetCampaignByIDResponse struct {
	Data struct {
		ID           int    `json:"id"`
		OrgID        int    `json:"orgId"`
		Name         string `json:"name"`
		BudgetAmount struct {
			Amount   string `json:"amount"`
			Currency string `json:"currency"`
		} `json:"budgetAmount"`
		DailyBudgetAmount struct {
			Amount   string `json:"amount"`
			Currency string `json:"currency"`
		} `json:"dailyBudgetAmount"`
		AdamID            int    `json:"adamId"`
		PaymentModel      string `json:"paymentModel"`
		LocInvoiceDetails struct {
			ClientName          string `json:"clientName"`
			OrderNumber         string `json:"orderNumber"`
			BuyerName           string `json:"buyerName"`
			BuyerEmail          string `json:"buyerEmail"`
			BillingContactEmail string `json:"billingContactEmail"`
		} `json:"locInvoiceDetails"`
		BudgetOrders        []interface{} `json:"budgetOrders"`
		StartTime           string        `json:"startTime"`
		EndTime             interface{}   `json:"endTime"`
		Status              string        `json:"status"`
		ServingStatus       string        `json:"servingStatus"`
		ServingStateReasons interface{}   `json:"servingStateReasons"`
		ModificationTime    string        `json:"modificationTime"`
		Deleted             bool          `json:"deleted"`
		SapinLawResponse    string        `json:"sapinLawResponse"`
		CountriesOrRegions  []string      `json:"countriesOrRegions"`
		// 该字段官网也没 demo 数据,我也不知道它该返回什么
		CountryOrRegionServingStateReasons interface{} `json:"countryOrRegionServingStateReasons"`
		SupplySources                      []string    `json:"supplySources"`
		AdChannelType                      string      `json:"adChannelType"`
		BillingEvent                       string      `json:"billingEvent"`
		DisplayStatus                      string      `json:"displayStatus"`
	}
}

// GetCampaignByID 获取 Campaign
func (s *Server) GetCampaignByID(campaignID int) (*GetCampaignByIDResponse, error) {
	address := fmt.Sprintf("https://api.searchads.apple.com/api/v5/campaigns/%d", campaignID)
	resp := new(GetCampaignByIDResponse)
	key := fmt.Sprintf("ft_apple_ads_info_campaigns_%d", campaignID)
	rd := cache.GetCRedis().GetClient()
	cmd := rd.Get(context.Background(), key)
	if cmd.Err() != nil && cmd.Err() != redis.Nil {
		return nil, cmd.Err()
	}
	if cmd.Err() == nil && cmd.Val() != "" {
		if err := json.Unmarshal([]byte(cmd.Val()), resp); err != nil {
			return nil, err
		}
		return resp, nil
	}
	token := s.GetToken()
	acl, err := s.GetACL(token)
	if err != nil {
		return nil, err
	}
	orgID := acl.Data[0].OrgID
	header := http.Header{}
	header.Set("Authorization", fmt.Sprintf("Bearer %s", token.Value))
	header.Set("X-AP-Context", fmt.Sprintf("orgId=%d", orgID))
	header.Set("Content-Type", "application/json")
	body, err := util.HTTPRequest(address, "GET", header, nil)
	if err != nil {
		return nil, err
	}
	if err := json.Unmarshal(body, resp); err != nil {
		return nil, err
	}
	if err := rd.Set(context.Background(), key, string(body), time.Minute*50).Err(); err != nil {
		return nil, err
	}
	return resp, nil
}

// GetKeywordsByCampaignResponse 获取关键词
type GetKeywordsByCampaignResponse struct {
	Data []struct {
		ID        int    `json:"id"`
		AdGroupID int    `json:"adGroupId"`
		Text      string `json:"text"`
		Status    string `json:"status"`
		MatchType string `json:"matchType"`
		BidAmount struct {
			Amount   string `json:"amount"`
			Currency string `json:"currency"`
		} `json:"bidAmount"`
		ModificationTime string `json:"modificationTime"`
		Deleted          bool   `json:"deleted"`
	} `json:"data"`
	Pagination struct {
		TotalResults int `json:"totalResults"`
		StartIndex   int `json:"startIndex"`
		ItemsPerPage int `json:"itemsPerPage"`
	} `json:"pagination"`
	Error interface{} `json:"error"`
}

// GetKeywordsByCampaignID 通过 Campaign 获取关键词 (注:这个获取的比较全)
func (s *Server) GetKeywordsByCampaignID(campaignID int) (*GetKeywordsByCampaignResponse, error) {
	resp := new(GetKeywordsByCampaignResponse)
	key := fmt.Sprintf("ft_apple_ads_info_keywords_bycampaign_%d", campaignID)
	rd := cache.GetCRedis().GetClient()
	cmd := rd.Get(context.Background(), key)
	if cmd.Err() != nil && cmd.Err() != redis.Nil {
		return nil, cmd.Err()
	}
	if cmd.Err() == nil && cmd.Val() != "" {
		if err := json.Unmarshal([]byte(cmd.Val()), resp); err != nil {
			return nil, err
		}
		return resp, nil
	}
	token := s.GetToken()
	acl, err := s.GetACL(token)
	if err != nil {
		return nil, err
	}
	orgID := acl.Data[0].OrgID
	address := fmt.Sprintf("https://api.searchads.apple.com/api/v5/campaigns/%d/adgroups/targetingkeywords/find",
		campaignID)
	header := http.Header{}
	header.Set("Authorization", fmt.Sprintf("Bearer %s", token.Value))
	header.Set("X-AP-Context", fmt.Sprintf("orgId=%d", orgID))
	header.Set("Content-Type", "application/json")
	reader := strings.NewReader(`{"pagination":{"offset":0,"limit":99999},"orderBy":[{"field":"id","sortOrder":"ASCENDING"}],"conditions":[{"field":"deleted","operator":"EQUALS","values":["false"]}]}`) // nolint
	body, err := util.HTTPRequest(address, "POST", header, reader)
	if err != nil {
		logger.Warn(err.Error())
		return nil, nil
	}
	if err := json.Unmarshal(body, resp); err != nil {
		return nil, err
	}
	if resp.Error != nil {
		return nil, fmt.Errorf("%s", resp.Error)
	}
	if err := rd.Set(context.Background(), key, string(body), time.Minute*20).Err(); err != nil {
		return nil, err
	}
	return resp, nil
}

func (s *Server) authKeyFromBytes(bytes []byte) (*ecdsa.PrivateKey, error) {
	block, _ := pem.Decode(bytes)
	if block == nil {
		return nil, errors.New("value: AuthKey must be a valid .p8 PEM file")
	}
	key, err := x509.ParseECPrivateKey(block.Bytes)
	if err != nil {
		return nil, err
	}
	return key, nil
}

// GetKeywordsResponse 获取全部关键词的响应
type GetKeywordsResponse struct {
	Data []struct {
		ID        int    `json:"id"`
		AdGroupID int    `json:"adGroupId"`
		Text      string `json:"text"`
		Status    string `json:"status"`
		MatchType string `json:"matchType"`
		BidAmount struct {
			Amount   string `json:"amount"`
			Currency string `json:"currency"`
		} `json:"bidAmount"`
		ModificationTime string `json:"modificationTime"`
		Deleted          bool   `json:"deleted"`
	} `json:"data"`
	Pagination struct {
		TotalResults int `json:"totalResults"`
		StartIndex   int `json:"startIndex"`
		ItemsPerPage int `json:"itemsPerPage"`
	} `json:"pagination"`
}

// GetKeywordsByCampaignIDAdgroupID 获取关键字
func (s *Server) GetKeywordsByCampaignIDAdgroupID(campaignID, adgroupID int) (*GetKeywordsResponse, error) {
	resp := new(GetKeywordsResponse)
	key := fmt.Sprintf("ft_apple_ads_info_keywords_%d_%d", campaignID, adgroupID)
	rd := cache.GetCRedis().GetClient()
	cmd := rd.Get(context.Background(), key)
	if cmd.Err() != nil && cmd.Err() != redis.Nil {
		return nil, cmd.Err()
	}
	if cmd.Err() == nil && cmd.Val() != "" {
		if err := json.Unmarshal([]byte(cmd.Val()), resp); err != nil {
			return nil, err
		}
		return resp, nil
	}
	token := s.GetToken()
	acl, err := s.GetACL(token)
	if err != nil {
		return nil, err
	}
	orgID := acl.Data[0].OrgID
	address := fmt.Sprintf("https://api.searchads.apple.com/api/v5/campaigns/%d/adgroups/%d/targetingkeywords",
		campaignID, adgroupID)
	header := http.Header{}
	header.Set("Authorization", fmt.Sprintf("Bearer %s", token.Value))
	header.Set("X-AP-Context", fmt.Sprintf("orgId=%d", orgID))
	body, err := util.HTTPRequest(address, "GET", header, nil)
	if err != nil {
		return nil, err
	}
	if err := json.Unmarshal(body, resp); err != nil {
		return nil, err
	}
	if err := rd.Set(context.Background(), key, string(body), time.Minute*30).Err(); err != nil {
		return nil, err
	}
	return resp, nil
}

type ACLResponse struct {
	Data []struct {
		OrgName      string   `json:"orgName"`
		OrgID        int      `json:"orgId"`
		Currency     string   `json:"currency"`
		TimeZone     string   `json:"timeZone"`
		PaymentModel string   `json:"paymentModel"`
		RoleNames    []string `json:"roleNames"`
		ParentOrgID  int      `json:"parentOrgId"`
	} `json:"data"`
	Pagination interface{} `json:"pagination"`
	Error      interface{} `json:"error"`
}

// GetACL 请求用户权限范围
func (s *Server) GetACL(token *AdsToken) (*ACLResponse, error) {
	const key = "ft_apple_ads_info_acl"
	resp := new(ACLResponse)
	rd := cache.GetCRedis().GetClient()
	cmd := rd.Get(context.Background(), key)
	if cmd.Err() != nil && cmd.Err() != redis.Nil {
		return nil, cmd.Err()
	}
	if cmd.Err() == nil && cmd.Val() != "" {
		if err := json.Unmarshal([]byte(cmd.Val()), resp); err != nil {
			return nil, err
		}
		s.formatACLOrgID(resp)
		return resp, nil
	}
	address := "https://api.searchads.apple.com/api/v5/acls"
	header := http.Header{}
	header.Set("Authorization", fmt.Sprintf("Bearer %s", token.Value))
	body, err := util.HTTPRequest(address, "GET", header, nil)
	if err != nil {
		return nil, err
	}
	if err := json.Unmarshal(body, resp); err != nil {
		return nil, fmt.Errorf("获取请求apple ads 用户权限失败 %s 响应内容:%s", err, string(body))
	}
	if resp.Error != nil {
		return nil, fmt.Errorf("%s", resp.Error)
	}
	if len(resp.Data) == 0 {
		return nil, fmt.Errorf("apple acl 没有权限 %s", string(body))
	}
	if err := rd.Set(context.Background(), key, string(body), time.Minute*100).Err(); err != nil {
		return nil, err
	}
	s.formatACLOrgID(resp)
	return resp, nil
}

// formatACLOrgID 处理ACL数据 分账号
func (s *Server) formatACLOrgID(resp *ACLResponse) {
	conf := config.Get().AppleAdsConfig
	for _, v := range resp.Data {
		if strings.Contains(v.OrgName, conf.OrgName) {
			resp.Data[0] = v
			break
		}
	}
}

// GetAdGroupResponse AdGroup 的响应
type GetAdGroupResponse struct {
	Data struct {
		ID         int    `json:"id"`
		CampaignID int    `json:"campaignId"`
		Name       string `json:"name"`
		CpaGoal    struct {
			Amount   string `json:"amount"`
			Currency string `json:"currency"`
		} `json:"cpaGoal"`
		StartTime              string `json:"startTime"`
		EndTime                string `json:"endTime"`
		AutomatedKeywordsOptIn bool   `json:"automatedKeywordsOptIn"`
		DefaultBidAmount       struct {
			Amount   string `json:"amount"`
			Currency string `json:"currency"`
		} `json:"defaultBidAmount"`
		PricingModel        string `json:"pricingModel"`
		TargetingDimensions struct {
			Age struct {
				Included []struct {
					MinAge int `json:"minAge"`
					MaxAge int `json:"maxAge"`
				} `json:"included"`
			} `json:"age"`
			Gender struct {
				Included []string `json:"included"`
			} `json:"gender"`
			Country     interface{} `json:"country"`
			AdminArea   interface{} `json:"adminArea"`
			Locality    interface{} `json:"locality"`
			DeviceClass struct {
				Included []string `json:"included"`
			} `json:"deviceClass"`
			Daypart struct {
				UserTime struct {
					Included []int `json:"included"`
				} `json:"userTime"`
			} `json:"daypart"`
			AppDownloaders interface{} `json:"appDownloaders"`
		} `json:"targetingDimensions"`
		OrgID               int         `json:"orgId"`
		ModificationTime    string      `json:"modificationTime"`
		Status              string      `json:"status"`
		ServingStatus       string      `json:"servingStatus"`
		ServingStateReasons interface{} `json:"servingStateReasons"`
		DisplayStatus       string      `json:"displayStatus"`
		Deleted             bool        `json:"deleted"`
	} `json:"data"`
}

// GetAdGroupByCampaignIDAdgroupID 获取 AdGroup
func (s *Server) GetAdGroupByCampaignIDAdgroupID(campaignID, adgroupID int) (*GetAdGroupResponse, error) {
	resp := new(GetAdGroupResponse)
	key := fmt.Sprintf("ft_apple_ads_info_adgroup_%d_%d", campaignID, adgroupID)
	rd := cache.GetCRedis().GetClient()
	cmd := rd.Get(context.Background(), key)
	if cmd.Err() != nil && cmd.Err() != redis.Nil {
		return nil, cmd.Err()
	}
	if cmd.Err() == nil && cmd.Val() != "" {
		if err := json.Unmarshal([]byte(cmd.Val()), resp); err != nil {
			return nil, err
		}
		return resp, nil
	}
	token := s.GetToken()
	acl, err := s.GetACL(token)
	if err != nil {
		return nil, err
	}
	orgID := acl.Data[0].OrgID
	address := fmt.Sprintf("https://api.searchads.apple.com/api/v5/campaigns/%d/adgroups/%d", campaignID, adgroupID)
	header := http.Header{}
	header.Set("Authorization", fmt.Sprintf("Bearer %s", token.Value))
	header.Set("X-AP-Context", fmt.Sprintf("orgId=%d", orgID))
	header.Set("Content-Type", "application/json")
	body, err := util.HTTPRequest(address, "GET", header, nil)
	if err != nil {
		return nil, err
	}
	if err := json.Unmarshal(body, resp); err != nil {
		return nil, err
	}
	if err := rd.Set(context.Background(), key, string(body), time.Minute*50).Err(); err != nil {
		return nil, err
	}
	return resp, nil
}

// nolint
func getKeywordAndMatchType(data *AdsInfoAdService) (keywordText, matchType string) {
	// 查找关键词
	keywordsByCampaign, err := ServerInstance.GetKeywordsByCampaignID(data.CampaignID)
	if err != nil {
		logger.Errorf(err.Error())
		return
	}
	if keywordsByCampaign == nil {
		return
	}

	var isFind bool
	for _, keyword := range keywordsByCampaign.Data {
		if keyword.ID == data.KeywordID {
			keywordText = keyword.Text
			matchType = keyword.MatchType
			isFind = true
			break
		}
	}

	// 由于苹果貌似会延迟,如果还没有找到,就去 adGroup 里再查一次
	if !isFind {
		keywords, err := ServerInstance.GetKeywordsByCampaignIDAdgroupID(data.CampaignID,
			data.AdGroupID)
		if err != nil {
			logger.Errorf(err.Error())
			return
		}
		for _, keyword := range keywords.Data {
			if keyword.ID == data.KeywordID {
				keywordText = keyword.Text
				matchType = keyword.MatchType
				break
			}
		}
	}
	return keywordText, matchType
}
