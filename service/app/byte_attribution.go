package app

import (
	"context"
	"encoding/json"
	"errors"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	proto "gitlab.dailyyoga.com.cn/protogen/srv-kafka-go"
	"gitlab.dailyyoga.com.cn/server/children/grpc"
	"gitlab.dailyyoga.com.cn/server/children/library"
	cl "gitlab.dailyyoga.com.cn/server/children/library/client"
)

type OceanEngine struct {
}
type BindAttributionReq struct {
	Platform    string `json:"platform"`
	IDfv        string `json:"idfv"`
	AndroidID   string `json:"android_id"`
	PackageName string `json:"package_name"`
	AnonymousID string `json:"anonymous_id"`
	Event       string `json:"event"`
}

// UploadInfo 巨量实时归因
func (*OceanEngine) UploadInfo(appClient *library.AppClient, req *BindAttributionReq) error {
	if req.PackageName == "" || req.AnonymousID == "" {
		return errors.New("必要参数为空")
	}
	platform, ok := cl.DeviceTypeDesc[cl.DeviceTypeInt(appClient.OsType)]
	if !ok || platform == "unknown" {
		return errors.New("未知系统类型")
	}
	if cl.DeviceTypeInt(appClient.OsType) == cl.DeviceTypeEnum.IOS && req.IDfv == "" {
		return errors.New("ios系统类型 IDfv 为空")
	}
	if (cl.DeviceTypeInt(appClient.OsType) == cl.DeviceTypeEnum.Android ||
		cl.DeviceTypeInt(appClient.OsType) == cl.DeviceTypeEnum.Harmony) && req.AndroidID == "" {
		return errors.New("安卓系统类型 Android ID 为空")
	}
	req.Platform = platform
	req.Event = "OceanEngine"
	payload, err := json.Marshal(req)
	if err != nil {
		return errors.New("json 处理失败")
	}
	client := grpc.GetKafkaClient()
	if _, err = client.Produce(context.Background(), &proto.ProducerRequest{
		Topic:   "topic_sat_subscribe", // 神策 SAT 数据订阅流
		Payload: string(payload),
		Key:     req.AnonymousID,
	}); err != nil {
		logger.Errorf("巨量实时归因上报,发送 kafka 失败 %s", err)
		return err
	}
	return nil
}
