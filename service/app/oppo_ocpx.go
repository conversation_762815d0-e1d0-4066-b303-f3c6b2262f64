package app

import (
	"encoding/base64"
	"encoding/json"
	"errors"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/crypto"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/databases/client"
	libc "gitlab.dailyyoga.com.cn/server/children/library/client"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type OppoOcpx struct{}

var SrvOppoOcpx OppoOcpx

const (
	AdvOPPO = iota
	AdvAttr
)

// AppActivation 激活事件上报oppo
func (*OppoOcpx) AppActivation(opcx *client.ChannelMarket) (bool, error) {
	oppo := &OppoOcpxUploadReq{
		OuID:     opcx.OaID,
		PkgName:  opcx.PkgName,
		DataType: int(libc.OppoOcpxTypeEnum.AppActivation),
	}
	utm := InstallUtmIsAD(opcx.AnonymousID)
	logger.Infof("F-OPPO 激活 utm:%+v ", utm)
	// nolint
	if utm != nil && utm.UtmSource == "【FT_OPPO】" {
		oppo.AdID, _ = strconv.Atoi(utm.ChannelADID)
		oppo.AscribeType = AdvAttr
	}
	logger.Infof("F-OPPO 激活 DistinctID:%s OaID:%s", opcx.AnonymousID, opcx.OaID)
	if UploadOppoOcpx(oppo) {
		return true, nil
	}
	return false, errors.New("F-OPPO 上报失败")
}

// UserRegister 注册事件上报oppo
func (*OppoOcpx) UserRegister(opcx *client.ChannelMarket) bool {
	oppo := &OppoOcpxUploadReq{
		OuID:        opcx.OaID,
		PkgName:     opcx.PkgName,
		DataType:    int(libc.OppoOcpxTypeEnum.Register),
		AscribeType: AdvOPPO,
	}
	utm := InstallUtmIsAD(opcx.AnonymousID)
	logger.Infof("F-OPPO 注册 utm:%+v ", utm)
	if utm != nil && utm.UtmSource == "【FT_OPPO】" {
		oppo.AdID, _ = strconv.Atoi(utm.ChannelADID)
		oppo.AscribeType = AdvAttr
	}
	logger.Infof("F-OPPO 注册 DistinctID:%s OaID:%s", opcx.AnonymousID, opcx.OaID)
	UploadOppoOcpx(oppo)
	return true
}

func (*OppoOcpx) FirstPurchase(amount float64, opcx *client.ChannelMarket) bool {
	var ascribeType, adID int
	utm := InstallUtmIsAD(opcx.AnonymousID)
	if utm != nil && utm.UtmSource == "【FT_OPPO】" {
		adID, _ = strconv.Atoi(utm.ChannelADID)
		ascribeType = AdvAttr
	}
	logger.Infof("F-OPPO 付费 DistinctID:%s OaID:%s", opcx.AnonymousID, opcx.OaID)
	if UploadOppoOcpx(&OppoOcpxUploadReq{
		OuID:        opcx.OaID,
		PkgName:     opcx.PkgName,
		DataType:    int(libc.OppoOcpxTypeEnum.Purchase),
		AdID:        adID,
		AscribeType: ascribeType,
	}) {
		var hundred float64 = 100
		a := math.Round(amount * hundred)
		UploadOppoOcpx(&OppoOcpxUploadReq{
			OuID:        opcx.OaID,
			PkgName:     opcx.PkgName,
			DataType:    int(libc.OppoOcpxTypeEnum.CustomGoal),
			PayAmount:   int(a),
			AdID:        adID,
			AscribeType: ascribeType,
		})
		return true
	}
	return false
}

type OppoOcpxUploadReq struct {
	OuID        string `json:"ouId"`
	PkgName     string `json:"pkg"`
	DataType    int    `json:"dataType"`
	Channel     int    `json:"channel"`
	Type        int    `json:"type"`
	PayAmount   int    `json:"payAmount,omitempty"`
	AscribeType int    `json:"ascribeType"`
	TimeStamp   int64  `json:"timestamp"`
	AdID        int    `json:"adId,omitempty"`
}
type OppoOcpxUploadRsp struct {
	Ret int `json:"ret"`
}

func UploadOppoOcpx(req *OppoOcpxUploadReq) bool {
	// 回传imei如果按照md5加密就是1，不加密的话就是0，回传oaid按md5加密是2
	req.Type = 0
	// 固定为1 OPPO手机
	req.Channel = 1
	req.TimeStamp = time.Now().UnixNano() / 1e6
	cfg := config.Get()
	keyByte, err := base64.StdEncoding.DecodeString(cfg.OppoOcpx.AesKey)
	if err != nil {
		logger.Error(err)
	}
	// OAID需要aes-128-ecb加密
	oaidByte := util.AesEncryptECB([]byte(req.OuID), keyByte)
	if err != nil {
		logger.Error(err)
		return false
	}
	req.OuID = string(oaidByte)
	header := http.Header{}
	reqByte, err := json.Marshal(req)
	if err != nil {
		logger.Error(err)
		return false
	}
	data := string(reqByte) + strconv.FormatInt(req.TimeStamp, 10) + cfg.OppoOcpx.Salt
	sign := crypto.Byte2Md5([]byte(data))
	header.Add("Content-Type", "application/json")
	header.Add("signature", sign)
	header.Add("timestamp", strconv.FormatInt(req.TimeStamp, 10))
	uri := "https://api.ads.heytapmobi.com/api/uploadActiveData"
	res, err := util.HTTPRequest(uri, http.MethodPost, header, strings.NewReader(string(reqByte)))
	logger.Infof("F-OPPO uploadOppoOcpx DataType:%d res:%+v resp:%s",
		req.DataType,
		*req,
		string(res),
	)
	if err != nil {
		logger.Warn(err)
		return false
	}
	rsp := OppoOcpxUploadRsp{}
	if err := json.Unmarshal(res, &rsp); err != nil {
		return false
	}
	return true
}
