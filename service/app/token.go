package app

import (
	"encoding/json"
	"net/http"
	"strings"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

const (
	AppID        = 107940133                                                          // 应用id
	ClientID     = "1204437931826105984"                                              // client Id
	ClientSecret = "E9370D8CD8B70C136A3D8FD0D2F43D44C1EAA09EE7AC274E6D59937540E37AA4" // nolint
	TokenURL     = "https://connect-api.cloud.huawei.com/api/oauth2/v1/token"         // nolint
	PushURL      = "https://connect-api.cloud.huawei.com/api/datasource/v1/track/activate"
	OAID         = "OAID"
)

const (
	DeviceIDTypeOther = 1 // other
	NoPush            = 0 // 未推送
	PushSuccess       = 1 // 推送成功
	PushFailed        = 2 // 推送失败
)

type huaweiToken struct {
}

var HuaweiTokenService huaweiToken

type RequestTokenJSON struct {
	GrantType    string `json:"grant_type"`
	ClientID     string `json:"client_id"`
	ClientSecret string `json:"client_secret"`
}

// ResponseJSON token返回结构
type ResponseJSON struct {
	ExpiresIn   int64  `json:"expires_in"`
	AccessToken string `json:"access_token"`
	ErrorRet    `json:"ErrorRet"`
}

// ErrorRet 失败结构
type ErrorRet struct {
	Code int64  `json:"code"`
	Msg  string `json:"msg"`
}

// OCPDToken Token机构
type OCPDToken struct {
	Token    string
	LoseTime int64
}

// GetAccessToken 获取华为ocpd请求token
func (t *huaweiToken) GetAccessToken() (OCPDToken, error) {
	token := OCPDToken{}
	p, _ := json.Marshal(RequestTokenJSON{"client_credentials", ClientID, ClientSecret})
	header := http.Header{}
	header.Add("Content-Type", "application/json")
	body, err := util.HTTPRequest(TokenURL, "POST", header, strings.NewReader(string(p)))
	if err != nil {
		return token, err
	}
	jsonResponse := ResponseJSON{}
	decodeErr := json.Unmarshal(body, &jsonResponse)
	logger.Infof("华为token返回信息:%v", jsonResponse)
	if decodeErr != nil {
		return token, decodeErr
	}
	token.Token = jsonResponse.AccessToken
	token.LoseTime = time.Now().Unix() + jsonResponse.ExpiresIn
	return token, nil
}
