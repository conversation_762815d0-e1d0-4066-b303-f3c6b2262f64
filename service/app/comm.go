package app

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/util"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	db "gitlab.dailyyoga.com.cn/server/children/databases/client"
	"gitlab.dailyyoga.com.cn/server/children/databases/ocpx"
	"gitlab.dailyyoga.com.cn/server/children/library/client"
)

type Activation struct {
	OuID        string  `json:"ou_id"`
	AnonymousID string  `json:"anonymous_id"`
	PkgName     string  `json:"pkg_name"`
	ChannelStr  string  `json:"channel_str"`
	UID         int64   `json:"uid"`
	Amount      float64 `json:"amount"`
	ActionType  int64   `json:"action_type"`
	IsFrom      int     `json:"is_from"` // 用于判断是否是延迟队列回调过来的
}

const InvalidOAID = "00000000-0000-0000-0000-000000000000"

// AppActivation 激活
// nolint
func (o *Activation) AppActivation() (bool, error) {
	return true, nil
	if o.OuID == InvalidOAID {
		return false, nil
	}
	if o.HasHandleDelayQueue() {
		return true, nil
	}
	cMarket := db.TbMarket.GetItemByAnonymousID(o.AnonymousID, o.ChannelStr)
	if cMarket != nil && cMarket.IsAdSource != library.Yes {
		return false, nil
	}
	cMarket = &db.ChannelMarket{
		OaID:        o.OuID,
		Channel:     o.ChannelStr,
		AnonymousID: o.AnonymousID,
		PkgName:     o.PkgName,
		IsAdSource:  library.No,
		CreateTime:  time.Now().Unix(),
	}
	if err := cMarket.Save(); err != nil {
		logger.Warn(err)
		return false, nil
	}
	fns := map[string]func(db *db.ChannelMarket) (bool, error){
		client.ChannelXiaomi: SrvXiaomiMarket.AppActivation,
		client.ChannelOPPO:   SrvOppoOcpx.AppActivation,
	}
	var response bool
	var err error
	if fn, ok := fns[o.ChannelStr]; ok {
		response, err = fn(cMarket)
	}
	return response, err
}

// UserRegister 注册
// nolint
func (o *Activation) UserRegister() {
	return
	if o.OuID == InvalidOAID {
		return
	}
	if o.HasHandleDelayQueue() {
		return
	}
	if o.ChannelStr == client.ChannelHuawei {
		SrvHuaweiOcpx.UserRegister(o.OuID, &db.ChannelMarket{
			OaID:    o.OuID,
			UID:     o.UID,
			Channel: o.ChannelStr,
		})
		return
	}
	cMarket := db.TbMarket.GetItemByAnonymousID(o.AnonymousID, o.ChannelStr)
	logger.Infof("F-OPPO 注册的时候还没有激活数据 anonymousID：%s ", o.AnonymousID)
	if cMarket == nil {
		return
	}
	isAD := library.No
	if InstallUtmIsAD(cMarket.AnonymousID) != nil {
		isAD = library.Yes
	}
	if cMarket.UID == 0 {
		cMarket.UID = o.UID
		cMarket.IsAdSource = isAD
		if err := cMarket.Update(); err != nil {
			return
		}
	} else {
		cMarketNew := &db.ChannelMarket{
			OaID:        cMarket.OaID,
			AnonymousID: cMarket.AnonymousID,
			PkgName:     cMarket.PkgName,
			UID:         o.UID,
			Channel:     o.ChannelStr,
			IsAdSource:  isAD,
			CreateTime:  cMarket.CreateTime,
		}
		if err := cMarketNew.Save(); err != nil {
			logger.Warn(err)
			return
		}
		cMarket = cMarketNew
	}
	fns := map[string]func(db *db.ChannelMarket) bool{
		client.ChannelXiaomi: SrvXiaomiMarket.UserRegister,
		client.ChannelOPPO:   SrvOppoOcpx.UserRegister,
	}
	if fn, ok := fns[o.ChannelStr]; ok {
		fn(cMarket)
	}
}

// FirstPurchase 首购
func (o *Activation) FirstPurchase() bool {
	if o.HasHandleDelayQueue() {
		return true
	}
	item := ocpx.TbOCPD.GetItemByUID(o.UID)
	if item != nil && strconv.Itoa(int(item.ChannelMe)) == client.ChannelHuawei {
		SrvHuaweiOcpx.FirstPurchase(o.Amount, &db.ChannelMarket{
			OaID:    item.DeviceID,
			UID:     item.UID,
			Channel: client.ChannelHuawei,
		})
		return true
	}
	cMarket := db.TbMarket.GetItemByUID(o.UID)
	if cMarket == nil {
		return false
	}
	cMarket.FirstBuyTime = time.Now().Unix()
	defer func() {
		err := cMarket.Update()
		if err != nil {
			logger.Warn(err)
		}
	}()
	if cMarket.IsAdSource == library.Yes || (InstallUtmIsAD(cMarket.AnonymousID) != nil) {
		cMarket.IsAdSource = library.Yes
	}
	fns := map[string]func(amount float64, db *db.ChannelMarket) bool{
		client.ChannelXiaomi: SrvXiaomiMarket.FirstPurchase,
		client.ChannelOPPO:   SrvOppoOcpx.FirstPurchase,
	}
	if fn, ok := fns[cMarket.Channel]; ok {
		return fn(o.Amount, cMarket)
	}
	return false
}

type Utm struct {
	AnonymousID      string `json:"anonymous_id"`
	PlatformType     string `json:"platform_type"`
	ChannelClickTime int64  `json:"channel_click_time"`
	UtmSource        string `json:"utm_source"`
	UtmMedium        string `json:"utm_medium"`
	UtmTerm          string `json:"utm_term"`
	UtmContent       string `json:"utm_content"`
	UtmCampaign      string `json:"utm_campaign"`
	ChannelADID      string `json:"channel_ad_id"` // 广告ID 目前oppo回传的时候需要
}

// InstallUtmIsAD 判断安装来源是不是广告来的
func InstallUtmIsAD(anonymousID string) *Utm {
	if anonymousID == "" {
		return nil
	}
	utmData, err := cache.GetYoga01Redis().GetClient().Get(context.Background(),
		fmt.Sprintf("AppInstall:%s", anonymousID)).Result()
	logger.Infof("F-OPPO 数据 anonymousID：%s  utm:%s ", anonymousID, utmData)
	if err != nil {
		return nil
	}
	var utStruct Utm
	if err := json.Unmarshal([]byte(utmData), &utStruct); err != nil {
		return nil
	}
	if strings.Contains(utStruct.UtmSource, "FT") {
		return &utStruct
	}
	return nil
}

// HasHandleDelayQueue 处理队列
func (o *Activation) HasHandleDelayQueue() bool {
	if o.ChannelStr == client.ChannelOPPO && o.IsFrom == 0 {
		data, err := json.Marshal(o)
		if err != nil {
			logger.Error("π-OPPO延迟队列format json err", err.Error())
			return false
		}
		queueDelay := int64(library.OPPOQueueDelaySeconds)
		if o.ActionType == client.OppoOcpxTypeEnum.Register.ToInt64() {
			queueDelay = int64(library.OPPOQueueDelaySeconds) + 1
		}
		if err := util.AddToQueue(context.Background(),
			cache.GetYoga01Redis().GetClient(),
			library.OPPOQueueKey, string(data), queueDelay); err != nil {
			logger.Error("π-OPPO延迟队列错误", err.Error())
			return false
		}
		return true
	}
	return false
}

func GetMiniAppNameByClient(appClient *library.AppClient) string {
	name := ""
	switch strconv.Itoa(appClient.Channel) {
	case client.ChannelTiktok:
		name = "小树苗运动计划"
	case client.ChannelWechatMiNiApp:
		name = "小树苗运动"
	}
	return name
}
