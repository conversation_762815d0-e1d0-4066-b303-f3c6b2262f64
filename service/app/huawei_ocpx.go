package app

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/gokit/sensorsdata"
	"gitlab.dailyyoga.com.cn/server/children/databases/client"
	db "gitlab.dailyyoga.com.cn/server/children/databases/ocpx"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libuser "gitlab.dailyyoga.com.cn/server/children/library/user"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

const (
	MinuteSeconds = 60
	FailedTimes   = 3
	SuccessStatus = 200
)

type HuaweiOcpx struct{}

var SrvHuaweiOcpx HuaweiOcpx

type HuaweiPushInfo struct {
	HuaweiChannel  string
	HuaweiCallBack string
	HuaweiTaskID   string
	CustomerID     string
	ActionType     int64
	ActionParam    string
	Channel        int64
	DistinctID     string
	UID            int64
}

// PushBody 向华为推送的ocpd的body结构 需要转成json
type PushBody struct {
	AppID        int64  `json:"appId"`
	DeviceIDType string `json:"deviceIdType"`
	DeviceID     string `json:"deviceId"`
	ActionTime   int64  `json:"actionTime"`
	ActionType   string `json:"actionType"`
	Callback     string `json:"callBack"`
	ActionParam  string `json:"actionParam,omitempty"`
}

type ActionParamItem struct {
	Name  string `json:"name"`
	Value int64  `json:"value"`
}

type responseMessage struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

// FirstPurchase 首购事件上报
func (*HuaweiOcpx) FirstPurchase(amount float64, opcx *client.ChannelMarket) bool {
	actionParam := make([]*ActionParamItem, 0)
	actionParam = append(actionParam, &ActionParamItem{
		Name:  "付费金额",
		Value: int64(amount),
	})
	parasStr, _ := json.Marshal(actionParam)
	channel, _ := strconv.ParseInt(opcx.Channel, 10, 64)
	req := &HuaweiPushInfo{
		CustomerID:  opcx.OaID,
		ActionType:  library.AppPay,
		UID:         opcx.UID,
		Channel:     channel,
		ActionParam: string(parasStr),
	}
	logger.Infof("huawei 付费 DistinctID:%s OaID:%s", opcx.AnonymousID, opcx.OaID)
	status, _ := SrvHuaweiOcpx.Huawei(req)
	return status
}

// UserRegister 注册事件上报
func (*HuaweiOcpx) UserRegister(oaID string, opcx *client.ChannelMarket) {
	channel, _ := strconv.ParseInt(opcx.Channel, 10, 64)
	req := &HuaweiPushInfo{
		CustomerID: oaID,
		ActionType: library.AppRegister,
		UID:        opcx.UID,
		Channel:    channel,
	}
	logger.Infof("π-HuaWEI 注册  OaID:%s", oaID)
	_, _ = SrvHuaweiOcpx.Huawei(req)
}

// nolint
func (*HuaweiOcpx) Huawei(pushInfo *HuaweiPushInfo) (status bool, err error) {
	return true, nil
	now := time.Now().Unix()
	data := &db.OCPD{
		TaskID:       pushInfo.HuaweiTaskID,
		Channel:      pushInfo.HuaweiChannel,
		CallBack:     pushInfo.HuaweiCallBack,
		DeviceID:     pushInfo.CustomerID,
		DeviceIDType: DeviceIDTypeOther,
		ActionTime:   now,
		ActionType:   pushInfo.ActionType,
		PushStatus:   NoPush,
		CreateTime:   now,
		UpdateTime:   now,
		UID:          pushInfo.UID,
		ChannelMe:    pushInfo.Channel,
	}
	rd := cache.GetYoga01Redis().GetClient()
	cacheKey := fmt.Sprintf("cs:hw:ocpd:%v", pushInfo.CustomerID)
	if pushInfo.ActionType != library.AppActive {
		cacheID, err := rd.Get(context.Background(), cacheKey).Int()
		if err != nil || cacheID == 0 {
			return true, nil
		}
		item := db.TbOCPD.GetItemByID(int64(cacheID))
		if item == nil || item.ID == 0 {
			return true, nil
		}
		if pushInfo.ActionType == library.AppRegister {
			item.UID = pushInfo.UID
			if err := item.Update(); err != nil {
				return false, err
			}
		}
		data.Channel = item.Channel
		data.TaskID = item.TaskID
		data.DeviceID = item.DeviceID
		data.CallBack = item.CallBack
	}
	cacheKeyOaid := fmt.Sprintf(libuser.OcpdOaidKey+"%s", pushInfo.CustomerID)
	cacheUID, _ := rd.Get(context.Background(), cacheKeyOaid).Int64()
	if cacheUID != 0 {
		data.UID = cacheUID
	}
	insertID, insertErr := db.TbOCPD.Insert(data)
	if insertErr != nil {
		return false, insertErr
	}
	// 先有激活事件，这个数据为客户端上报的，没有这个数据就没办法进行后续的操作
	if pushInfo.ActionType == library.AppActive {
		err = rd.Set(context.Background(), cacheKey, strconv.FormatInt(insertID, 10), 15*util.SecondsPerDay*time.Second).Err()
		if err != nil {
			logger.Warnf("ocpd 设置出错 %s", err.Error())
		}
	}
	// （付费）支持OCPD投放“付费”及“首日ROI”两个目标投放 首日ROI需要这个字段
	var actionParam []ActionParamItem
	if pushInfo.ActionType == library.AppPay && pushInfo.ActionParam != "" {
		err = json.Unmarshal([]byte(pushInfo.ActionParam), &actionParam)
		if err != nil {
			logger.Error("parse json failed, err:" + err.Error())
		}
	}
	safelygo.GoSafelyByTraceID(func() {
		SyncPush(pushInfo.UID, insertID, data, actionParam)
	})
	return true, nil
}

// nolint
func SyncPush(distinctID, insertID int64, data *db.OCPD, param []ActionParamItem) {
	times := 0
	for {
		// 失败大于三次认为失败,这边会有一种极端情况就是保存失败状态失败,对于这种不做处理
		if times > FailedTimes {
			data.PushStatus = PushFailed
			data.UpdateTime = time.Now().Unix()
			_, _ = db.TbOCPD.Save(insertID, data)
			return
		}
		// 首次进入直接执行 后续进入等待60秒
		if times > 0 {
			time.Sleep(time.Second * MinuteSeconds)
		}
		// 读取token
		token, err := HuaweiTokenService.GetAccessToken()
		if err != nil {
			times++
			continue
		}
		// 发送信息
		/*pushErr := SrvHuaweiOcpx.OcpdPush(token, data, param)
		if pushErr != nil {
			// 等待客户端 上报数据正确以后  在做日志打印
			logger.Debugf("opcd huawei syncPush Err %+v", pushErr)
			times++
			continue
		}*/
		// 记录入库
		data.PushStatus = PushSuccess
		data.PushFinishTime = time.Now().Unix()
		data.AccessToken = token.Token
		data.AccessTokenExpires = token.LoseTime
		data.UpdateTime = time.Now().Unix()
		_, updateErr := db.TbOCPD.Save(insertID, data)
		if updateErr != nil {
			times++
			continue
		} else {
			logger.Info("--------fit华为ocpd推送记录入库--------")
			if distinctID != 0 {
				// 修改用户属性
				p := map[string]interface{}{
					"huawei_channel":  data.Channel,
					"huawei_callback": data.CallBack,
					"huawei_taskid":   data.TaskID,
				}
				logger.Infof("用户属性:uid:%v,其他:%v", distinctID, p)
				sensorsdata.ProfileSet(distinctID, p, true)
			}
			return
		}
	}
}

// OcpdPush 向华为服务器发送ocpd数据
func (*HuaweiOcpx) OcpdPush(token OCPDToken, data *db.OCPD, aParam []ActionParamItem) error {
	body := PushBody{
		AppID:        AppID,
		DeviceID:     data.DeviceID,
		DeviceIDType: OAID,
		ActionTime:   data.ActionTime * 1000, // 华为要求为毫秒
		ActionType:   strconv.FormatInt(data.ActionType, 10),
		Callback:     data.CallBack,
	}
	if len(aParam) > 0 {
		aParamJSONStr, _ := json.Marshal(aParam)
		body.ActionParam = string(aParamJSONStr)
	}
	param, jsonErr := json.Marshal(body)
	if jsonErr != nil {
		return jsonErr
	}
	c := http.Client{}
	c.Transport = &http.Transport{
		DisableKeepAlives: true,
	}
	c.Timeout = 10 * time.Second
	request, requestErr := http.NewRequestWithContext(context.Background(), "POST", PushURL, bytes.NewReader(param))
	if requestErr != nil {
		return requestErr
	}
	request.Header.Set("client_id", ClientID)
	request.Header.Set("Authorization", fmt.Sprintf("Bearer %s", token.Token))
	response, err := c.Do(request)
	if err != nil {
		return err
	}
	defer response.Body.Close()
	logger.Infof("fit-华为push返回信息:%s  %+v", string(param), response)
	if response.StatusCode != SuccessStatus {
		return fmt.Errorf("ocpd请求状态码为:%d", response.StatusCode)
	}
	respBody, err := io.ReadAll(response.Body)
	if err != nil {
		return err
	}
	var result responseMessage
	err = json.Unmarshal(respBody, &result)
	if err != nil {
		return err
	}
	if result.Code != 0 {
		return fmt.Errorf("ocpd 返回数据错误码:%d 错误信息为:%s 请求数据:%s",
			result.Code, result.Msg, string(param))
	}
	return nil
}
