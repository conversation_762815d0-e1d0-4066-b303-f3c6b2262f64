package app

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"

	libcache "gitlab.dailyyoga.com.cn/server/children/library/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/util"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
)

type tiktokOcean struct{}

var SrvTiktok tiktokOcean

func (*tiktokOcean) FirstPurchase(uid int64, amount float64) bool {
	// 关键行为上报
	timeNow := time.Now().UnixNano() / 1e6
	UploadOcean(uid, &OceanReq{
		EventType: "game_addiction",
		Properties: struct {
			PayAmount float64 `json:"pay_amount"`
		}{
			PayAmount: amount * 100,
		},
		Timestamp: timeNow,
	})
	// 付费上报
	return UploadOcean(uid, &OceanReq{
		EventType: "active_pay",
		Properties: struct {
			PayAmount float64 `json:"pay_amount"`
		}{
			PayAmount: amount * 100,
		},
		Timestamp: timeNow,
	})
}

type OceanReq struct {
	EventType string `json:"event_type"`
	Context   struct {
		Ad struct {
			Callback string `json:"callback"`
		} `json:"ad"`
	} `json:"context"`
	Properties struct {
		PayAmount float64 `json:"pay_amount"`
	} `json:"properties,omitempty"`
	Timestamp int64 `json:"timestamp"`
}

type oceanRsp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

const clickAct = "active"

func UploadOcean(uid int64, req *OceanReq) bool {
	ctx := context.Background()
	rd := cache.GetCRedis().GetClient()
	clickID, err := rd.Get(ctx, key(uid)).Result()
	if err != nil && err != redis.Nil {
		logger.Error(err.Error())
		return false
	}
	logger.Info("UploadOcean clickID -1", uid)
	if clickID != "" && req.EventType == clickAct {
		return false
	}
	logger.Info("UploadOcean clickID -2", uid)
	if err == redis.Nil || clickID == "" {
		// 只有active场景会setCache
		if req.EventType != clickAct {
			return false
		}
		setCallback(ctx, uid, req.Context.Ad.Callback)
	} else {
		req.Context.Ad.Callback = clickID
	}
	logger.Info("UploadOcean clickID -3", uid)
	header := http.Header{}
	reqByte, err := json.Marshal(req)
	if err != nil {
		logger.Error(err)
		return false
	}
	logger.Info("UploadOcean clickID -4", uid)
	header.Add("Content-Type", "application/json")
	uri := "https://analytics.oceanengine.com/api/v2/conversion"
	res, err := sendHTTPRequest(uri, http.MethodPost, header, reqByte)
	if err != nil {
		logger.Warn(err)
		return false
	}
	logger.Info("UploadOcean clickID -5", uid)
	rsp := oceanRsp{}
	if err := json.Unmarshal(res, &rsp); err != nil {
		return false
	}
	logger.Info("UploadOcean clickID -6", uid, string(res))
	return true
}

func key(uid int64) string {
	return fmt.Sprintf("%s%d", libcache.OceanEnginePrefix, uid)
}

func setCallback(ctx context.Context, uid int64, callback string) {
	hr, _ := time.ParseDuration("720h")
	rd := cache.GetCRedis().GetClient()
	rd.SetEX(ctx, key(uid), callback, hr)
}

func sendHTTPRequest(uri, method string, header http.Header, reqByte []byte) ([]byte, error) {
	res, err := util.HTTPRequest(uri, method, header, strings.NewReader(string(reqByte)))
	return res, err
}
