package app

import (
	"crypto/md5" //nolint
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/databases/client"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type xiaomiMarket struct{}

var SrvXiaomiMarket xiaomiMarket

const (
	ConvTypeAPPActive   = "APP_ACTIVE"
	ConvTypeAPPRegister = "APP_REGISTER"
	ConvTypeAPPFirstBuy = "APP_FIRST_PAY"
	ConvTypeAPPPay      = "APP_PAY"
)

// AppActivation xiaomi激活事件上报
func (*xiaomiMarket) AppActivation(cMarket *client.ChannelMarket) (bool, error) {
	req := &XiaomiMarketReq{
		OAID:     cMarket.OaID,
		ConvTime: int(cMarket.CreateTime) * 1000,
		ConvType: ConvTypeAPPActive,
	}
	return uploadXiaomiMarket(req), nil
}

// UserRegister 注册事件上报
func (*xiaomiMarket) UserRegister(cMarket *client.ChannelMarket) bool {
	req := &XiaomiMarketReq{
		OAID:     cMarket.OaID,
		ConvTime: int(cMarket.CreateTime) * 1000,
		ConvType: ConvTypeAPPRegister,
	}
	return uploadXiaomiMarket(req)
}

func (*xiaomiMarket) FirstPurchase(amount float64, cMarket *client.ChannelMarket) bool {
	if time.Now().Unix()-cMarket.CreateTime > library.DayTime {
		return true
	}
	req := &XiaomiMarketReq{
		OAID:       cMarket.OaID,
		ConvTime:   int(cMarket.CreateTime) * 1000,
		ConvType:   ConvTypeAPPPay,
		ConvWeight: amount,
	}
	return uploadXiaomiMarket(req)
}

type pair struct {
	key   string
	value string
}

type XiaomiMarketReq struct {
	OAID       string
	ConvTime   int
	ConvType   string
	ConvWeight float64
}

type XiaomiMarketRsp struct {
	Success bool `json:"success"`
}

func uploadXiaomiMarket(req *XiaomiMarketReq) bool {
	device := []pair{
		{"oaid", req.OAID},
		{"conv_time", strconv.Itoa(req.ConvTime)},
	}
	cfg := config.Get().XiaomiMarket
	logger.Info(device)
	signKey, encryptKey := getSecret(cfg.Secret, req.ConvType)
	if signKey == "" || encryptKey == "" {
		return false
	}
	appID := cfg.APPID
	convType := req.ConvType
	customerID := cfg.CustomerID
	finalInfo := buildInfo(device, signKey, encryptKey)
	logger.Info("XiaomiMarket 请求信息", finalInfo, device)
	finalURL := fmt.Sprintf("http://trail.e.mi.com/global/log?appId=%s&info=%s&conv_type=%s&customer_id=%s",
		appID, finalInfo, convType, customerID)
	if req.ConvWeight > 0 {
		finalURL = fmt.Sprintf("%s&conv_weight=%.2f", finalURL, req.ConvWeight)
	}
	logger.Info("XiaomiMarket url", finalURL)
	header := http.Header{}
	header.Add("Content-Type", "application/x-www-form-urlencoded")
	body, err := util.HTTPRequest(finalURL, "GET", header, nil)
	if err != nil {
		logger.Warn(err)
		return false
	}
	logger.Info("XiaomiMarket 返回信息", string(body))
	rsp := &XiaomiMarketRsp{}
	if err := json.Unmarshal(body, rsp); err != nil {
		logger.Warn(err)
		return false
	}
	if rsp.Success {
		logger.Info("XiaomiMarket 小米上报成功", req.OAID, req.ConvType, string(body))
		return true
	}
	logger.Warn("XiaomiMarket 小米上报失败", req.OAID, req.ConvType, string(body))
	return false
}

// 根据不同转化类型获取密钥
func getSecret(cfg []config.XiaomiMarketSecret, convType string) (sign, encrypt string) {
	if len(cfg) == 0 {
		return
	}
	for k := range cfg {
		if cfg[k].ConvType == convType {
			return cfg[k].SignKey, cfg[k].EncryptKey
		}
	}
	return
}

func buildInfo(device []pair, signKey, encryptKey string) string {
	// queryString 设备信息
	query := ""
	for i, p := range device {
		and := ""
		if i != 0 {
			and = "&"
		}
		query += fmt.Sprintf("%s%s=%s", and, p.key, p.value)
	}
	query1 := url.QueryEscape(query)
	// md5后的sign值
	sign := fmt.Sprintf("%x", md5.Sum([]byte(signKey+"&"+query1))) //nolint
	// baseData
	baseData := query + "&sign=" + sign
	return enc(baseData, encryptKey)
}

// 对baseData进行加密 Base64(simple_xor{base_data, encrypt_key})
func enc(baseData, key string) string {
	l2 := len(key)
	var res []byte
	for i := range baseData {
		u := baseData[i] ^ key[i%l2]&0xFF
		res = append(res, u)
	}
	return url.QueryEscape(base64.StdEncoding.EncodeToString(res))
}
