package collect

import (
	"strconv"
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	dbcollect "gitlab.dailyyoga.com.cn/server/children/databases/collect"
	dbuser "gitlab.dailyyoga.com.cn/server/children/databases/user"
	lib "gitlab.dailyyoga.com.cn/server/children/library/collect"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
)

type Relation struct {
	UID int64

	PageNum  int
	PageSize int

	ResourceType int
	ResourceIds  []string

	ResourceID int64
}

// GetUserCourseCollectList 收藏课程ids
func (r *Relation) GetUserCourseCollectList() []int64 {
	collects := dbcollect.TbCollect.GetList(r.UID, int(lib.ResourceTypeEnum.Course), r.<PERSON>, r.PageSize)
	ids := make([]int64, 0)
	for _, collect := range collects {
		ids = append(ids, collect.ResourceID)
	}
	return ids
}

// GetUserCollectList 收藏课程ids
func (r *Relation) GetUserCollectList() []*dbcollect.Collect {
	return dbcollect.TbCollect.GetListByPage(r.UID, r.PageNum, r.PageSize)
}

func (r *Relation) GetUserCourseCollectListAll() []int64 {
	collects := dbcollect.TbCollect.GetListAll(r.UID, int(lib.ResourceTypeEnum.Course))
	ids := make([]int64, 0)
	for _, collect := range collects {
		ids = append(ids, collect.ResourceID)
	}
	return ids
}

// AddCollect 添加收藏
func (r *Relation) AddCollect() errorcode.ErrorCode {
	var collects = make([]*dbcollect.Collect, 0)
	var resourceIds = make([]int64, 0)
	now := time.Now().Unix()
	for _, ResourceIDStr := range r.ResourceIds {
		resourceID, err := strconv.Atoi(ResourceIDStr)
		if err != nil {
			logger.Warnf("解析收藏ID失败: %v", err)
			continue
		}
		if dbcollect.TbCollect.IsCollected(r.UID, r.ResourceType, int64(resourceID)) {
			continue
		}
		collect := &dbcollect.Collect{
			UID:          r.UID,
			ResourceType: r.ResourceType,
			ResourceID:   int64(resourceID),
			CreateTime:   now,
		}
		collects = append(collects, collect)
		resourceIds = append(resourceIds, int64(resourceID))
	}
	// 开启事务
	var rollBackErr error
	session := databases.GetEngineMaster().NewSession()
	if err := session.Begin(); err != nil {
		logger.Error(err)
		return errorcode.DBError
	}
	defer session.Close()
	defer func() {
		if rollBackErr != nil {
			logger.Error(rollBackErr)
			if err := session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()

	// 添加收藏
	var err error
	if err = dbcollect.TbCollect.BatchInsertByTran(session, collects); err != nil {
		logger.Error(err)
		return errorcode.DBError
	}
	// 更新收藏数量
	safelygo.GoSafelyByTraceID(func() {
		if err = incrCollectCount(session, r, resourceIds); err != nil {
			logger.Error(err)
		}
	})
	if err = session.Commit(); err != nil {
		return errorcode.DBError
	}
	return errorcode.Success
}

// DelCollect 取消收藏
func (r *Relation) DelCollect() errorcode.ErrorCode {
	var resourceIds = make([]int64, 0)
	for _, ResourceIDStr := range r.ResourceIds {
		resourceID, err := strconv.Atoi(ResourceIDStr)
		if err != nil {
			logger.Warnf("解析收藏ID失败: %v", err)
			continue
		}
		if resourceID == 0 {
			continue
		}
		resourceIds = append(resourceIds, int64(resourceID))
	}
	if len(resourceIds) == 0 {
		return errorcode.Success
	}
	if !dbcollect.TbCollect.IsCollectedByIds(r.UID, r.ResourceType, resourceIds) {
		return errorcode.InvalidParams
	}
	// 开启事务
	var rollBackErr error
	session := databases.GetEngineMaster().NewSession()
	if err := session.Begin(); err != nil {
		logger.Error(err)
		return errorcode.DBError
	}
	defer session.Close()
	defer func() {
		if rollBackErr != nil {
			logger.Error(rollBackErr)
			if err := session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()
	var err error
	// 取消收藏
	if err = dbcollect.TbCollect.BatchDelByTran(session, r.UID, r.ResourceType, resourceIds); err != nil {
		logger.Error(err)
		return errorcode.DBError
	}
	// 更新收藏数量
	safelygo.GoSafelyByTraceID(func() {
		if err = decrCollectCount(session, r, resourceIds); err != nil {
			logger.Error(err)
		}
	})
	if err = session.Commit(); err != nil {
		return errorcode.DBError
	}
	return errorcode.Success
}

// nolint
func incrCollectCount(session *xorm.Session, r *Relation, resourceIds []int64) (err error) {
	switch lib.ResourceTypeInt(r.ResourceType) {
	case lib.ResourceTypeEnum.Course:
		// 添加用户课程收藏数
		if err = dbuser.TbAccount.IncrCollectCountByTran(session, r.UID, resourceIds); err != nil {
			logger.Error(err)
			return err
		}
	}
	return err
}

// nolint
func decrCollectCount(session *xorm.Session, r *Relation, resourceIds []int64) (err error) {
	switch lib.ResourceTypeInt(r.ResourceType) {
	case lib.ResourceTypeEnum.Course:
		// 扣减用户课程收藏数
		if err = dbuser.TbAccount.DecrCollectCountByTran(session, r.UID, resourceIds); err != nil {
			logger.Error(err)
			return err
		}
	}
	return err
}
