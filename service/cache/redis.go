package cache

import (
	"context"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
)

// Client redis client
type Client struct {
	*redis.Client
}

// NewClient Connecting to Redis Server
func NewClient(host, password string) (*Client, error) {
	c := &Client{}
	c.Client = redis.NewClient(&redis.Options{
		Addr:     host,
		Password: password,
		// 设置连接池大小
		PoolSize: 10,
		// 设置最小空闲连接数
		MinIdleConns: 5,
	})

	// test connect using ping command
	err := c.Client.Ping(context.Background()).Err()

	return c, err
}

type Redis struct {
	c          *Client
	expiration time.Duration
}

var (
	stRedis     *Redis
	yogaRedis01 *Redis
	yogaRedis   *Redis
)

func Starter(ms *microservice.Microservice) {
	conf := ms.GetConf().GetViperConf()
	address := conf.GetString("children_redis.address")
	password := conf.GetString("children_redis.password")
	if err := InitRedis(address, password); err != nil {
		logger.Error(err)
	}
	address01 := conf.GetString("yoga_redis_01.address")
	password01 := conf.GetString("yoga_redis_01.password")
	if err := InitYoga01Redis(address01, password01); err != nil {
		logger.Error(err)
	}
	yogaAddress := conf.GetString("redis.address")
	yogaPassword01 := conf.GetString("redis.password")
	if err := InitYogaRedis(yogaAddress, yogaPassword01); err != nil {
		logger.Error(err)
	}
}

func InitRedis(address, password string) error {
	c, err := NewClient(address, password)
	if err != nil {
		return err
	}
	stRedis = &Redis{
		c:          c,
		expiration: 10 * time.Second,
	}
	return nil
}

func InitYoga01Redis(address, password string) error {
	c, err := NewClient(address, password)
	if err != nil {
		return err
	}
	yogaRedis01 = &Redis{
		c:          c,
		expiration: 10 * time.Second,
	}
	return nil
}

func InitYogaRedis(address, password string) error {
	c, err := NewClient(address, password)
	if err != nil {
		return err
	}
	yogaRedis = &Redis{
		c:          c,
		expiration: 10 * time.Second,
	}
	return nil
}

func GetYogaRedis() *Redis {
	return yogaRedis
}

func GetCRedis() *Redis {
	return stRedis
}

func (r *Redis) GetClient() *Client {
	return r.c
}

func GetYoga01Redis() *Redis {
	return yogaRedis01
}

type LockOption func(*Redis)

func LockExpiration(t time.Duration) LockOption {
	return func(r *Redis) {
		r.expiration = t
	}
}

func (r *Redis) Lock(k string, opts ...LockOption) bool {
	v := strconv.FormatInt(time.Now().Unix(), 10)
	ctx := context.Background()

	for _, opt := range opts {
		opt(r)
	}
	if r.expiration == 0 {
		r.expiration = time.Second * 10
	}

	ret, err := r.c.SetNX(ctx, k, v, r.expiration).Result()
	if err != nil {
		return false
	}
	return ret
}

func (r *Redis) Unlock(k string) error {
	ctx := context.Background()
	if err := r.c.Del(ctx, k).Err(); err != nil && err != redis.Nil {
		return err
	}
	return nil
}

const RedisErrNil = "redis: nil"

func (r *Redis) Storage(k, v string, t time.Duration) bool {
	_, err := r.GetClient().Set(context.Background(), k, v, t).Result()
	if err != nil {
		logger.Warn(err)
		return false
	}
	return true
}

func (r *Redis) Load(k string) string {
	cst, err := r.GetClient().Get(context.Background(), k).Result()
	if err != nil && err.Error() != RedisErrNil {
		return ""
	}
	return cst
}
