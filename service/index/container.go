package index

import (
	"context"
	"encoding/json"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/protogen/children-user-group-go/childrenusergroup"
	dbcourse "gitlab.dailyyoga.com.cn/server/children/databases/course"
	dbi "gitlab.dailyyoga.com.cn/server/children/databases/index"
	"gitlab.dailyyoga.com.cn/server/children/grpc"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libclient "gitlab.dailyyoga.com.cn/server/children/library/client"
	libcourse "gitlab.dailyyoga.com.cn/server/children/library/course"
	libi "gitlab.dailyyoga.com.cn/server/children/library/index"
	"gitlab.dailyyoga.com.cn/server/children/service/audit"
	"gitlab.dailyyoga.com.cn/server/children/service/course"
	libP "gitlab.dailyyoga.com.cn/server/children/service/product"
	srvuser "gitlab.dailyyoga.com.cn/server/children/service/user"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type contanier struct{}

var SrvContainer contanier

const GroupIDDefault = 1
const DefaultIndexGroupHome = 2

// 获取用户身份对应的分群下的容器
// nolint
func (c *contanier) GetUserGroupContainer(uid, resourceTab int64) ([]*dbi.Container, int64) {
	indexGroupID := c.GetUserIndexGroupID(uid, resourceTab)
	indexGroupIDDefault := int64(0)
	switch resourceTab {
	case libi.ResourceTypeHome, libi.ResourceTypeDef:
		indexGroupIDDefault = int64(DefaultIndexGroupHome)
	case libi.ResourceTypePractice:
		indexGroupIDDefault = GroupIDDefault
	default:
	}
	if indexGroupID == 0 {
		indexGroupID = indexGroupIDDefault
	}
	grList := dbi.TbGroupContainer.GetList(indexGroupID, resourceTab)
	if len(grList) == 0 && indexGroupID != indexGroupIDDefault {
		logger.Info("首页容器:原分群ID下无内容，查询默认分群", uid)
		grList = dbi.TbGroupContainer.GetList(indexGroupIDDefault, resourceTab)
	}
	idList := make([]int64, 0)
	for _, v := range grList {
		idList = append(idList, v.ContainerID)
	}
	logger.Info("首页容器:用户要展示的容器列表", uid, idList)
	containerList := dbi.TbContainer.GetList(idList)
	res := make([]*dbi.Container, 0)
	for _, v := range idList {
		for _, c := range containerList {
			if c.BeginTime > time.Now().Unix() || (c.EndTime > 0 && c.EndTime < time.Now().Unix()) {
				continue
			}
			if v == c.ID {
				res = append(res, c)
			}
		}
	}
	return res, indexGroupID
}

func (c *contanier) GetUserIndexGroupID(uid, resourceTab int64) int64 {
	if uid == 0 {
		return 0
	}
	groupList := dbi.TbUserGroup.GetListByStatus(library.Yes, resourceTab)
	if len(groupList) == 0 {
		return 0
	}
	groupIDList := make([]int64, 0)
	for _, v := range groupList {
		if v.UserGroupID == 0 {
			continue
		}
		groupIDList = append(groupIDList, v.UserGroupID)
	}
	logger.Info("首页容器:有效的分群列表", uid, groupIDList, resourceTab)
	groupClient := grpc.GetChildrenGroupClient()
	rsp, err := groupClient.ValidGroup(context.Background(), &childrenusergroup.ValidGroupRequest{
		UID:           uid,
		GroupLabelIDs: groupIDList,
	})
	if err != nil {
		logger.Error(err)
		return 0
	}
	var indexGroupID int64
	validGroupList := rsp.GetGroupLabelID()
	logger.Info("首页容器:用户所在的分群列表", uid, groupIDList, validGroupList, resourceTab)
	for _, v := range groupList {
		if library.InArray(v.UserGroupID, validGroupList) {
			if grList := dbi.TbGroupContainer.GetList(v.ID, resourceTab); len(grList) > 0 {
				indexGroupID = v.ID
				break
			}
		}
	}
	logger.Info("首页容器:用户匹配到的分群ID为", uid, indexGroupID)
	return indexGroupID
}

type ContainerListRsp struct {
	List []*ContainerItem `json:"list"`
}

type Banner struct {
	Link       *library.Link         `json:"link"`
	BannerImg  *library.ImageInfo    `json:"banner_img"`
	ID         int64                 `json:"id"`
	PraiseInfo *srvuser.ResourceData `json:"praise_info,omitempty"`
}
type VideoStreamResp struct {
	VideoURL   string             `json:"video_url,omitempty"`
	VideoType  int                `json:"video_type,omitempty"`
	CourseInfo *course.ItemCourse `json:"course_info,omitempty"`
	StartTime  int                `json:"start_time,omitempty"`
	EndTime    int                `json:"end_time,omitempty"`
	CoverImg   string             `json:"cover_img,omitempty"`
}

type ResourceInfo struct {
	BannerInfo  *Banner          `json:"banner,omitempty"`
	ProductInfo *library.SkuItem `json:"product_info,omitempty"`
}

type ContainerItem struct {
	ContainerID   int64         `json:"container_id"`
	ContainerType int64         `json:"container_type"`
	Title         string        `json:"title"`
	BgImg         string        `json:"bg_img,omitempty"`
	ResourceInfo  interface{}   `json:"resource_info,omitempty"`
	ResourceList  []interface{} `json:"resource_list,omitempty"`
	SubTitle      string        `json:"sub_title,omitempty"`
}

type ContainerResourceItem struct {
	ResourceID     int64       `json:"resource_id"`
	MarketTitle    string      `json:"market_title"`
	MarketSubTitle string      `json:"market_sub_title"`
	TitleCover     string      `json:"title_cover,omitempty"`
	BgType         int         `json:"bg_type,omitempty"`
	BgColorStart   string      `json:"bg_color_start,omitempty"`
	BgColorEnd     string      `json:"bg_color_end,omitempty"`
	BgImg          string      `json:"bg_img,omitempty"`
	Labels         []string    `json:"labels,omitempty"`
	ResourceInfo   interface{} `json:"resource_info,omitempty"`
	ResourceList   interface{} `json:"resource_list,omitempty"`
}

type DBContainerResourceItem struct {
	ResourceID     int64                    `json:"resource_id"`
	ResourceType   int64                    `json:"resource_type"`
	MarketTitle    string                   `json:"market_title"`
	MarketSubTitle string                   `json:"market_sub_title,omitempty"`
	Labels         string                   `json:"labels,omitempty"`
	TitleCover     string                   `json:"title_cover,omitempty"`
	BgType         int                      `json:"bg_type,omitempty"`
	BgColorStart   string                   `json:"bg_color_start,omitempty"`
	BgColorEnd     string                   `json:"bg_color_end,omitempty"`
	BgImg          string                   `json:"bg_img,omitempty"`
	AliasTitle     string                   `json:"alias_title"`
	CourseList     []*DBContainerCourseItem `json:"course_list,omitempty"`
}

type ConfigParam struct {
	LinkType       int    `json:"link_type"`
	LinkContent    string `json:"link_content,omitempty"`
	LinkContentIos string `json:"link_content_ios,omitempty"`
	BannerImgPhone string `json:"banner_img_phone"`   // 图片地址
	BannerImgPad   string `json:"banner_img_pad"`     // 图片地址
	PraiseResID    int64  `json:"praise_resource_id"` // 资源id
}

type DBContainerCourseItem struct {
	ID           int64  `json:"id"`
	ResourceType int64  `json:"resource_type"`
	AliasTitle   string `json:"alias_title"`
}

type FormatContainerListRsp struct {
	List          []*ContainerItem `json:"list"`
	FreeCourseIDs []int64          `json:"free_course_ids"`
}

type VideoStreaming struct {
	Sort               int    `json:"sort,omitempty"`
	VideoType          int    `json:"video_type,omitempty"`
	CoverImgPhone      string `json:"cover_img_phone,omitempty"`
	CoverImgPad        string `json:"cover_img_pad,omitempty"`
	PlanID             int64  `json:"plan_id,omitempty"`
	CourseID           int64  `json:"course_id,omitempty"`
	StartTimeMinute    int    `json:"start_time_minute,omitempty"`
	EndTimeMinute      int    `json:"end_time_minute,omitempty"`
	StartTimeSecond    int    `json:"start_time_second,omitempty"`
	EndTimeSecond      int    `json:"end_time_second,omitempty"`
	EffectiveStartTime int64  `json:"effective_start_time,omitempty"`
	EffectiveEndTime   int64  `json:"effective_end_time,omitempty"`
	VideoURL           string `json:"video_url,omitempty"`
}

// 金刚区
type SessionFilterItem struct {
	LabelID   int                `json:"label_id"`
	LabelName string             `json:"label_name"`
	Image     *library.ImageInfo `json:"image"`
}

// 容器列表
// nolint
func (c *contanier) FormatContainerList(containerList []*dbi.Container,
	uid int64, appClient *library.AppClient, obChannel *libP.ObUserChannel, resourceTab int64) FormatContainerListRsp {
	res := FormatContainerListRsp{
		List:          make([]*ContainerItem, 0),
		FreeCourseIDs: make([]int64, 0),
	}
	if len(containerList) == 0 {
		return res
	}
	cMap := make(map[int64]*ContainerItem)
	var wg sync.WaitGroup
	var lock sync.Mutex
	for k := range containerList {
		item := &ContainerItem{
			ContainerID:   containerList[k].ID,
			ContainerType: containerList[k].ContainerType,
			Title:         containerList[k].Name,
			BgImg:         containerList[k].BgImgPhone,
			SubTitle:      containerList[k].Title,
		}
		if appClient.ScreenType == int(libclient.ScreenTypeEnum.Pad) {
			item.BgImg = containerList[k].BgImgPad
		}
		wg.Add(1)
		cItem := containerList[k]
		safelygo.GoSafelyByTraceID(func() {
			defer wg.Done()
			list := GetResourceList(cItem, uid, appClient)
			if len(list) == 0 && !library.Int64InArray(cItem.ContainerType, []int64{
				int64(libi.ContainerTypeEnum.CourseCollect),
				int64(libi.ContainerTypeEnum.CustomCard),
				int64(libi.ContainerTypeEnum.GuessLike),
				int64(libi.ContainerTypeEnum.RecentPractice),
				int64(libi.ContainerTypeEnum.VideoStreaming),
				int64(libi.ContainerTypeEnum.SessionFilter),
			}) {
				return
			}
			switch cItem.ContainerType {
			case int64(libi.ContainerTypeEnum.CustomCard):
				logger.Infof("CustomCard uid:%d item:%+v citem:%+v", uid, item, cItem)
				item.ResourceInfo = GetResourceInfo(cItem, appClient, uid, obChannel)
				if item.ResourceInfo == nil {
					return
				}
			case int64(libi.ContainerTypeEnum.VideoStreaming):
				if util.UVersion.LtVersion(appClient, 10200, 10200) {
					return
				}
				item.ResourceInfo = VideoStreamingProcess(cItem, uid, appClient)
				if item.ResourceInfo == nil {
					return
				}
			default:
				item.ResourceList = list
			}
			lock.Lock()
			defer lock.Unlock()
			cMap[item.ContainerID] = item
		})
	}
	wg.Wait()
	for k := range containerList {
		if cInfo, ok := cMap[containerList[k].ID]; ok {
			res.List = append(res.List, cInfo)
		}
	}
	isHuaweiAudit := false
	if obChannel.AboutAudit.IsInAudit && resourceTab == library.Yes &&
		(strconv.Itoa(appClient.Channel) == libclient.ChannelHuawei || strconv.Itoa(appClient.Channel) == libclient.ChannelXiaomi ||
			strconv.Itoa(appClient.Channel) == libclient.ChannelOPPO || strconv.Itoa(appClient.Channel) == libclient.ChannelVIVO) {
		isHuaweiAudit = true
	}
	if audit.GetCompliance(uid, appClient) && resourceTab == library.Yes && strconv.Itoa(appClient.Channel) == libclient.ChannelHuawei {
		isHuaweiAudit = true
	}
	isJump := false
	for i := 0; i < len(res.List); i++ {
		if !isHuaweiAudit {
			break
		}
		if isJump {
			break
		}
		if res.List[i].ContainerType == int64(libi.ContainerTypeEnum.HorizontalCourse) ||
			res.List[i].ContainerType == int64(libi.ContainerTypeEnum.VerticalCourse) {
			for j := range res.List[i].ResourceList {
				if j == 3 {
					isJump = true
					break
				}
				res.List[i].ResourceList[j].(*ContainerResourceItem).ResourceInfo.(*course.ItemCourse).IsVIP = library.No
				res.FreeCourseIDs = append(res.FreeCourseIDs,
					res.List[i].ResourceList[j].(*ContainerResourceItem).ResourceInfo.(*course.ItemCourse).ID)
			}
		}
	}
	return res
}

// nolint
func GetResourceList(container *dbi.Container, uid int64, appClient *library.AppClient) []interface{} {
	dbJSONList := make([]*DBContainerResourceItem, 0)
	err := json.Unmarshal([]byte(container.ResourceList), &dbJSONList)
	if err != nil {
		logger.Error(err)
		return nil
	}
	switch libi.ContainerTypeInt(container.ContainerType) {
	case libi.ContainerTypeEnum.HorizontalCourse, libi.ContainerTypeEnum.VerticalCourse:
		return ContainerCourse(dbJSONList, uid, appClient)
	case libi.ContainerTypeEnum.BillboardCourse:
		return ContainerListCourse(dbJSONList, uid, appClient)
	case libi.ContainerTypeEnum.SessionFilter:
		if util.UVersion.LtVersion(appClient, 10300, 10300) {
			return nil
		}
		return GetSessionFilter(container)
	default:
	}
	return make([]interface{}, 0)
}

// nolint
func GetResourceInfo(container *dbi.Container, appClient *library.AppClient, uid int64,
	obChannel *libP.ObUserChannel) *ResourceInfo {
	logger.Info("GetPraiseInfoByID-1", uid, obChannel.Compliance)
	if obChannel.Compliance {
		return nil
	}
	configParam := &ConfigParam{}
	err := json.Unmarshal([]byte(container.ConfigParam), &configParam)
	if err != nil {
		logger.Info(err)
		return nil
	}
	if configParam.LinkType == int(library.LinkTypeEnum.InnerLink) && util.UVersion.LtVersion(appClient, 10100, 10100) {
		return nil
	}
	if configParam.LinkType == int(library.LinkTypeEnum.Praise) && util.UVersion.LtVersion(appClient, 10200, 10200) {
		return nil
	}
	bannerImg := configParam.BannerImgPhone
	if libclient.ScreenType(appClient.ScreenType) == libclient.ScreenTypeEnum.Pad {
		bannerImg = configParam.BannerImgPad
	}
	linkContent := configParam.LinkContent
	if libclient.DeviceTypeInt(appClient.OsType) == libclient.DeviceTypeEnum.IOS {
		linkContent = configParam.LinkContentIos
	}
	bannerImgResp := &library.ImageInfo{}
	if err := json.Unmarshal([]byte(bannerImg), &bannerImgResp); err != nil {
		logger.Error(err)
		return nil
	}
	logger.Info("GetPraiseInfoByID-2", uid, configParam.LinkType)
	if configParam.LinkType == int(library.LinkTypeEnum.Course) {
		courseID, _ := strconv.Atoi(linkContent)
		item := dbcourse.TbCourseLibrary.GetItem(int64(courseID))
		if item == nil || item.IsOnline == library.No {
			return nil
		}
	}
	banner := &ResourceInfo{
		BannerInfo: &Banner{
			Link: &library.Link{
				LinkType:    library.LinkTypeInt(configParam.LinkType),
				LinkContent: linkContent,
			},
			BannerImg: bannerImgResp,
		},
	}
	if configParam.LinkType == int(library.LinkTypeEnum.Praise) &&
		util.UVersion.GtVersion(appClient, 10200, 10200) {
		if configParam.PraiseResID == 0 {
			return nil
		}
		praiseFinish := srvuser.GetPraiseInfoByID(uid, configParam.PraiseResID, appClient, obChannel)
		logger.Info("GetPraiseInfoByID--===", uid, configParam.PraiseResID, obChannel.Compliance)
		if praiseFinish == nil {
			return nil
		}
		banner.BannerInfo.PraiseInfo = praiseFinish
	}
	return banner
}

type ArticleItem struct {
	ID        int64  `json:"id"`
	Title     string `json:"title"`
	Content   string `json:"content"`
	SourceDes string `json:"source_des"`
	Cover     string `json:"cover"`
	ViewNum   int64  `json:"view_num"`
}

// nolint
func ContainerCourse(list []*DBContainerResourceItem, uid int64,
	appClient *library.AppClient) []interface{} {
	res := make([]interface{}, 0)
	courseIDList := make([]int64, 0)
	planIDList := make([]int64, 0)
	for _, v := range list {
		if v.ResourceType == libi.ResourcePlan {
			planIDList = append(planIDList, v.ResourceID)
		} else {
			courseIDList = append(courseIDList, v.ResourceID)
		}
	}
	srvCourse := &course.SrvOptionCourse{}
	courseList := srvCourse.BatchGetCourseDetail(uid, courseIDList, appClient, &libcourse.OptionCourse{
		IsOnline: library.Yes,
	})
	logger.Infof("容器课程和计划结构:%+v集合ids:%+v", len(courseList), courseIDList)
	for _, v := range list {
		rItem := &ContainerResourceItem{
			ResourceID:     v.ResourceID,
			MarketTitle:    v.MarketTitle,
			MarketSubTitle: v.MarketSubTitle,
			Labels:         strings.Split(v.Labels, ","),
		}
		for k := range courseList {
			if courseList[k].ID == v.ResourceID {
				if v.AliasTitle != "" {
					courseList[k].Title = v.AliasTitle
				}
				rItem.ResourceInfo = courseList[k]
				break
			}
		}
		if rItem.ResourceInfo == nil {
			continue
		}
		res = append(res, rItem)
	}
	return res
}

// nolint
func ContainerListCourse(list []*DBContainerResourceItem, uid int64,
	appClient *library.AppClient) []interface{} {
	res := make([]interface{}, 0)
	courseIDList := make([]int64, 0)
	planIDList := make([]int64, 0)
	for _, v := range list {
		for _, c := range v.CourseList {
			if c.ResourceType == libi.ResourcePlan {
				planIDList = append(planIDList, c.ID)
			} else {
				courseIDList = append(courseIDList, c.ID)
			}
		}
	}
	srvCourse := &course.SrvOptionCourse{}
	courseList := srvCourse.BatchGetCourseDetail(uid, courseIDList, appClient, &libcourse.OptionCourse{
		IsOnline: library.Yes,
	})
	for _, v := range list {
		rItem := &ContainerResourceItem{
			TitleCover:   v.TitleCover,
			BgType:       v.BgType,
			BgColorStart: v.BgColorStart,
			BgColorEnd:   v.BgColorEnd,
			BgImg:        v.BgImg,
			Labels:       strings.Split(v.Labels, ","),
		}
		if len(v.CourseList) == 0 {
			continue
		}
		ResourceList := make([]interface{}, 0)
		for _, c := range v.CourseList {
			for _, ci := range courseList {
				if ci.ID == c.ID {
					if c.AliasTitle != "" {
						ci.Title = c.AliasTitle
					}
					ResourceList = append(ResourceList, ci)
					break
				}
			}
		}
		if len(ResourceList) == 0 {
			continue
		}
		rItem.ResourceList = ResourceList
		res = append(res, rItem)
	}
	return res
}
func VideoStreamingProcess(container *dbi.Container, uid int64,
	appClient *library.AppClient) *VideoStreamResp {
	dbJSONList := make([]*VideoStreaming, 0)
	err := json.Unmarshal([]byte(container.ResourceList), &dbJSONList)
	if err != nil {
		logger.Error(err)
		return nil
	}
	sort.Slice(dbJSONList, func(i, j int) bool {
		return dbJSONList[i].Sort > dbJSONList[j].Sort
	})
	effectiveData := &VideoStreaming{}
	for _, v := range dbJSONList {
		if v.EffectiveStartTime < time.Now().Unix() && v.EffectiveEndTime > time.Now().Unix() {
			effectiveData = v
			break
		}
	}
	if effectiveData == nil {
		return nil
	}
	item := dbcourse.TbCourseLibrary.GetItem(effectiveData.CourseID)
	if item == nil || item.IsOnline == library.No {
		return nil
	}
	srvCourse := &course.SrvOptionCourse{WithLabel: true}
	courseInfo := srvCourse.GetCourseDetail(uid, effectiveData.CourseID, &libcourse.OptionCourse{
		IsOnline: library.Yes,
	}, appClient, course.WithLabel())
	if courseInfo == nil {
		return nil
	}
	if effectiveData.VideoURL == "" {
		effectiveData.VideoURL = courseInfo.VideoURL
	}
	if appClient.ScreenType != int(libclient.ScreenTypeEnum.Phone) {
		effectiveData.CoverImgPhone = effectiveData.CoverImgPad
	}
	if effectiveData.CoverImgPhone == "" {
		effectiveData.CoverImgPhone = courseInfo.CoverURL
	}
	return &VideoStreamResp{
		VideoURL:   effectiveData.VideoURL,
		VideoType:  effectiveData.VideoType,
		CourseInfo: courseInfo,
		StartTime:  effectiveData.StartTimeMinute*60 + effectiveData.StartTimeSecond,
		EndTime:    effectiveData.EndTimeMinute*60 + effectiveData.EndTimeSecond,
		CoverImg:   effectiveData.CoverImgPhone,
	}
}

// GetPracticeJingangDistrict 获取金刚区
func GetPracticeJingangDistrict() []interface{} {
	resp := make([]interface{}, 0)
	for i := range libi.JingangDistrict {
		jdID := libi.JingangDistrict[i]
		labelName := libi.JingangDistrictNameMap[jdID]
		icon := libi.JingangDistrictIconMap[jdID]
		resp = append(resp, SessionFilterItem{
			LabelID:   jdID,
			LabelName: labelName,
			Image:     util.FormatImageInfo(icon),
		})
	}
	return resp
}

type ResourceSessionFilter struct {
	LabelID      int                `json:"label_id"`
	SubLabelID   int                `json:"sub_label_id"`
	SubLabelName string             `json:"sub_label_name"`
	Image        *library.ImageInfo `json:"image"`
	Sort         int                `json:"sort"`
}

func GetSessionFilter(item *dbi.Container) []interface{} {
	resp := make([]interface{}, 0)
	sessionFilterList := make([]*ResourceSessionFilter, 0)
	if err := json.Unmarshal([]byte(item.ResourceList), &sessionFilterList); err != nil {
		logger.Warn("容器SessionLabel,解析错误", item.ID, item.ResourceList)
		return nil
	}
	sort.Slice(sessionFilterList, func(i, j int) bool {
		return sessionFilterList[i].Sort > sessionFilterList[j].Sort
	})
	for _, filter := range sessionFilterList {
		resp = append(resp, filter)
	}
	return resp
}
