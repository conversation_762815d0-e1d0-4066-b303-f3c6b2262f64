package rop

import (
	"context"
	"fmt"
	"strconv"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	rPot "gitlab.dailyyoga.com.cn/protogen/children-rop-go/children-rop"
	"gitlab.dailyyoga.com.cn/server/children/grpc"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libc "gitlab.dailyyoga.com.cn/server/children/library/client"
	lib "gitlab.dailyyoga.com.cn/server/children/library/rop"
)

type serv struct{}

// Serv rop服务调用
var Serv serv

type SceneRop struct {
	AbtType    int64       `json:"abt_type"`    // ABT测试场景
	AbtVersion string      `json:"abt_version"` // Rop后台版本号
	AbtContent interface{} `json:"abt_content"` // Rop测试配置内容
}

func (r *serv) getFetchData(data *rPot.FetchRequest) (map[string]map[string]string, SceneRop) {
	res := make(map[string]map[string]string)
	sRop := SceneRop{}
	sRop.AbtType = data.Scenes[0].ID
	resp, err := grpc.GetChildrenRopClient().Fetch(context.Background(), data)
	if err != nil {
		logger.Error("rop服务出错", err)
		return res, sRop
	}
	if !resp.OK {
		return res, sRop
	}
	if len(resp.Data) > 0 {
		for _, v := range resp.Data {
			if s, ok := v.Scene.Params[lib.DefaultParamsKey]; ok {
				if len(v.Experiments[0].Version.Config) > 0 {
					if _, ok := res[s]; !ok {
						res[s] = make(map[string]string)
					}
					sRop.AbtVersion = v.Experiments[0].Version.ID
					res[s] = v.Experiments[0].Version.Config
				}
			}
		}
	}
	return res, sRop
}

type PaymentPage struct {
	PageID int64
}

// GetPageAbt 付费方案页
func (r *serv) GetPageAbt(uid int64, appClient *library.AppClient) (*PaymentPage, SceneRop) {
	data := &rPot.FetchRequest{
		Identify: &rPot.ApiIdentify{UID: fmt.Sprintf("%d", uid)},
		Scenes:   make([]*rPot.ApiScene, 0),
	}
	apiScenesData := getAPIScenes(appClient, lib.ChannelPaymentPage)
	data.Scenes = append(data.Scenes, apiScenesData.Scenes...)
	res, sRop := r.getFetchData(data)
	logger.Info("getFetchData:", res)
	paymentPage := &PaymentPage{}
	if len(res) > 0 {
		// 获取当前版本号
		for _, device := range apiScenesData.RopDevice {
			dType := strconv.Itoa(device)
			if v, ok := res[dType][lib.PaymentPageID]; ok {
				paymentPage.PageID, _ = strconv.ParseInt(v, 10, 64)
			}
			if paymentPage.PageID != 0 {
				break
			}
		}
	}
	return paymentPage, sRop
}

type apiSceneResp struct {
	RopDevice []int
	Scenes    []*rPot.ApiScene
}

func getAPIScenes(appClient *library.AppClient, sceneID int) *apiSceneResp {
	ch := strconv.Itoa(appClient.Channel)
	data := &apiSceneResp{}
	switch ch {
	case libc.ChannelApple:
		data.Scenes = []*rPot.ApiScene{
			{
				ID: int64(sceneID),
				Params: map[string]string{
					lib.DefaultParamsKey: strconv.Itoa(lib.DeviceTypeIos),
				},
			},
			{
				ID: int64(sceneID),
				Params: map[string]string{
					lib.DefaultParamsKey: strconv.Itoa(lib.DeviceTypeAll),
				},
			},
		}
		data.RopDevice = []int{lib.DeviceTypeIos, lib.DeviceTypeAll}
	case libc.ChannelHuawei, libc.ChannelHarmonyHuawei:
		data.Scenes = []*rPot.ApiScene{
			{
				ID: int64(sceneID),
				Params: map[string]string{
					lib.DefaultParamsKey: strconv.Itoa(lib.DeviceTypeAndroidHUAWEI),
				},
			},
			{
				ID: int64(sceneID),
				Params: map[string]string{
					lib.DefaultParamsKey: strconv.Itoa(lib.DeviceTypeAndroid),
				},
			},
			{
				ID: int64(sceneID),
				Params: map[string]string{
					lib.DefaultParamsKey: strconv.Itoa(lib.DeviceTypeAll),
				},
			},
		}
		data.RopDevice = []int{lib.DeviceTypeAndroidHUAWEI, lib.DeviceTypeAndroid, lib.DeviceTypeAll}
	default:
		data.Scenes = []*rPot.ApiScene{
			{
				ID: int64(sceneID),
				Params: map[string]string{
					lib.DefaultParamsKey: strconv.Itoa(lib.DeviceTypeAndroidNOHUAWEI),
				},
			},
			{
				ID: int64(sceneID),
				Params: map[string]string{
					lib.DefaultParamsKey: strconv.Itoa(lib.DeviceTypeAndroid),
				},
			},
			{
				ID: int64(sceneID),
				Params: map[string]string{
					lib.DefaultParamsKey: strconv.Itoa(lib.DeviceTypeAll),
				},
			},
		}
		data.RopDevice = []int{lib.DeviceTypeAndroidNOHUAWEI, lib.DeviceTypeAndroid, lib.DeviceTypeAll}
	}
	return data
}

// GetPageAbt 计划生成页
func (r *serv) GetPlanGenerationPage(uid int64, appClient *library.AppClient) (int64, SceneRop) {
	data := &rPot.FetchRequest{
		Identify: &rPot.ApiIdentify{UID: fmt.Sprintf("%d", uid)},
		Scenes:   make([]*rPot.ApiScene, 0),
	}
	apiScenesData := getAPIScenes(appClient, lib.ChannelPlanGenerationPage)
	data.Scenes = append(data.Scenes, apiScenesData.Scenes...)
	res, sRop := r.getFetchData(data)
	logger.Info("getFetchData:", res)
	pageID := int64(0)
	if len(res) > 0 {
		// 获取当前版本号
		for _, device := range apiScenesData.RopDevice {
			dType := strconv.Itoa(device)
			if v, ok := res[dType][lib.PlanGenerationPageID]; ok {
				pageID, _ = strconv.ParseInt(v, 10, 64)
			}
			if pageID != 0 {
				break
			}
		}
	}
	if pageID == 0 {
		pageID = library.Yes
	}
	return pageID, sRop
}

func (r *serv) GetTallerObSwitch(uid int64, appClient *library.AppClient) (int64, SceneRop) {
	data := &rPot.FetchRequest{
		Identify: &rPot.ApiIdentify{UID: fmt.Sprintf("%d", uid)},
		Scenes:   make([]*rPot.ApiScene, 0),
	}
	apiScenesData := getAPIScenes(appClient, lib.TallerOb)
	data.Scenes = append(data.Scenes, apiScenesData.Scenes...)
	res, sRop := r.getFetchData(data)
	if len(res) > 0 {
		// 获取当前版本号
		switchKey := lib.TallerObSwitch
		for _, device := range apiScenesData.RopDevice {
			dType := strconv.Itoa(device)
			if v, ok := res[dType][switchKey]; ok {
				obSwitch, err := strconv.ParseInt(v, 10, 64)
				if err == nil {
					return obSwitch, sRop
				}
			}
		}
	}
	return 0, sRop
}
