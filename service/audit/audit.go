package audit

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/gokit/sensorsdata"
	gpb "gitlab.dailyyoga.com.cn/protogen/yoga-ip-geo-go/yoga-ip-geo"
	"gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/databases/client"
	"gitlab.dailyyoga.com.cn/server/children/grpc"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libcache "gitlab.dailyyoga.com.cn/server/children/library/cache"
	libc "gitlab.dailyyoga.com.cn/server/children/library/client"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/sensor"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type ConfigInfo struct {
	IOSAuditVersion    string `json:"ios_audit_version"`
	IsIOSChannelIAP    bool   `json:"is_ios_channel_iap"`
	VivoAuditVersion   string `json:"vivo_audit_version"`
	HuaweiAuditVersion string `json:"huawei_audit_version"`
}

type AboutAudit struct {
	IsInAudit       bool
	IsIOSChannelIAP bool
}

const (
	SensorChannelPage   = "付费"
	SensorChannelNature = "自然"
)

// GetAuditInfo 审核开关
func GetAuditInfo(appClient *library.AppClient) *AboutAudit {
	res := &AboutAudit{
		IsInAudit:       false,
		IsIOSChannelIAP: true,
	}
	auditInfo := &ConfigInfo{}
	androidAudit := &androidAuditConfig{}
	if appClient.OsType == int(libc.DeviceTypeEnum.IOS) {
		cInfo := client.TbConfig.GetItemByKey(libc.ConfigKeyAuditInfo)
		if cInfo == nil {
			return res
		}

		err := json.Unmarshal([]byte(cInfo.Value), auditInfo)
		if err != nil {
			return res
		}
	} else {
		cInfo := client.TbConfig.GetItemByKey(libc.ConfigKeyAndroidAuditInfo)
		if cInfo == nil {
			return res
		}
		err := json.Unmarshal([]byte(cInfo.Value), androidAudit)
		if err != nil {
			return res
		}
	}
	res.IsInAudit = isInAudit(appClient, auditInfo, androidAudit)
	res.IsIOSChannelIAP = auditInfo.IsIOSChannelIAP
	return res
}

type FuncSwitch struct {
	Audit        bool
	OnlineSwitch bool
}

// GetAuditInfo 审核开关
func GetFuncSwitchByKey(appClient *library.AppClient, funcKey string, isChannel int) *FuncSwitch {
	res := &FuncSwitch{}
	if appClient.OsType == int(libc.DeviceTypeEnum.IOS) {
		return res
	}
	androidAudit := &androidAuditConfig{}
	cInfo := client.TbConfig.GetItemByKey(libc.ConfigKeyAndroidAuditInfo)
	if cInfo == nil {
		return res
	}
	err := json.Unmarshal([]byte(cInfo.Value), androidAudit)
	if err != nil {
		return res
	}
	conf := getComplianceConfig()
	confChannel := getComplianceConfigChannel(appClient, conf)
	isAudit := isInAudit(appClient, &ConfigInfo{}, androidAudit)
	if confChannel != nil {
		for _, v := range confChannel.Switch {
			if v.Key != funcKey {
				continue
			}
			res.Audit = isAudit
			if funcKey != libc.OnlineConfLoginBuy {
				res.OnlineSwitch = v.Value
			} else if v.Value && isChannel == libc.UserChanneDef {
				res.OnlineSwitch = true
			}
		}
	}
	return res
}

type androidAuditConfig struct {
	ClientChannel       []string                     `json:"client_channel"`
	AndroidAuditVersion string                       `json:"android_audit_version"`
	AllChannel          []string                     `json:"all_channel"`
	OnlineConf          map[string][]*OnlineConfItem `json:"online_conf"`
}
type OnlineConfItem struct {
	Name  string `json:"name"`
	Key   string `json:"key"`
	Value bool   `json:"value"`
}

func isInAudit(appClient *library.AppClient, auditInfo *ConfigInfo, androandroidAudit *androidAuditConfig) bool {
	versionConfig := ""
	// 合规升级 安卓审核 对vivo和Huawei 进行合并
	if appClient.OsType == int(libc.DeviceTypeEnum.IOS) {
		versionConfig = auditInfo.IOSAuditVersion
	} else {
		androidAudit := androandroidAudit
		if len(androidAudit.ClientChannel) < 1 {
			return false
		}
		// 渠道转换 -1是其他
		channelConversion := "-1"
		versionConfig = androidAudit.AndroidAuditVersion
		if library.StringInArray(strconv.Itoa(appClient.Channel), androidAudit.AllChannel) {
			channelConversion = strconv.Itoa(appClient.Channel)
		}
		if !library.StringInArray(channelConversion, androidAudit.ClientChannel) {
			return false
		}
	}
	if versionConfig == "" {
		return false
	}
	versionSlice := strings.Split(versionConfig, ",")
	vLen := 2
	if len(versionSlice) != vLen {
		return false
	}
	minVersion := util.UVersion.Format(versionSlice[1])
	maxVersion := util.UVersion.Format(versionSlice[0])
	currentVersion := util.UVersion.Format(appClient.Version)
	if !currentVersion.Success || !maxVersion.Success || !minVersion.Success {
		return false
	}
	if currentVersion.Version >= minVersion.Version && currentVersion.Version <= maxVersion.Version {
		return true
	}
	return false
}

type SCFormatUtm struct {
	AnonymousID      string `json:"anonymous_id"`
	PlatformType     string `json:"platform_type"`
	ChannelClickTime int64  `json:"channel_click_time"`
	UtmSource        string `json:"utm_source"`
	UtmMedium        string `json:"utm_medium"`
	UtmTerm          string `json:"utm_term"`
	UtmContent       string `json:"utm_content"`
	UtmCampaign      string `json:"utm_campaign"`
	ChannelADID      string `json:"channel_ad_id"`
}

func GetChannel(appClient *library.AppClient, uid int64) int {
	chFun := func(utmSource string) int {
		for channel, value := range libc.ChannelMap {
			if strings.Replace(value, " ", "", 1) ==
				strings.Replace(utmSource, " ", "", 1) {
				return channel
			}
		}
		if v, ok := libc.ChannelClientMap[strconv.Itoa(appClient.Channel)]; ok {
			return v
		}
		return libc.UserChanneDef
	}
	if appClient == nil {
		logger.Error("非法调用传入数据为空", uid)
		return libc.UserChanneDef
	}
	t := &ChannelInfoByDevice{
		UID:       uid,
		AppDevice: appClient,
	}
	if uid == 0 {
		if appClient.DeviceID == "" {
			return libc.UserChanneDef
		}
		item := t.getChannelByDeviceIDV2()
		if item == nil {
			return libc.UserChanneDef
		}
		return chFun(item.UtmSource)
	}
	adUIDChannel := client.TbAdUIDChannel.GetItemByUID(uid)
	if adUIDChannel != nil && adUIDChannel.UtmSource != "" {
		return chFun(adUIDChannel.UtmSource)
	}
	if appClient.DeviceID == "" {
		return libc.UserChanneDef
	}
	item := t.getChannelByDeviceIDV2()
	if item == nil {
		return libc.UserChanneDef
	}
	safelygo.GoSafelyByTraceID(func() {
		saveAdUIDChannel(item, t.AppDevice, t.UID)
	})
	return chFun(item.UtmSource)
}

func saveAdUIDChannel(item *client.AdChannelSensorsData, appClient *library.AppClient, uid int64) {
	rd := cache.GetCRedis()
	lockKey := libcache.LockUIDADChannel + appClient.DeviceID
	var expireTime time.Duration = 1
	if lock := rd.Lock(lockKey, cache.LockExpiration(expireTime*time.Minute)); !lock {
		return
	}
	defer func() {
		if err := rd.Unlock(lockKey); err != nil {
			logger.Error(err)
		}
	}()
	adUIDChannel := client.TbAdUIDChannel.GetItemByUID(uid)
	if adUIDChannel == nil {
		auc := &client.AdUIDChannel{
			UID:        uid,
			DeviceID:   appClient.DeviceID,
			UtmSource:  item.UtmSource,
			IPSource:   item.IPSource,
			IPAreaCode: item.IPAreaCode,
			IPAdCode:   item.IPAdCode,
			Version:    appClient.Version,
		}
		if err := auc.Save(); err != nil {
			logger.Error("保存 数据出错", err.Error())
		}
		scQuDaoProfileSet(auc)
		return
	}
	if item.UtmSource != "" {
		adUIDChannel.UtmSource = item.UtmSource
	}
	adUIDChannel.IPSource = item.IPSource
	adUIDChannel.IPAreaCode = item.IPAreaCode
	adUIDChannel.IPAdCode = item.IPAdCode
	adUIDChannel.Version = appClient.Version
	adUIDChannel.DeviceID = appClient.DeviceID
	if err := adUIDChannel.Update(); err != nil {
		logger.Error("保存 数据出错", err.Error())
	}
	scQuDaoProfileSet(adUIDChannel)
}

func getChannelByRedis(appClient *library.AppClient, anonymousID string) *client.AdChannelSensorsData {
	if appClient.DeviceID == "" {
		return nil
	}
	item := client.TbAdSensorsData.GetItemByDeviceID(appClient.DeviceID)
	if item != nil {
		scFormatUtmBytes, err := cache.GetYoga01Redis().GetClient().Get(context.Background(),
			fmt.Sprintf("%s:%s", "AppInstall", anonymousID)).Result()
		if err == nil && scFormatUtmBytes != "" {
			var scFormatUtm SCFormatUtm
			if err := json.Unmarshal([]byte(scFormatUtmBytes), &scFormatUtm); err == nil {
				if item.UtmSource == "" {
					item.UtmSource = scFormatUtm.UtmSource
					item.AnonymousID = anonymousID
				} else if _, ok := libc.SelfUtmSourceMap[scFormatUtm.UtmSource]; ok {
					item.UtmSource = scFormatUtm.UtmSource
				}
			}
		}
		updateAdChannelSensorsData(item, appClient)
	}
	return item
}

// scQuDaoProfileSet 实时更新用户属性
// nolint
func scQuDaoProfileSet(item *client.AdUIDChannel) {
	quDao := SensorChannelNature
	if _, ok := libc.SelfUtmSourceMap[item.UtmSource]; ok {
		quDao = SensorChannelPage
	}
	if item.UtmSource == "【CS_小树苗运动】" {
		item.UtmSource = ""
	}
	prop := map[string]interface{}{
		"qudao":        quDao,
		"qudao_detail": item.UtmSource,
	}
	err := sensorsdata.ProfileSet(strconv.FormatInt(item.UID, 10), prop, true)
	if err != nil {
		logger.Errorf("用户属性注册渠道上报失败 %v", err)
	}
}

const (
	ChongQing = "重庆"
)

// updateAdChannelSensorsData 处理更新表数据
func updateAdChannelSensorsData(item *client.AdChannelSensorsData, appDevice *library.AppClient) {
	var isChannel bool
	if _, ok := libc.SelfUtmSourceMap[item.UtmSource]; ok {
		isChannel = true
	}
	ipSource := updateIPSource(isChannel, appDevice, item.IPAds)
	if ipSource != nil {
		item.IPAreaCode = ipSource.AreaCode
		item.IPAdCode = ipSource.AdCode
		item.IPSource = ipSource.City
		item.IPAds = appDevice.IPAds
	} else {
		ipSource = formatSaveIP(item.IPAds)
		item.IPAreaCode = ipSource.AreaCode
		item.IPAdCode = ipSource.AdCode
		item.IPSource = ipSource.City
	}
	if err := item.Update(); err != nil {
		logger.Error("ad_channel_sensorsdata 表更新失败", err)
	}
}

// IsChongqingIP 判断是不是重庆ip
func IsChongqingIP(ipAds string) bool {
	ipSource := formatSaveIP(ipAds)
	// 重庆特殊处理
	return strings.Contains(ipSource.City, ChongQing)
}

const (
	TwoDay = 48
)

// StatisticsEntranceCount  统计进入合规次数
func StatisticsEntranceCount(countType, count int64) {
	var lockKey string
	cfgEnv := config.Get().Service.Env
	if cfgEnv != microservice.Dev {
		lockKey += string(cfgEnv) + "_"
	}
	// 获取当前小时
	currentHour := time.Now().Format("2006-01-02-15")
	lockKey += libc.CountComplianceMap[countType]
	lockKey += currentHour
	// 存储
	bk := context.Background()
	rd := cache.GetYoga01Redis().GetClient()
	_, err := rd.IncrBy(bk, lockKey, count).Result()
	if err != nil {
		logger.Warn("更新合规次数key出错", err.Error())
	}
	timeKey, _ := rd.TTL(bk, lockKey).Result()
	if int(timeKey) == -1 {
		_, _ = rd.Expire(bk, lockKey, time.Hour*TwoDay).Result()
	}
}

func formatSaveIP(ip string) *IPSourceV2 {
	ipSourceList := getIPSourceV2(ip)
	city := make([]string, 0)
	adCode := make([]string, 0)
	areaCode := make([]string, 0)
	for s := range ipSourceList {
		ipSource := ipSourceList[s]
		city = append(city, ipSource.City)
		adCode = append(adCode, ipSource.AdCode)
		areaCode = append(areaCode, ipSource.AreaCode)
	}
	return &IPSourceV2{
		City:     strings.Join(city, ","),
		AdCode:   strings.Join(adCode, ","),
		AreaCode: strings.Join(areaCode, ","),
	}
}

// 更新操作 orgIPSource 原始IP是否后台配置 没配置 使用device内的ip更新
func updateIPSource(isChannel bool, device *library.AppClient, orgIPSource string) *IPSourceV2 {
	selectCityList, _ := getSelectCityList(device.OsType, isChannel, nil)
	orgIPSourceList := getIPSourceV2(orgIPSource)
	// 取出来特殊赋值 选中的中国城市
	chinaCityAdCode := getSelectChinaCityAdCode(device.OsType, isChannel)
	// 原始IPAds 如果已经命中 选择的城市 不更新
	if orgIPSource != "" {
		// selectCity.AdCode == 999999 后台选中 中国其他城市
		// AreaCode=cn IP库里面中国的IP 有些是没有AdCode的 所以加strings.EqualFold(orgIPSource.AreaCode, "cn")
		for k := range orgIPSourceList {
			oIPSource := orgIPSourceList[k]
			for s := range selectCityList {
				selectCity := selectCityList[s]
				if strings.EqualFold(oIPSource.AreaCode, "cn") {
					if strconv.Itoa(selectCity.AdCode) == oIPSource.AdCode ||
						strings.Contains(oIPSource.City, selectCity.City) {
						return nil
					} else if selectCity.AdCode == 999999 &&
						!library.StringInArray(oIPSource.AdCode, chinaCityAdCode) {
						return nil
					}
				} else if selectCity.AdCode == -1 && !strings.EqualFold(oIPSource.AreaCode, "cn") {
					return nil
				}
			}
		}
	}
	if device.IPAds == "" {
		return nil
	}
	return formatSaveIP(device.IPAds)
}

func getSelectChinaCityAdCode(osType int, isChannel bool) []string {
	selectChinaCityAdCode := make([]string, 0)
	table := client.TbConfig.GetItemByKey(libc.ComplianceConfig)
	var conf ComplianceConfig
	if table != nil {
		err := json.Unmarshal([]byte(table.Value), &conf)
		if err != nil {
			return selectChinaCityAdCode
		}
	}
	deviceData := conf.Android
	if osType == int(libc.DeviceTypeEnum.IOS) {
		deviceData = conf.IOS
	}
	cityList := deviceData.Natural
	if isChannel {
		cityList = deviceData.Channel
	}
	for c := range cityList {
		selectCity := cityList[c]
		if selectCity.AdCode != 999999 && selectCity.AdCode != -1 {
			selectChinaCityAdCode = append(selectChinaCityAdCode, strconv.Itoa(selectCity.AdCode))
		}
	}
	return selectChinaCityAdCode
}

func UploadProcessData(appClient *library.AppClient, anonymousID string) {
	rd := cache.GetCRedis()
	lockKey := fmt.Sprintf("D:Upload:DeviceID:Anonymous:%s", anonymousID)
	if !rd.Lock(lockKey) {
		return
	}
	defer func() {
		if err := rd.Unlock(lockKey); err != nil {
			logger.Error("UploadProcessData redis解锁失败", lockKey, err)
		}
	}()
	item := client.TbAdSensorsData.GetItemByDeviceID(appClient.DeviceID)
	if item == nil {
		channelSensors := &client.AdChannelSensorsData{
			DeviceID:    appClient.DeviceID,
			OSType:      appClient.OsType,
			AnonymousID: anonymousID,
			IPAds:       appClient.IPAds,
		}
		if err := channelSensors.Save(); err != nil {
			logger.Error("ad_channel_sensorsdata 表写入失败", err)
			return
		}
		item = channelSensors
	}
	// water 修改为不是渠道都去更新
	if _, ok := libc.SelfUtmSourceMap[item.UtmSource]; !ok {
		writeUtmSource(item)
	}
	updateAdChannelSensorsData(item, appClient)
}

func writeUtmSource(item *client.AdChannelSensorsData) {
	utmData, err := cache.GetYoga01Redis().GetClient().
		Get(context.Background(), fmt.Sprintf("AppInstall:%s", item.AnonymousID)).Result()
	if err == nil && utmData != "" {
		var utStruct SCFormatUtm
		if err := json.Unmarshal([]byte(utmData), &utStruct); err == nil {
			item.UtmSource = utStruct.UtmSource
		}
	}
}

func SetSensorUtmSource(anonymousID string, userChannel int) {
	var scFormatUtm SCFormatUtm
	scFormatUtm.UtmSource = libc.ChannelMap[userChannel]
	jsonData, err := json.Marshal(scFormatUtm)
	if err != nil {
		return
	}
	ctx := context.Background()
	anonymousIDKey := fmt.Sprintf("%s:%s", "AppInstall", anonymousID)
	hr, _ := time.ParseDuration("1h")
	cache.GetYoga01Redis().GetClient().SetEX(ctx, anonymousIDKey, string(jsonData), hr)
}

type ConfigIdentityIP struct {
	UserIPSwitch bool       `json:"user_ip_switch"`
	WhiteIP      string     `json:"white_ip"`
	CityList     []CityItem `json:"city_list"`
}

type CityItem struct {
	Label    string `json:"label"`
	Value    int    `json:"value"`
	Selected int    `json:"selected"`
}

const Success int32 = 1001 // 成功

// yoga-ip-geo 获取Geo
func getUserSourceIP(ip string) map[string]*gpb.IPSvFourGeoItem {
	if ip == "" {
		return nil
	}
	ips := strings.Split(ip, ",")
	rpc, err := grpc.GetIPGeoClient().IPSvFourGeo(context.Background(), &gpb.IPSvFourGeoRequest{
		IPS: ips,
	})
	if err != nil || rpc.ResultCode != Success {
		return nil
	}
	return rpc.GetConfig()
}

type JuLiangSwitch struct {
	Android bool `json:"android"`
	Ios     bool `json:"ios"`
}

func GetJuLiangSwitch(appClient *library.AppClient) JuLiangSwitch {
	return JuLiangSwitch{
		Android: true,
		Ios:     true,
	}
}

// GetCompliance 合规开关
func GetCompliance(uid int64, appDevice *library.AppClient) bool {
	var compliance bool
	defer func() {
		scComplianceEvent(uid, appDevice, compliance)
	}()
	// 版本审核判断
	if GetAuditInfo(appDevice).IsInAudit {
		compliance = true
		return compliance
	}
	t := &ChannelInfoByDevice{
		UID:       uid,
		AppDevice: appDevice,
	}
	if t.UID == 0 {
		compliance = ipCompliance(appDevice, t, false)
		return compliance
	}
	adUIDChannel := client.TbAdUIDChannel.GetItemByUID(t.UID)
	if adUIDChannel != nil {
		item := t.getChannelByDeviceIDV2()
		if item != nil {
			adUIDChannel.IPAreaCode = item.IPAreaCode
			adUIDChannel.IPAdCode = item.IPAdCode
			adUIDChannel.IPSource = item.IPSource
			adUIDChannel.Version = appDevice.Version
			if _, ok := libc.SelfUtmSourceMap[item.UtmSource]; ok {
				adUIDChannel.UtmSource = item.UtmSource
			}
			if err := adUIDChannel.Update(); err != nil {
				logger.Error("更新 数据出错", err.Error())
			}
			safelygo.GoSafelyByTraceID(func() {
				scQuDaoProfileSet(adUIDChannel)
			})
		}
		formatIPArr := formatIPSourceV2(adUIDChannel.IPAreaCode, adUIDChannel.IPAdCode, adUIDChannel.IPSource)
		compliance = isIPComplianceForDB(formatIPArr, adUIDChannel.UtmSource, appDevice)
		return compliance
	}
	compliance = ipCompliance(appDevice, t, true)
	return compliance
}

// 神策上报数据
func scComplianceEvent(uid int64, appDevice *library.AppClient, compliance bool) {
	adUIDChannel := client.TbAdUIDChannel.GetItemByUID(uid)
	if adUIDChannel == nil {
		return
	}
	quDao := SensorChannelNature
	if _, ok := libc.SelfUtmSourceMap[adUIDChannel.UtmSource]; ok {
		quDao = SensorChannelPage
	}
	if adUIDChannel.UtmSource == "【CS_小树苗运动】" {
		adUIDChannel.UtmSource = ""
	}
	s := &sensor.ScComplianceEvent{
		CommData:     sensor.InitCommDataByClient(appDevice),
		QuDao:        quDao,
		QuDaoDetail:  adUIDChannel.UtmSource,
		City1:        adUIDChannel.IPSource,
		IsCompliance: compliance,
	}
	s.Track(strconv.FormatInt(uid, 10))
}

type ChannelInfoByDevice struct {
	UID       int64
	AppDevice *library.AppClient
}

// IP ipCompliance 是否合规
func ipCompliance(appDevice *library.AppClient, t *ChannelInfoByDevice, isSave bool) bool {
	if appDevice.DeviceID == "" {
		return ipNaturalCompliance(appDevice)
	}
	item := t.getChannelByDeviceIDV2()
	if item == nil {
		return ipNaturalCompliance(appDevice)
	}
	if isSave {
		safelygo.GoSafelyByTraceID(func() {
			saveAdUIDChannel(item, appDevice, t.UID)
		})
	}
	return isCompliance(item, appDevice)
}

// isCompliance 判断是是否合规
func isCompliance(item *client.AdChannelSensorsData, appDevice *library.AppClient) bool {
	formatIPArr := formatIPSourceV2(item.IPAreaCode, item.IPAdCode, item.IPSource)
	return isIPComplianceForDB(formatIPArr, item.UtmSource, appDevice)
}

func formatIPSourceV2(areaCode, adCode, city string) []IPSourceV2 {
	resp := make([]IPSourceV2, 0)
	areaCodeList := strings.Split(areaCode, ",")
	adCodeList := strings.Split(adCode, ",")
	cityList := strings.Split(city, ",")
	for k := range areaCodeList {
		resp = append(resp, IPSourceV2{
			AreaCode: areaCodeList[k],
			AdCode:   adCodeList[k],
			City:     cityList[k],
		})
	}
	return resp
}

// getChannelByDeviceIDV2 附带更新功能
func (t *ChannelInfoByDevice) getChannelByDeviceIDV2() *client.AdChannelSensorsData {
	itemByDeviceID := client.TbAdSensorsData.GetItemByDeviceID(t.AppDevice.DeviceID)
	if itemByDeviceID == nil {
		return nil
	}
	item := getChannelByRedis(t.AppDevice, itemByDeviceID.AnonymousID)
	if item == nil {
		return nil
	}
	if item.UtmSource == "" && t.AppDevice.OsType == int(libc.DeviceTypeEnum.IOS) {
		df := libc.AnonymousIDPrefix
		if strings.Contains(itemByDeviceID.AnonymousID, df) {
			itemByDeviceID.AnonymousID = itemByDeviceID.AnonymousID[len(df):]
		} else {
			itemByDeviceID.AnonymousID = df + itemByDeviceID.AnonymousID
		}
		item = getChannelByRedis(t.AppDevice, itemByDeviceID.AnonymousID)
	}
	return item
}

// isIPComplianceForDB true-合规
// nolint
func isIPComplianceForDB(ipSourceList []IPSourceV2, utmSource string, device *library.AppClient) bool {
	var isChannel bool
	if _, ok := libc.SelfUtmSourceMap[utmSource]; ok {
		isChannel = true
	}
	conf := getComplianceConfig()
	// 渠道应用商店合规检查
	if isShopComplianceByChannel(device, utmSource, conf) {
		return true
	}
	selectCityList, whiteIP := getSelectCityList(device.OsType, isChannel, conf)
	ips := strings.Split(device.IPAds, ",")
	// 白名单逻辑
	if whiteIP != "" {
		whiteIPArr := strings.Split(whiteIP, ",")
		for _, uIP := range ips {
			if library.StringInArray(uIP, whiteIPArr) {
				return false
			}
		}
	}
	if len(selectCityList) < 1 {
		return false
	}
	if len(ipSourceList) < 1 {
		return true
	}
	// 取出来特殊赋值 选中的中国城市
	chinaCityAdCode := getSelectChinaCityAdCode(device.OsType, isChannel)
	for k := range ipSourceList {
		orgIPSource := ipSourceList[k]
		for s := range selectCityList {
			selectCity := selectCityList[s]
			if strings.EqualFold(orgIPSource.AreaCode, "cn") {
				if strconv.Itoa(selectCity.AdCode) == orgIPSource.AdCode ||
					strings.Contains(orgIPSource.City, selectCity.City) {
					return true
				} else if selectCity.AdCode == 999999 && !library.StringInArray(orgIPSource.AdCode, chinaCityAdCode) {
					return true
				}
			} else if selectCity.AdCode == -1 && !strings.EqualFold(orgIPSource.AreaCode, "cn") {
				return true
			}
		}
	}
	return false
}

// ipNaturalCompliance 判断IP是否合规  true-合规
func ipNaturalCompliance(device *library.AppClient) bool {
	conf := getComplianceConfig()
	// 渠道应用商店合规检查
	if isShopComplianceByChannel(device, "", conf) {
		return true
	}
	selectCityList, whiteIP := getSelectCityList(device.OsType, false, conf)
	ips := strings.Split(device.IPAds, ",")
	// 白名单逻辑
	if whiteIP != "" {
		whiteIPArr := strings.Split(whiteIP, ",")
		for _, uIP := range ips {
			if library.StringInArray(uIP, whiteIPArr) {
				return false
			}
		}
	}
	if len(selectCityList) < 1 {
		return false
	}
	ipSourceList := getIPSourceV2(device.IPAds)
	// 取出来特殊赋值 选中的中国城市
	chinaCityAdCode := getSelectChinaCityAdCode(device.OsType, false)
	for k := range ipSourceList {
		orgIPSource := ipSourceList[k]
		for s := range selectCityList {
			selectCity := selectCityList[s]
			if strings.EqualFold(orgIPSource.AreaCode, "cn") {
				if strconv.Itoa(selectCity.AdCode) == orgIPSource.AdCode ||
					strings.Contains(orgIPSource.City, selectCity.City) {
					return true
				} else if selectCity.AdCode == 999999 && !library.StringInArray(orgIPSource.AdCode, chinaCityAdCode) {
					return true
				}
			} else if selectCity.AdCode == -1 && !strings.EqualFold(orgIPSource.AreaCode, "cn") {
				return true
			}
		}
	}
	return false
}

type IPCityItem struct {
	City     string           `json:"label"`    // 城市名称
	AreaCode string           `json:"areacode"` // 国家code
	AdCode   int              `json:"value"`    // cityCode
	Selected int              `json:"selected"`
	Invalid  *InvalidDuration `json:"invalid,omitempty"` // 不合规时间段
}

type InvalidDuration struct {
	IsContainHoliday bool   `json:"is_contain_holiday"`
	StartDate        int64  `json:"start_date"`
	EndDate          int64  `json:"end_date"`
	StartHour        string `json:"start_hour"`
	EndHour          string `json:"end_hour"`
}

// ComplianceConfig 后台配置数据
type ComplianceConfig struct {
	Android ComplianceItem `json:"1"`
	IOS     ComplianceItem `json:"2"`
	// 渠道配置扩展（最小侵入方式）
	Huawei     ComplianceItem `json:"1600001,omitempty"` // 华为渠道配置
	OPPO       ComplianceItem `json:"1600002,omitempty"` // OPPO渠道配置
	VIVO       ComplianceItem `json:"1600003,omitempty"` // VIVO渠道配置
	Xiaomi     ComplianceItem `json:"1600004,omitempty"` // 小米渠道配置
	OtherSmall ComplianceItem `json:"-1,omitempty"`      // 其他小渠道配置
}
type ComplianceItem struct {
	Natural []IPCityItem     `json:"natural"`
	Channel []IPCityItem     `json:"channel"`
	WhiteIP string           `json:"white_ip"`
	Switch  []OnlineConfItem `json:"switch,omitempty"` // 开关配置，可选
}

// 获取配置的城市
func getSelectCityList(osType int, isChannel bool, conf *ComplianceConfig) (selectCity []IPCityItem, ipWhiteIP string) {
	selectCity = make([]IPCityItem, 0)
	if conf == nil {
		table := client.TbConfig.GetItemByKey(libc.ComplianceConfig)
		var tempConf ComplianceConfig
		if table != nil {
			err := json.Unmarshal([]byte(table.Value), &tempConf)
			if err != nil {
				return selectCity, ""
			}
		}
		conf = &tempConf
	}

	deviceData := conf.Android
	if osType == int(libc.DeviceTypeEnum.IOS) {
		deviceData = conf.IOS
	}
	cityList := deviceData.Natural
	if isChannel {
		cityList = deviceData.Channel
	}
	for c := range cityList {
		if cityList[c].Selected == library.Yes {
			// 判断是否在合规生效的时间段
			if isComplianceValid(cityList[c].Invalid) {
				selectCity = append(selectCity, cityList[c])
			}
		}
	}
	return selectCity, deviceData.WhiteIP
}

// isComplianceValid 后台会配置不合规时间段，需要检查当前是否是合规时间
func isComplianceValid(invalidDuration *InvalidDuration) bool {
	if invalidDuration == nil {
		return true
	}
	// 特殊假期开关打开时，判断是否节假日，如果是节假日，则属于不合规时间段
	if invalidDuration.IsContainHoliday && todayIsHoliday() {
		return false
	}
	if invalidDuration.StartDate == 0 || invalidDuration.EndDate == 0 ||
		invalidDuration.StartHour == "" || invalidDuration.EndHour == "" {
		return true
	}
	nowTime := time.Now()
	now := nowTime.Unix()
	if now < util.FormatStartTime(invalidDuration.StartDate) ||
		now > util.FormatEndTime(invalidDuration.EndDate) {
		return true
	}
	todayStart := timeHourFunc(invalidDuration.StartHour, nowTime)
	todayEnd := timeHourFunc(invalidDuration.EndHour, nowTime)
	if todayStart == 0 || todayEnd == 0 {
		return true
	}
	return checkInvalidHourTime(invalidDuration, todayStart, todayEnd, now)
}

const (
	DateTypeHoliday        = 1
	DateTypeInvalidWeekEnd = 2
)

var allHolidayList []*client.YearHoliday

func todayIsHoliday() bool {
	holidayList := GetYearHoliday()
	if len(holidayList) == 0 {
		logger.Error("不合规时间段未配置特殊节假日")
		return false
	}
	now := time.Now()
	todayStr := now.Format("20060102")
	isWeekEnd := false
	if now.Weekday() == time.Saturday || now.Weekday() == time.Sunday {
		isWeekEnd = true
	}
	for _, v := range holidayList {
		if v.DateType == DateTypeHoliday && todayStr == v.DateIndex {
			return true
		}
		if v.DateType == DateTypeInvalidWeekEnd && todayStr == v.DateIndex {
			return false
		}
	}
	return isWeekEnd
}

// GetYearHoliday 获取年份节假日
// nolint
func GetYearHoliday() []*client.YearHoliday {
	if len(allHolidayList) > 0 {
		return allHolidayList
	}
	vStr := cache.GetCRedis().Load(libcache.YearHolidayList + time.Now().Format("2006"))
	cList := make([]*client.YearHoliday, 0)
	if vStr != "" {
		err := json.Unmarshal([]byte(vStr), &cList)
		if err != nil {
			logger.Warn(err)
		}
		allHolidayList = cList
		return cList
	}
	list := client.TbYearHoliday.GetListByYear(time.Now().Format("2006"))
	if len(list) != 0 {
		vByte, err := json.Marshal(list)
		if err == nil {
			var ex time.Duration = time.Hour * 24 * 366
			cache.GetCRedis().Storage(libcache.YearHolidayList, string(vByte), ex)
		}
	}
	allHolidayList = list
	return list
}

// 检查是否在小时区间段内
func checkInvalidHourTime(invalidDuration *InvalidDuration, todayStart, todayEnd, now int64) bool {
	if todayStart < todayEnd {
		// 例如15:00到16:00
		if now >= todayStart && now <= todayEnd {
			return false
		}
		return true
	}
	// 例如 23:00到08:00
	tEnd := util.FormatEndTime(now)
	// 不是最后一天或者开始日期和结束日期相等 只要当前时间在今天23:00到23:59 即不合规时间段
	if (util.FormatStartTime(now) != util.FormatStartTime(invalidDuration.EndDate) ||
		util.FormatStartTime(invalidDuration.StartDate) == util.FormatStartTime(invalidDuration.EndDate)) &&
		now >= todayStart && now <= tEnd {
		return false
	}
	tStart := util.FormatStartTime(now)
	// 不是第一天 只要当前时间在今天00:000到08:00 即不合规时间段
	if util.FormatStartTime(now) != util.FormatStartTime(invalidDuration.StartDate) && now >= tStart && now <= todayEnd {
		return false
	}
	return true
}

// 格式化小时时间点为当天时间点
func timeHourFunc(input string, now time.Time) int64 {
	loc, err := time.LoadLocation(library.TimeZoneBeijing)
	if err != nil {
		return 0
	}
	// 解析时间字符串
	parsedTime, err := time.ParseInLocation("15:04", input, loc)
	if err != nil {
		return 0
	}
	// 获取当天的日期
	currentDate := now.Format("2006-01-02")
	// 将解析后的时间与当天的日期组合
	fullTime, err := time.ParseInLocation("2006-01-02 15:04", fmt.Sprintf("%s %s",
		currentDate, parsedTime.Format("15:04")), loc)
	if err != nil {
		return 0
	}
	return fullTime.Unix()
}

type IPSourceV2 struct {
	City     string // 城市名称
	AdCode   string // 城市code
	AreaCode string // 国家code
}

const (
	AreaCodeUnknown = "yg_unknown"
	CityUnknown     = "未知"
	XIAN            = "西安市"
)

func getIPSourceV2(ipAds string) []*IPSourceV2 {
	resp := make([]*IPSourceV2, 0)
	ipResp := getUserSourceIP(ipAds)
	if ipResp == nil {
		logger.Warn("ip 未查询到相应的数据", ipAds)
		return resp
	}
	ips := strings.Split(ipAds, ",")
	// 999999-中国其他城市 -1 海外
	for _, ip := range ips {
		temp := &IPSourceV2{}
		if i, ok := ipResp[ip]; !ok {
			temp.AdCode = "-1"
			temp.City = CityUnknown
			temp.AreaCode = AreaCodeUnknown // yoga-ip-geo 枚举一致
		} else {
			temp.AreaCode = i.AreaCode
			temp.City = i.City
			temp.AdCode = i.AdCode
			if i.AreaCode == "" {
				temp.AdCode = "-1"
				temp.City = CityUnknown
				temp.AreaCode = AreaCodeUnknown // yoga-ip-geo 枚举一致
			} else if i.City == "" && i.Province != "" && strings.HasPrefix(i.Province, "陕西") {
				temp.City = XIAN
				temp.AdCode = "610100"
			}
		}
		resp = append(resp, temp)
	}
	return resp
}

func getComplianceConfig() *ComplianceConfig {
	table := client.TbConfig.GetItemByKey(libc.ComplianceConfig)
	var conf ComplianceConfig
	if table != nil {
		err := json.Unmarshal([]byte(table.Value), &conf)
		if err != nil {
			return nil
		}
	}
	return &conf
}

// getComplianceConfigChannel 获取合规配置
func getComplianceConfigChannel(device *library.AppClient, conf *ComplianceConfig) *ComplianceItem {
	// 只对安卓有用，排除iOS设备
	if device.OsType == int(libc.DeviceTypeEnum.IOS) {
		return nil
	}
	if conf == nil {
		return nil
	}
	channelStr := strconv.Itoa(device.Channel)

	// 根据渠道获取对应的配置
	switch channelStr {
	case libc.ChannelHuawei:
		return &conf.Huawei
	case libc.ChannelOPPO:
		return &conf.OPPO
	case libc.ChannelVIVO:
		return &conf.VIVO
	case libc.ChannelXiaomi:
		return &conf.Xiaomi
	default:
		// 其他渠道都取其他小渠道配置
		return &conf.OtherSmall
	}
}

// isShopComplianceByChannel 应用商店合规检查
func isShopComplianceByChannel(device *library.AppClient, utmSource string, conf *ComplianceConfig) bool {
	confChannel := getComplianceConfigChannel(device, conf)
	if confChannel == nil {
		return false
	}
	var configList []IPCityItem
	// 1. 判断utmSource类型
	if utmSource == "" || utmSource == libc.UserChannelNaturalTest {
		// 自然量，使用confChannel.Natural
		configList = confChannel.Natural
	} else {
		channelStr := strconv.Itoa(device.Channel)
		// 检查是否是对应渠道的应用商店付费渠道
		expectedAppStoreUtmSource, exists := libc.ChannelToAppStoreUtmSourceMap[channelStr]
		if exists && utmSource == expectedAppStoreUtmSource {
			// 是对应渠道的应用商店付费渠道，使用confChannel.Channel
			configList = confChannel.Channel
		} else {
			// 不属于需要判断是否合规的utmSource
			return false
		}
	}
	// 2. 判断是否选中，如果选中则合规
	for _, item := range configList {
		if item.Selected == library.Yes {
			// 判断是否在合规生效的时间段
			if isComplianceValid(item.Invalid) {
				return true
			}
		}
	}
	return false
}
