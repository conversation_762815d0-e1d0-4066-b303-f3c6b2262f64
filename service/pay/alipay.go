package pay

import (
	"context"
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libpay "gitlab.dailyyoga.com.cn/server/children/library/pay"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
	"gitlab.dailyyoga.com.cn/server/gopay"
	ali "gitlab.dailyyoga.com.cn/server/gopay/alipay"
	putil "gitlab.dailyyoga.com.cn/server/gopay/pkg/util"
	"gitlab.dailyyoga.com.cn/server/gopay/pkg/xpem"
	"gitlab.dailyyoga.com.cn/server/gopay/pkg/xrsa"
)

type alipay struct{}

var SrvAlipay alipay

// nolint
type PayOrderInfo struct {
	// 支付时需要的订单内容字段
	OrderID string
	Amount  float64
	// 支付时需要的描述内容字段
	Body       string
	Subject    string
	MchID      string
	AlipaySign *AlipaySignParams
	UID        int64
}
type AlipaySignParams struct {
	ContractCode string
	PeriodType   string
	Period       int
	Price        string
	ExecuteTime  string
	ServiceDes   string
	OrgPrice     string
}
type AlipayPreOrder struct {
	PaymentType string `json:"payment_type"`
	OutTradeNo  string `json:"out_trade_no"`
	PayInfo     string `json:"pay_info"`
	Subject     string `json:"subject"`
	NotifyURL   string `json:"notify_url"`
	Body        string `json:"body"`
	Partner     int64  `json:"partner,omitempty"`
}

// 初始化支付宝支付client
// nolint
func InitAlipayClient(appID string) *ali.Client {
	conf := config.Get().GetAlipayKeyByAppID(appID)
	if conf == nil {
		logger.Error("未配置支付宝当前使用的商户号", appID)
		return nil
	}
	client, err := ali.NewClient(conf.AppID, conf.PrivateKey, true)
	if err != nil {
		logger.Error(err.Error())
		return nil
	}
	// client.DebugSwitch = gopay.DebugOff
	// client.SetCharset("utf-8").SetSignType(ali.RSA2)
	// publicKeyByte, err := tools.ReadFile(conf.AliPayPublicCertPath)
	// if err != nil || len(publicKeyByte) == 0 {
	// 	logger.Error("读取支付宝公钥失败", err)
	// 	return nil
	// }
	// client.AutoVerifySign(publicKeyByte)
	// err = client.SetCertSnByPath(conf.AppCertPath, conf.AliPayRootCertPath, conf.AliPayPublicCertPath)
	// if err != nil {
	// 	logger.Error(err.Error())
	// 	return nil
	// }
	return client
}

func (*alipay) BuildPreOrder(order *PayOrderInfo) (*AlipayPreOrder, error) {
	bm := gopay.BodyMap{}
	client := InitAlipayClient(order.MchID)
	if client == nil {
		return nil, errors.New("初始化支付宝client失败")
	}
	alipayRes := &AlipayPreOrder{
		PaymentType: util.ToString(libpay.PayTypeAlipay), // yoga为1，分析1为微信 2为支付宝
		OutTradeNo:  order.OrderID,
		Subject:     order.Subject,
		NotifyURL:   "https://" + config.Get().Service.Domain + "/children/callback/alipay/tradeapppay",
		Body:        order.Body,
	}
	// 配置公共参数
	client.SetCharset(ali.UTF8).
		SetSignType(ali.RSA2).
		SetNotifyUrl(alipayRes.NotifyURL)
	bm.Set("subject", alipayRes.Subject).
		Set("out_trade_no", alipayRes.OutTradeNo).
		Set("total_amount", strconv.FormatFloat(order.Amount, 'f', 2, 64)).
		Set("product_code", "QUICK_WAP_WAY")

	payParam, err := client.TradeAppPay(context.Background(), bm)
	if err != nil {
		if bizErr, ok := ali.IsBizError(err); ok {
			logger.Errorf("支付宝下单失败：%+v err:%v", bizErr, err)
		}
		return nil, err
	}
	alipayRes.PayInfo = payParam
	return alipayRes, nil
}

func (*alipay) BuildH5PreOrder(order *PayOrderInfo) (*AlipayPreOrder, error) {
	bm := gopay.BodyMap{}
	client := InitAlipayClient(order.MchID)
	if client == nil {
		return nil, errors.New("初始化支付宝client失败")
	}
	// 配置公共参数
	client.SetCharset(ali.UTF8).
		SetSignType(ali.RSA2).
		SetNotifyUrl("https://" + config.Get().Service.Domain + "/children/callback/alipay/tradewappay")
	bm.Set("subject", order.Subject).
		Set("out_trade_no", order.OrderID).
		Set("total_amount", strconv.FormatFloat(order.Amount, 'f', 2, 64)).
		Set("product_code", "QUICK_WAP_WAY")

	payParam, err := client.TradeWapPay(context.Background(), bm)
	if err != nil {
		if bizErr, ok := ali.IsBizError(err); ok {
			logger.Errorf("支付宝下单失败：%+v err:%v", bizErr, err)
		}
		return nil, err
	}
	alipayRes := &AlipayPreOrder{}
	alipayRes.PayInfo = payParam
	return alipayRes, nil
}

// AggrementPaySign 支付中签约模式
func (*alipay) AggrementPaySign(order *PayOrderInfo) (*AlipayPreOrder, error) {
	bm := gopay.BodyMap{}
	client := InitAlipayClient(order.MchID)
	if client == nil {
		return nil, errors.New("初始化支付宝client失败")
	}
	alipayRes := &AlipayPreOrder{
		OutTradeNo: order.OrderID,
	}
	// 配置公共参数
	client.SetCharset(ali.UTF8).
		SetSignType(ali.RSA2).
		SetNotifyUrl("https://" + config.Get().Service.Domain + "/children/callback/alipay/agreementapppay")
	bizContent := map[string]interface{}{
		"personal_product_code": "CYCLE_PAY_AUTH_P",
		"sign_scene":            "INDUSTRY|APPSTORE",
		"external_agreement_no": order.AlipaySign.ContractCode,
		"external_logon_id":     order.OrderID,
		"access_params":         map[string]string{"channel": "ALIPAYAPP"},
		"period_rule_params": map[string]interface{}{
			"period_type":   order.AlipaySign.PeriodType,
			"period":        order.AlipaySign.Period,
			"execute_time":  order.AlipaySign.ExecuteTime,
			"single_amount": order.AlipaySign.OrgPrice,
		},
		"sign_notify_url": "https://" + config.Get().Service.Domain + "/children/callback/alipay/aggrementpay",
	}
	// 新账号不支持自定文案
	if !library.StringInArray(order.MchID, libpay.AliCustomCopyNotSupported) {
		bizContent["sub_merchant"] = map[string]string{
			"sub_merchant_name":                "小树苗运动",
			"sub_merchant_service_name":        "小树苗运动自动续费",
			"sub_merchant_service_description": order.AlipaySign.ServiceDes,
		}
	}
	bm.Set("out_trade_no", order.OrderID).
		Set("total_amount", order.Amount).
		Set("subject", order.Subject).
		Set("product_code", "CYCLE_PAY_AUTH").
		Set("timeout_express", "30m").
		Set("agreement_sign_params", bizContent)

	payParam, err := client.TradeAppPay(context.Background(), bm)
	if err != nil {
		logger.Error("支付宝支付中签约预下单失败", err)
		return nil, err
	}
	alipayRes.PayInfo = payParam
	return alipayRes, nil
}

// UserAgreementPageSign 先签约后扣款模式
func (*alipay) UserAgreementPageSign(order *PayOrderInfo) (*AlipayPreOrder, error) {
	logger.Infof("UserAgreementPageSign %+v", order)
	bm := gopay.BodyMap{}
	conf := config.Get().GetAlipayKeyByAppID(order.MchID)
	if conf == nil {
		return nil, errors.New("加载支付宝支付配置失败")
	}
	client := InitAlipayClient(order.MchID)
	if client == nil {
		return nil, errors.New("初始化支付宝client失败")
	}
	alipayRes := &AlipayPreOrder{
		PaymentType: util.ToString(libpay.PayTypeAlipay), // yoga为1，分析1为微信 2为支付宝
		OutTradeNo:  order.OrderID,
		Subject:     order.Subject,
		NotifyURL:   "https://" + config.Get().Service.Domain + "/children/callback/alipay/aggrement",
		Body:        order.Body,
	}
	// 配置公共参数
	client.SetCharset(ali.UTF8).
		SetSignType(ali.RSA2).
		SetNotifyUrl(alipayRes.NotifyURL).
		SetReturnUrl("children://com.yinniu/home")
	bm.Set("personal_product_code", "CYCLE_PAY_AUTH_P").
		Set("product_code", "CYCLE_PAY_AUTH").
		Set("external_agreement_no", order.AlipaySign.ContractCode).
		Set("sign_scene", "INDUSTRY|APPSTORE").
		Set("access_params", map[string]string{"channel": "ALIPAYAPP"}).
		Set("period_rule_params", map[string]interface{}{
			"period_type":   order.AlipaySign.PeriodType,
			"period":        order.AlipaySign.Period,
			"execute_time":  order.AlipaySign.ExecuteTime,
			"single_amount": order.AlipaySign.OrgPrice,
		})
	// 新账号不支持自定文案
	if !library.StringInArray(order.MchID, libpay.AliCustomCopyNotSupported) {
		bm.Set("sub_merchant", map[string]string{
			"sub_merchant_name":                "小树苗运动",
			"sub_merchant_service_name":        "小树苗运动自动续费",
			"sub_merchant_service_description": order.AlipaySign.ServiceDes,
		})
	}
	// 支付宝个人页面签约接口不用直接调，只需要返回给客户端链接，gopay包里面没有相应方法，只能在外面构造了
	var (
		bodyBs []byte
	)
	bodyBs, err := json.Marshal(bm)
	if err != nil {
		return nil, fmt.Errorf("json.Marshal：%w", err)
	}
	pubBody := pubParamsHandle(client, "alipay.user.agreement.page.sign", string(bodyBs))
	key := xrsa.FormatAlipayPrivateKey(conf.PrivateKey)
	priKey, err := xpem.DecodePrivateKey([]byte(key))
	if err != nil {
		return nil, err
	}
	// 计算sign
	sign, err := signWithPKCS1v15(pubBody, priKey, crypto.SHA256)
	if err != nil {
		return nil, err
	}
	pubBody.Add("sign", sign)
	alipayRes.PayInfo = "alipays://platformapi/startapp?appId=60000157" +
		"&appClearTop=false&startMultApp=YES&sign_params=" + url.QueryEscape(pubBody.Encode())
	return alipayRes, nil
}

// 公共参数处理
func pubParamsHandle(a *ali.Client, method, bizContent string) url.Values {
	pubBody := make(url.Values)
	pubBody.Add("app_id", a.AppId)
	pubBody.Add("method", method)
	pubBody.Add("format", "json")
	pubBody.Add("charset", a.Charset)
	pubBody.Add("sign_type", a.SignType)
	pubBody.Add("version", "1.0")
	pubBody.Add("timestamp", time.Now().Format(putil.TimeLayout))
	if bizContent != "" {
		pubBody.Add("biz_content", bizContent)
	}
	if a.NotifyUrl != "" {
		pubBody.Add("notify_url", a.NotifyUrl)
	}
	if a.ReturnUrl != "" {
		pubBody.Add("return_url", a.ReturnUrl)
	}
	return pubBody
}

func signWithPKCS1v15(param url.Values, privateKey *rsa.PrivateKey, hashType crypto.Hash) (s string, err error) {
	if param == nil {
		param = make(url.Values)
	}
	var pList = make([]string, 0)
	for key := range param {
		var value = strings.TrimSpace(param.Get(key))
		if len(value) > 0 {
			pList = append(pList, key+"="+value)
		}
	}
	sort.Strings(pList)
	var src = strings.Join(pList, "&")
	h := sha256.New()
	if _, err = h.Write([]byte(src)); err != nil {
		return
	}
	sig, err := rsa.SignPKCS1v15(rand.Reader, privateKey, hashType, h.Sum(nil))
	if err != nil {
		return "", err
	}
	s = base64.StdEncoding.EncodeToString(sig)
	return s, nil
}

type TradePayRes struct {
	Success     bool
	OrderID     string
	Price       float64
	IsStepPrice bool
	StepPrice   float64
	Code        string
	ErrSubCode  string
	ErrMsg      string
	ErrSubMsg   string
}

func (*alipay) TradePay(orderID, contractID, appID string, price float64) *TradePayRes {
	res := &TradePayRes{
		Success: false,
		OrderID: orderID,
		Price:   price,
	}
	bm := gopay.BodyMap{}
	client := InitAlipayClient(appID)
	if client == nil {
		return res
	}
	// 配置公共参数
	client.SetCharset(ali.UTF8).
		SetSignType(ali.RSA2).
		SetNotifyUrl("https://" + config.Get().Service.Domain + "/children/callback/alipay/tradepay")
	bm.Set("subject", "小树苗运动自动续订扣款").
		Set("out_trade_no", orderID).
		Set("total_amount", strconv.FormatFloat(price, 'f', 2, 64)).
		Set("product_code", "CYCLE_PAY_AUTH").
		Set("agreement_params", map[string]string{
			"agreement_no": contractID,
		})
	logger.Info("支付宝请求扣款接口,参数", contractID)
	payRes, err := client.TradePay(context.Background(), bm)
	if payRes != nil && payRes.Response != nil && payRes.Response.Code != "10000" {
		res.Code = payRes.Response.Code
		res.ErrSubCode = payRes.Response.SubCode
		res.ErrMsg = payRes.Response.Msg
		res.ErrSubMsg = payRes.Response.SubMsg
		if strings.TrimSpace(res.ErrMsg) == "order success pay inprocess" {
			res.ErrSubCode = "CustomErr_OrderSuccessPayInprocess"
		}
		logger.Warnf("支付宝下单失败：%v,%v,%v", err, res, bm)
		return res
	}
	if err != nil {
		logger.Warn("支付宝下单失败：", err, bm)
		return res
	}
	if payRes.Response != nil && payRes.Response.Code != "10000" {
		logger.Error("支付宝续订扣款失败", payRes, bm)
		return res
	}
	logger.Info("支付宝请求扣款接口,成功", bm)
	res.Success = true
	return res
}

// AgreementQueryExist 查询签约协议是否存在
func (a *alipay) AgreementQueryExist(contractCode, appID string) bool {
	resp := a.UserAgreementQuery(contractCode, appID)
	return resp != nil
}

// UserAgreementQuery 支付签约查询
func (a *alipay) UserAgreementQuery(contractCode, appID string) *ali.UserAgreementQueryRsp {
	bm := gopay.BodyMap{}
	client := InitAlipayClient(appID)
	if client == nil {
		return &ali.UserAgreementQueryRsp{}
	}
	// 配置公共参数
	client.SetCharset(ali.UTF8).
		SetSignType(ali.RSA2)
	bm.Set("personal_product_code", "CYCLE_PAY_AUTH_P").
		Set("sign_scene", "INDUSTRY|APPSTORE").
		Set("external_agreement_no", contractCode)
	payRes, err := client.UserAgreementQuery(context.Background(), bm)
	if err != nil {
		if strings.Contains(err.Error(), "USER_AGREEMENT_NOT_EXIST") {
			logger.Info("签约协议不存在", contractCode, appID)
			return nil
		}
		logger.Warn("支付宝查询协议信息报错：", err, bm)
		return payRes
	}
	logger.Info("AgreementQueryExist", payRes)
	return payRes
}

// AgreementChargeDateChange 扣款时间修改
func (*alipay) AgreementChargeDateChange(contractID, appID string, chargeTime int64) error {
	client := InitAlipayClient(appID)
	if client == nil {
		return errors.New("初始化支付宝client失败")
	}
	ct := time.Unix(chargeTime, 0)
	maxDay := 28
	if ct.Day() > maxDay {
		temp := time.Date(ct.Year(), ct.Month(), 1, 0, 0, 0, 0, time.Local)
		ct = temp.AddDate(0, 1, 0)
	}
	bmc := gopay.BodyMap{}
	bmc.Set("deduct_time", ct.Format("2006-01-02")).
		Set("agreement_no", contractID)
	payRes, err := client.UserAgreementExecutionplanModify(context.Background(), bmc)
	if err != nil {
		if strings.Contains(err.Error(), "ILLEGAL_EXECUTION_TIME") {
			logger.Warn("支付宝修改协议信息报错：", err, bmc)
			return nil
		}
		logger.Error("支付宝修改协议信息报错：", err, bmc)
		return err
	}
	logger.Info("AgreementChargeDateChange", payRes)
	return err
}

func MerchantEmailToCompany(merchantEmail string) int {
	for company, merchantList := range libpay.CompanyToMerchant {
		for i := range merchantList {
			if merchantList[i].Email == merchantEmail {
				return company
			}
		}
	}
	return 0
}
