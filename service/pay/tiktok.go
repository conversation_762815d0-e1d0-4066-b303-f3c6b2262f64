package pay

import (
	"context"
	"crypto" // #nosec
	"crypto/rand"
	"crypto/rsa" // #nosec
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/config"
	dbo "gitlab.dailyyoga.com.cn/server/children/databases/order"
	dbuser "gitlab.dailyyoga.com.cn/server/children/databases/user"
	libpay "gitlab.dailyyoga.com.cn/server/children/library/pay"
	libuser "gitlab.dailyyoga.com.cn/server/children/library/user"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
	"gitlab.dailyyoga.com.cn/server/gopay"
)

type douYin struct{}

var SrvDouYin douYin

type DouYinUnifiedOrder struct {
	ErrNo   int        `json:"err_no"`
	ErrTips string     `json:"err_tips"`
	Data    DouYinResp `json:"data"`
}
type DouYinResp struct {
	OrderID           string `json:"order_id"`
	OrderToken        string `json:"order_token"`
	Data              string `json:"data"`
	ByteAuthorization string `json:"byteAuthorization"`
	ContractCode      string `json:"contract_code"`
}

type TiktokPayOrderInfo struct {
	// 支付时需要的订单内容字段
	OrderID string
	Amount  float64
	// 支付时需要的描述内容字段
	Body          string
	Subject       string
	MchID         string
	TiktokPaySign *TiktokPaySignParams
	ProductID     int64
}
type TiktokPaySignParams struct {
	OutAuthOrderNo string `json:"outAuthOrderNo"` // 开发者侧签约单的单号
	ServiceID      string `json:"serviceId"`      // 签约模板ID
	OpenID         string `json:"openId"`         // 用户 openId
	NotifyURL      string `json:"notifyUrl"`      // 签约结果回调地址
	// 首次扣款日期,格式YYYY-MM-DD,纯签约场景需要传入,用于c端展示
	FirstDeductionDate string `json:"firstDeductionDate,omitempty"`
	// 扣款信息，如果传入该字段则会走签约支付流程，否则走纯签约流程
	AuthPayOrder *AuthPayOrder `json:"authPayOrder,omitempty"`
}
type AuthPayOrder struct {
	OutPayOrderNo string `json:"outPayOrderNo"` // 开发者侧代扣单的单号
	// 首期代扣金额，单位[分]，不传则使用模板上的扣款金额，签约模板支持前N（N<=6）期优惠，该字段优先级高于模板的配置的第一期优惠价格
	InitialAmount int    `json:"initialAmount"`
	NotifyURL     string `json:"notifyUrl"`   // 支付结果回调地址，https开头，长度<=512byte
	MerchantUID   string `json:"merchantUid"` // 开发者自定义收款商户号，限定在在小程序绑定的商户号内
}

const (
	AccessTokenOverdueCode    = 28001008
	TokenOverdueMinute        = 100
	AccessAppTokenOverdueCode = 40004
	fenPerYuan                = 100
	AccessTokenKey            = "d_tiktok_token"        //nolint
	ClientTokenKey            = "d_tiktok_client_token" //nolint
	OtherSettleParams         = "other_settle_params"   // 其他分账方参数 (Other settle params)
	AppID                     = "app_id"                // 小程序appID (Applets appID)
	ThirdPartyID              = "thirdparty_id"         // 代小程序进行该笔交易调用的第三方平台服务商 id
	Sign                      = "sign"                  // 签名 (sign)
)

type ReqNotify struct {
	Timestamp    string `json:"timestamp"`
	Nonce        string `json:"nonce"`
	Msg          string `json:"msg"`
	MsgSignature string `json:"msg_signature"`
	Type         string `json:"type"`
}
type MsgStruct struct {
	AppID          string `json:"appid"`
	OutOrderID     string `json:"out_order_id"`
	CPOrderNo      string `json:"cp_orderno"`
	CPExtra        string `json:"cp_extra"`
	Way            string `json:"way"`
	PaymentOrderNo string `json:"payment_order_no"`
	TotalAmount    int    `json:"total_amount"`
	Status         string `json:"status"`
	SellerUID      string `json:"seller_uid"`
	Extra          string `json:"extra"`
	ItemID         string `json:"item_id"`
	OrderID        string `json:"order_id"`
}

type PaymentStruct struct {
	AppID          string `json:"app_id"`
	OutOrderNo     string `json:"out_order_no"`
	OrderID        string `json:"order_id"`
	Status         string `json:"status"`
	TotalAmount    int    `json:"total_amount"`
	DiscountAmount int    `json:"discount_amount"`
	PayChannel     int    `json:"pay_channel"`
	ChannelPayID   string `json:"channel_pay_id"`
	MerchantUID    string `json:"merchant_uid"`
	Message        string `json:"message"`
	EventTime      int64  `json:"event_time"`
	UserBillPayID  string `json:"user_bill_pay_id"`
}

func generateJSON(bm gopay.BodyMap) (string, error) {
	jsonData, err := json.Marshal(bm)
	if err != nil {
		return "", err
	}
	return string(jsonData), nil
}

type SyncTiktokOrderResp struct {
	ErrCode int    `json:"err_code"`
	ErrMsg  string `json:"err_msg"`
	Body    string `json:"body"`
}

type TokenResp struct {
	ErrNo   int       `json:"err_no"`
	ErrTips string    `json:"err_tips"`
	Data    TokenData `json:"data"`
}

type TokenData struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
}

func (t *douYin) DelToken(appID string) {
	rd := cache.GetCRedis().GetClient()
	cacheKey := fmt.Sprintf("%s:%s", AccessTokenKey, appID)
	_ = rd.Del(context.Background(), cacheKey)
}

func (t *douYin) GetToken(appID string) string {
	cacheKey := fmt.Sprintf("%s:%s", AccessTokenKey, appID)
	rd := cache.GetCRedis().GetClient()
	cmd := rd.Get(context.Background(), cacheKey)
	if cmd.Err() != nil && cmd.Err() != redis.Nil {
		logger.Error(cmd.Err().Error())
		return ""
	}
	if cmd.Val() != "" {
		return cmd.Val()
	}
	uri := "https://developer.toutiao.com/api/apps/v2/token"
	header := http.Header{}
	header.Add("Content-Type", "application/json")
	bm := make(gopay.BodyMap)
	bm.Set("appid", config.Get().GetTikTokAccess(appID).AppID).
		Set("secret", config.Get().GetTikTokAccess(appID).AppSecret).
		Set("grant_type", "client_credential")
	requestParams, err := generateJSON(bm)
	logger.Info("Tiktok token 请求的参数", requestParams)
	if err != nil {
		logger.Error("Tiktok token json err", err)
		return ""
	}
	res, err := util.HTTPRequest(uri, http.MethodPost, header, strings.NewReader(requestParams))
	if err != nil {
		logger.Error("Tiktok token requests err", err)
		return ""
	}
	logger.Info("Tiktok token 返回结果", string(res))
	rsp := TokenResp{}
	if err := json.Unmarshal(res, &rsp); err != nil {
		logger.Error(err)
		return ""
	}
	if rsp.ErrNo != 0 {
		logger.Errorf("Tiktok token 请求返回报错 错误码：%d msg:%s", rsp.ErrNo, rsp.ErrTips)
		return ""
	}
	token := rsp.Data.AccessToken
	if token == "" {
		return ""
	}
	err = rd.Set(context.Background(), cacheKey, token, TokenOverdueMinute*time.Minute).Err()
	if err != nil {
		logger.Error("Tiktok token 请求返回报错 Value 失败 ", err.Error())
		return ""
	}
	return token
}

// GetByteAuthorization 生成参数
func (t *douYin) GetByteAuthorization(data, mchID, uri string) (string, error) {
	var byteAuthorization string
	appID, ok := libpay.TikTokMchIDToAppID[mchID]
	if !ok {
		return "", fmt.Errorf("未能读取到appID")
	}
	conf := config.Get().GetTikTokPemPath(appID)
	if conf == nil {
		return "", fmt.Errorf("未能读取证书")
	}
	privateKey, err := util.GetPKCS1PrivateKey(conf.PrivateKey)
	if err != nil {
		return "", fmt.Errorf("解密PKCS1未能读取证书")
	}
	nonceStr := util.GetRandomString(10) // nolint
	timestamp := strconv.FormatInt(time.Now().Unix(), 10)
	keyVersion := config.Get().GetTikTokAccess(appID).KeyVersion
	// 生成签名
	signature, err := getSignature("POST", uri, timestamp, nonceStr, data, privateKey)
	if err != nil {
		return "", err
	}
	logger.Infof("GetByteAuthorization data:%+v", data)
	// 构造byteAuthorization
	byteAuthorization = fmt.Sprintf("SHA256-RSA2048 appid=%s,nonce_str=%s,"+
		"timestamp=%s,key_version=%s,signature=%s", appID, nonceStr, timestamp, keyVersion, signature)
	logger.Infof("byteAuthorization data:%s", byteAuthorization)
	return byteAuthorization, nil
}

func getSignature(method, url, timestamp, nonce, data string, privateKey *rsa.PrivateKey) (string, error) {
	targetStr := method + "\n" + url + "\n" + timestamp + "\n" + nonce + "\n" + data + "\n"
	h := sha256.New()
	_, _ = h.Write([]byte(targetStr))
	digestBytes := h.Sum(nil)
	signBytes, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA256, digestBytes)
	if err != nil {
		return "", err
	}
	sign := base64.StdEncoding.EncodeToString(signBytes)
	return sign, nil
}

type RespCheckSign struct {
	Version string `json:"version"`
	Msg     string `json:"msg"`
	Type    string `json:"type"`
}

type CheckSignCallback struct {
	AppID string `json:"app_id"`
}

type TiktokCycleResp struct {
	Data struct {
		PayOrderID string `json:"pay_order_id"`
	} `json:"data"`
	ErrNo  int    `json:"err_no"`
	ErrMsg string `json:"err_msg"`
	LogID  string `json:"log_id"`
}

// nolint
func (t *douYin) TiktokCycleCreate(orderID string, contract *dbo.TiktokContract,
	price float64, times int, domainURL string) *TiktokCycleResp {
	logger.Infof("tiktok TiktokCycleCreate %+v %+v", orderID, contract.ContractID)
	uri := "https://open.douyin.com/api/trade_auth/v1/developer/create_sign_pay"
	notifyURL := "https://" + domainURL + "/microapp/callback/tiktok/tradepay"
	header := http.Header{}
	header.Add("Content-Type", "application/json")
	header.Add("access-token", t.GetClientToken(libpay.TikTokMchIDToAppID[contract.MerchantID]))
	bm := make(gopay.BodyMap)
	bm.Set("auth_order_id", contract.ContractID).
		Set("expire_seconds", 86400).
		Set("merchant_uid", contract.MerchantID).
		Set("out_pay_order_no", orderID).
		Set("total_amount", int(math.Round(price*float64(fenPerYuan)))).
		Set("notify_url", notifyURL)
	requestParams, err := generateJSON(bm)
	logger.Info("tiktok TiktokCycleCreate 请求的参数", requestParams)
	if err != nil {
		logger.Error("Tiktok TiktokCycleCreate 失败", err)
		return nil
	}
	res, err := util.HTTPRequest(uri, http.MethodPost, header, strings.NewReader(requestParams))
	if err != nil {
		logger.Error("Tiktok TiktokCycleCreate 失败请求的参数", err)
		return nil
	}
	logger.Info("Tiktok TiktokCycleCreate 创建扣款", string(res))
	rsp := TiktokCycleResp{}
	if err := json.Unmarshal(res, &rsp); err != nil {
		logger.Error(err)
		return nil
	}
	if rsp.ErrNo != 0 {
		if rsp.ErrNo == AccessTokenOverdueCode && times < 2 {
			t.DelClientToken(libpay.TikTokMchIDToAppID[contract.MerchantID])
			times++
			return t.TiktokCycleCreate(orderID, contract, price, times, domainURL)
		}
		logger.Warnf("Tiktok 请求返回报错 错误码：%d msg:%s", rsp.ErrNo, rsp.ErrMsg)
		return nil
	}
	return &rsp
}

type AutoGenerated struct {
	AccessToken string `json:"access_token"`
	Description string `json:"description"`
	ErrorCode   int    `json:"error_code"`
	ExpiresIn   int    `json:"expires_in"`
}
type ClientTokenResp struct {
	Data  ClientTokenData `json:"data"`
	Extra struct {
		LogID string `json:"logid"`
		Now   int64  `json:"now"`
	} `json:"extra"`
}

type ClientTokenData struct {
	Description string `json:"description"`
	ErrorCode   int    `json:"error_code"`
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
}

func (t *douYin) DelClientToken(appID string) {
	rd := cache.GetCRedis().GetClient()
	cacheKey := fmt.Sprintf("%s:%s", ClientTokenKey, appID)
	_ = rd.Del(context.Background(), cacheKey)
}

func (t *douYin) GetClientToken(appID string) string {
	rd := cache.GetCRedis().GetClient()
	cacheKey := fmt.Sprintf("%s:%s", ClientTokenKey, appID)
	cmd := rd.Get(context.Background(), cacheKey)
	if cmd.Err() != nil && cmd.Err() != redis.Nil {
		logger.Error(cmd.Err().Error())
		return ""
	}
	if cmd.Val() != "" {
		return cmd.Val()
	}
	uri := "https://open.douyin.com/oauth/client_token"
	header := http.Header{}
	header.Add("Content-Type", "application/json")
	bm := make(gopay.BodyMap)
	bm.Set("client_key", config.Get().GetTikTokAccess(appID).AppID).
		Set("client_secret", config.Get().GetTikTokAccess(appID).AppSecret).
		Set("grant_type", "client_credential")
	requestParams, err := generateJSON(bm)
	logger.Info("Tiktok ClientToken 请求的参数", requestParams)
	if err != nil {
		logger.Error("Tiktok ClientToken json err", err)
		return ""
	}
	res, err := util.HTTPRequest(uri, http.MethodPost, header, strings.NewReader(requestParams))
	if err != nil {
		logger.Error("Tiktok ClientToken requests err", err)
		return ""
	}
	if len(res) < 1 {
		logger.Error("Tiktok ClientToken requests empty", requestParams)
		return ""
	}
	logger.Info("Tiktok ClientToken 返回结果", string(res))
	rsp := ClientTokenResp{}
	if err := json.Unmarshal(res, &rsp); err != nil {
		logger.Error(err)
		return ""
	}
	if rsp.Data.ErrorCode != 0 {
		logger.Errorf("Tiktok ClientToken 请求返回报错 错误码：%d msg:%s", rsp.Data.ErrorCode, rsp.Data.Description)
		return ""
	}
	token := rsp.Data.AccessToken
	if token == "" {
		return ""
	}
	err = rd.Set(context.Background(), cacheKey, token, TokenOverdueMinute*time.Minute).Err()
	if err != nil {
		logger.Error("Tiktok ClientToken 请求返回报错 Value 失败 ", err.Error())
		return ""
	}
	return token
}

func GetWxMiNiAppOpenID(uid int64) string {
	accountThird := dbuser.TbAccountThirdAuth.GetThirdInfoByUID(uid, libuser.LoginTypeWechat)
	if accountThird == nil {
		return ""
	}
	return accountThird.ThirdOpenID
}
