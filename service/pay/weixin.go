package pay

import (
	"context"
	"fmt"
	"math"
	"net/url"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/gopay"

	"gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/tools"

	wx3 "gitlab.dailyyoga.com.cn/server/gopay/wechat/v3"
)

type weixin struct{}

var SrvWeixin weixin

type WeixinUnifiedOrder struct {
	PrePayID  string
	NonceStr  string
	TimeStamp int64
	PaySign   string
	PartnerID string
	MwebURL   string
}

type UnifiedOrderCompleteNotify struct {
}

func (*weixin) BuildPreOrder(order *PayOrderInfo, times int) (*WeixinUnifiedOrder, error) {
	c := config.Get().WechatPay
	if c.AppID == "" {
		logger.Warnf("配置数据数据异常 用户无法创建订单 uid:%d", order.UID)
		return nil, fmt.Errorf("创建订单失败")
	}
	notifyurl := "https://" + config.Get().Service.Domain + "/children/callback/weixin/unifiedorder"
	expire := time.Now().Add(10 * time.Minute).Format(time.RFC3339) //nolint
	// 初始化参数Map
	fenPerYuan := 100
	bm := make(gopay.BodyMap)
	bm.Set("appid", c.AppID).
		Set("mchid", c.MchID).
		Set("description", order.Body).
		Set("out_trade_no", order.OrderID).
		Set("time_expire", expire).
		Set("notify_url", notifyurl).
		SetBodyMap("amount", func(bm gopay.BodyMap) {
			bm.Set("total", int(math.Round(order.Amount*float64(fenPerYuan)))).
				Set("currency", "CNY")
		})
	client, err := getV3ClientV3(&c)
	if err != nil {
		return nil, fmt.Errorf("获取配置异常 %+v", err)
	}
	wxRsp, err := client.V3TransactionApp(context.Background(), bm)
	if err != nil {
		return nil, err
	}
	if wxRsp.Code != wx3.Success {
		return nil, fmt.Errorf("获取微信小程序支付单号错误 %+v", wxRsp)
	}
	signData, err := client.PaySignOfApp(c.AppID, wxRsp.Response.PrepayId)
	if err != nil {
		return nil, err
	}
	timeStamp, _ := strconv.ParseInt(signData.Timestamp, 10, 64)
	res := &WeixinUnifiedOrder{
		PrePayID:  signData.Prepayid,
		NonceStr:  signData.Noncestr,
		TimeStamp: timeStamp,
		PartnerID: c.MchID,
		PaySign:   signData.Sign,
	}
	return res, nil
}

func (*weixin) BuildH5PreOrder(order *PayOrderInfo, times int, ac *library.AppClient) (*WeixinUnifiedOrder, error) {
	c := config.Get().WechatPay
	if c.AppID == "" {
		logger.Warnf("配置数据数据异常 用户无法创建订单 uid:%d", order.UID)
		return nil, fmt.Errorf("创建订单失败")
	}
	notifyurl := "https://" + config.Get().Service.Domain + "/children/callback/weixin/unifiedh5order"
	expire := time.Now().Add(10 * time.Minute).Format(time.RFC3339) //nolint
	bm := make(gopay.BodyMap)
	bm.Set("appid", c.AppID).
		Set("mchid", c.MchID).
		Set("description", order.Body).
		Set("out_trade_no", order.OrderID).
		Set("time_expire", expire).
		Set("notify_url", notifyurl).
		SetBodyMap("amount", func(bm gopay.BodyMap) {
			bm.Set("total", int(math.Round(order.Amount*float64(fenPerYuan)))).
				Set("currency", "CNY")
		}).SetBodyMap("scene_info", func(bm gopay.BodyMap) {
		bm.Set("payer_client_ip", ac.IPAds)
	})
	client, err := getV3ClientV3(&c)
	if err != nil {
		return nil, fmt.Errorf("获取配置异常 %+v", err)
	}
	wxRsp, err := client.V3TransactionH5(context.Background(), bm)
	if err != nil {
		return nil, err
	}
	if wxRsp.Code != wx3.Success {
		return nil, fmt.Errorf("获取微信小程序支付单号错误 %+v", wxRsp)
	}
	res := &WeixinUnifiedOrder{
		TimeStamp: time.Now().Unix(),
		PartnerID: c.MchID,
		MwebURL:   wxRsp.Response.H5Url + "&redirect_url=" + url.QueryEscape("h5.dailyworkout.com.cn://wxpay"),
	}
	return res, nil
}

func getV3ClientV3(c *config.WeChatPay) (*wx3.ClientV3, error) {
	privateKeyByte, err := tools.ReadFile(fmt.Sprintf("/wechat/%s-apiclient_key.pem", c.MchID))
	if err != nil {
		return nil, err
	}
	client, err := wx3.NewClientV3(c.MchID, c.CertificateNumber, c.APIV3Key, string(privateKeyByte))
	if err != nil {
		return nil, err
	}
	return client, nil
}
