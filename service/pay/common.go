package pay

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	dbo "gitlab.dailyyoga.com.cn/server/children/databases/order"
	dbu "gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libcache "gitlab.dailyyoga.com.cn/server/children/library/cache"
	"gitlab.dailyyoga.com.cn/server/children/library/pay"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
)

func SetPaymentPageIDCache(orderID string, paymentPageID int64) {
	if paymentPageID > 0 {
		rd := cache.GetCRedis().GetClient()
		if err := rd.Set(context.Background(), libcache.OrderPaymentPageID+orderID, paymentPageID,
			time.Hour*24*pay.WeekToDay).Err(); err != nil {
			logger.Error("订单支付的付费页ID设置失败, ", orderID, err.Error())
		}
	}
}

func GetPaymentPageIDCache(orderID string) int {
	if orderID != "" {
		rd := cache.GetCRedis().GetClient()
		pageID := rd.Get(context.Background(), libcache.OrderPaymentPageID+orderID).Val()
		if pageID != "" && pageID != "0" {
			pageIDResult, _ := strconv.Atoi(pageID)
			return pageIDResult
		}
	}
	return 0
}

func SetDealUpdateTimesCache(orderID string, dealUpdateTimes int64) {
	if dealUpdateTimes > 0 {
		rd := cache.GetCRedis().GetClient()
		if err := rd.Set(context.Background(), libcache.DealUpdateTimes+orderID, dealUpdateTimes,
			time.Hour*24*pay.WeekToDay).Err(); err != nil {
			logger.Error("订单支付的付费页ID设置失败, ", orderID, err.Error())
		}
	}
}

func GetDealUpdateTimes(orderID string) int {
	if orderID != "" {
		rd := cache.GetCRedis().GetClient()
		pageID := rd.Get(context.Background(), libcache.DealUpdateTimes+orderID).Val()
		if pageID != "" && pageID != "0" {
			pageIDResult, _ := strconv.Atoi(pageID)
			return pageIDResult
		}
	}
	return 0
}

func SetUserVipStatusCache(uid int64) {
	accountInfo := dbu.TbAccount.GetUserByID(uid)
	if accountInfo == nil {
		return
	}
	vipStatus := library.No
	if accountInfo.EndTime > time.Now().Unix() {
		vipStatus = library.Yes
	}
	rd := cache.GetCRedis().GetClient()
	if err := rd.Set(context.Background(), libcache.UserVipStatus+fmt.Sprintf("%d", uid), vipStatus,
		time.Hour*24*pay.WeekToDay).Err(); err != nil {
		logger.Error("会员状态设置失败, ", uid, err.Error())
	}
}

func GetUserVipStatus(uid int64) bool {
	rd := cache.GetCRedis().GetClient()
	vipStatus := rd.Get(context.Background(), libcache.UserVipStatus+fmt.Sprintf("%d", uid)).Val()
	if vipStatus != "" && vipStatus != "0" {
		vipStatusResult, _ := strconv.Atoi(vipStatus)
		if vipStatusResult == library.Yes {
			return true
		}
	}
	return false
}

func SetUserOldBuyNumCache(uid int64) {
	orderList := dbo.TbWebOrder.GetListByUID(uid)
	rd := cache.GetCRedis().GetClient()
	if err := rd.Set(context.Background(), libcache.UserOldBuyNum+fmt.Sprintf("%d", uid), len(orderList),
		time.Hour*24*pay.WeekToDay).Err(); err != nil {
		logger.Error("会员状态设置失败, ", uid, err.Error())
	}
}

func GetUserOldBuyNum(uid int64) int {
	rd := cache.GetCRedis().GetClient()
	vipStatus := rd.Get(context.Background(), libcache.UserOldBuyNum+fmt.Sprintf("%d", uid)).Val()
	if vipStatus != "" && vipStatus != "0" {
		vipStatusResult, _ := strconv.Atoi(vipStatus)
		return vipStatusResult
	}
	return 0
}
