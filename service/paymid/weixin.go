package paymid

import (
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/service/pay"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	srvorder "gitlab.dailyyoga.com.cn/server/children/service/order"
)

type _Weixin struct{}

func (o _Weixin) PreOrderPay(order *srvorder.PayOrderParam, ac *library.AppClient) interface{} {
	payOrder := &pay.PayOrderInfo{
		Amount:  order.Amount,
		OrderID: order.OrderID,
		Body:    order.Body,
		Subject: order.Subject,
	}
	var paydata *pay.WeixinUnifiedOrder
	var err error
	if order.IsH5Pay {
		paydata, err = pay.SrvWeixin.BuildH5PreOrder(payOrder, 0, ac)
	} else {
		paydata, err = pay.SrvWeixin.BuildPreOrder(payOrder, 0)
	}
	if err != nil {
		logger.Error("微信支付失败", err.<PERSON><PERSON><PERSON>(), *payOrder)
		return nil
	}
	return paydata
}

func (o _Weixin) FormatPayResponse(order *srvorder.PayOrderParam, paydata interface{}) *PreOrderPayInfo {
	payinfo, ok := paydata.(*pay.WeixinUnifiedOrder)
	if !ok {
		logger.Error("微信返回值format类型错误", *order, paydata)
		return nil
	}
	res := &PreOrderPayInfo{
		OrderID:      order.OrderID,
		PrePayID:     payinfo.PrePayID,
		PackageValue: "Sign=WXPay",
		Package:      "Sign=WXPay",
		NonceStr:     payinfo.NonceStr,
		TimeStamp:    payinfo.TimeStamp,
		PartnerID:    payinfo.PartnerID,
		Sign:         payinfo.PaySign,
		MwebURL:      payinfo.MwebURL,
	}
	return res
}
