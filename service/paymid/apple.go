package paymid

import (
	"gitlab.dailyyoga.com.cn/server/children/library"
	srvorder "gitlab.dailyyoga.com.cn/server/children/service/order"
)

type _Apple struct{}

func (a _Apple) PreOrderPay(order *srvorder.PayOrderParam, _ *library.AppClient) interface{} {
	return map[int]int{}
}

func (a _Apple) FormatPayResponse(order *srvorder.PayOrderParam, paydata interface{}) *PreOrderPayInfo {
	return &PreOrderPayInfo{
		OrderID: order.OrderID,
	}
}
