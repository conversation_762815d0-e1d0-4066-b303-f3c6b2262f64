package paymid

import (
	"strconv"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libpay "gitlab.dailyyoga.com.cn/server/children/library/pay"
	srvorder "gitlab.dailyyoga.com.cn/server/children/service/order"
	"gitlab.dailyyoga.com.cn/server/children/service/pay"
)

type _Alipay struct{}

func (o _Alipay) PreOrderPay(order *srvorder.PayOrderParam, _ *library.AppClient) interface{} {
	payOrder := &pay.PayOrderInfo{
		Amount:  order.Amount,
		OrderID: order.OrderID,
		Body:    order.Body,
		Subject: order.Subject,
		MchID:   order.MchID,
	}
	if order.IsSubscribe == library.Yes && order.ContractInfo != nil {
		payOrder.AlipaySign = &pay.AlipaySignParams{
			ContractCode: order.ContractInfo.ContractCode,
			PeriodType:   order.ContractInfo.PeriodType,
			Period:       order.ContractInfo.Period,
			Price:        strconv.FormatFloat(order.ContractInfo.Price, 'f', 2, 64),
			ExecuteTime:  order.ContractInfo.ExecuteTime,
			ServiceDes:   order.ContractInfo.ServiceDes,
			OrgPrice:     strconv.FormatFloat(order.OrgAmount, 'f', 2, 64),
		}
		var payRes *pay.AlipayPreOrder
		var err error
		if order.ContractInfo.SubscribeMode == libpay.SubscribeModeEnum.PayAndSubscribe {
			payRes, err = pay.SrvAlipay.AggrementPaySign(payOrder)
		} else {
			payRes, err = pay.SrvAlipay.UserAgreementPageSign(payOrder)
		}
		if err != nil {
			logger.Error("【支付宝】支付失败", *payOrder)
			return nil
		}
		return payRes
	}
	var payRes *pay.AlipayPreOrder
	var err error
	if order.IsH5Pay {
		payRes, err = pay.SrvAlipay.BuildH5PreOrder(payOrder)
	} else {
		payRes, err = pay.SrvAlipay.BuildPreOrder(payOrder)
	}
	if err != nil {
		logger.Error("【支付宝】支付失败", *payOrder)
		return nil
	}
	return payRes
}

func (o _Alipay) FormatPayResponse(order *srvorder.PayOrderParam, paydata interface{}) *PreOrderPayInfo {
	payInfo, ok := paydata.(*pay.AlipayPreOrder)
	if !ok {
		logger.Error("【支付宝】返回值format类型错误", *order, paydata)
		return nil
	}
	res := &PreOrderPayInfo{
		OrderID: order.OrderID,
		PayInfo: payInfo.PayInfo,
	}
	if order.ContractInfo != nil && order.ContractInfo.SubscribeMode > 0 {
		res.SubscribeMode = int(order.ContractInfo.SubscribeMode)
	}
	return res
}
