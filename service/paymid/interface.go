package paymid

import (
	"gitlab.dailyyoga.com.cn/server/children/library"
	srvorder "gitlab.dailyyoga.com.cn/server/children/service/order"

	libpay "gitlab.dailyyoga.com.cn/server/children/library/pay"
)

type IPay interface {
	PreOrderPay(order *srvorder.PayOrderParam, ac *library.AppClient) interface{}
	FormatPayResponse(order *srvorder.PayOrderParam, paydata interface{}) *PreOrderPayInfo
}

var _payType2IPay = map[int]IPay{
	libpay.PayTypeAlipay: new(_Alipay),
	libpay.PayTypeWechat: new(_Weixin),
	libpay.PayTypeApple:  new(_Apple),
}

func NewIPayByType(t int) IPay {
	return _payType2IPay[t]
}
