package paymid

import (
	"encoding/json"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	dborder "gitlab.dailyyoga.com.cn/server/children/databases/order"
	"gitlab.dailyyoga.com.cn/server/children/library"
	srvorder "gitlab.dailyyoga.com.cn/server/children/service/order"
)

// 预下单支付返回值字段
type PreOrderPayInfo struct {
	OrderID       string `json:"order_id"`
	SubscribeMode int    `json:"subscribe_mode"`
	// 微信支付字段
	PrePayID     string `json:"prepayid"`
	PackageValue string `json:"packageValue"`
	Package      string `json:"package"`
	NonceStr     string `json:"noncestr"`
	TimeStamp    int64  `json:"timestamp"`
	PartnerID    string `json:"partnerid"`
	Sign         string `json:"sign"`
	MwebURL      string `json:"mweb_url"`
	// 支付宝支付字段
	PayInfo string `json:"pay_info"`
}

// GoToPay 调用支付
func GoToPay(order *srvorder.PayOrderParam, payType int, ac *library.AppClient) *PreOrderPayInfo {
	of := NewIPayByType(payType)
	if of == nil {
		logger.Error("GoToPay 未识别payType", payType)
		return nil
	}
	payData := of.PreOrderPay(order, ac)
	if payData == nil {
		return nil
	}
	res := of.FormatPayResponse(order, payData)
	if res == nil {
		return nil
	}
	return res
}

// LogCompleteOrderNotify 记录订单回调
func LogCompleteOrderNotify(orderID string, payType int,
	notify map[string]interface{}) (*dborder.CompleteNotify, error) {
	cn := dborder.TbCompleteNofify.GetItemByOrderID(orderID)
	// 已经记录成功 返回
	if cn != nil {
		return cn, nil
	}
	data, err := json.Marshal(notify)
	if err != nil {
		logger.Error(err)
		return nil, err
	}
	cn = &dborder.CompleteNotify{
		OrderID:     orderID,
		Notify:      string(data),
		PayType:     payType,
		IsProcessed: library.No,
	}
	err = cn.Save()
	if err != nil {
		logger.Error(err)
		return nil, err
	}
	return cn, err
}
