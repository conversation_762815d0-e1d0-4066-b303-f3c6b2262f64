package sensor

import (
	"fmt"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	sc "gitlab.dailyyoga.com.cn/gokit/sensorsdata"
	"gitlab.dailyyoga.com.cn/server/children/config"
	libcache "gitlab.dailyyoga.com.cn/server/children/library/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
)

// ScComplianceEvent 神策事件上报
type ScComplianceEvent struct {
	City1        string `json:"city1"`
	QuDao        string `json:"qudao"`
	QuDaoDetail  string `json:"qudao_detail"`
	IsCompliance bool   `json:"is_compliance"`
	CommData     `json:"common_data"`
}

// EventName 事件名称
func (ScComplianceEvent) EventName() string {
	if config.Get().Service.Env != microservice.Product && config.Get().Service.Env != microservice.Mirror {
		return "h2_compliance_cs"
	}
	return "compliance_cs"
}

func (ScComplianceEvent) Prefix() string {
	return ""
}

func (s *ScComplianceEvent) Track(uid string) {
	rd := cache.GetCRedis()
	expire := getTodayEndDuration()
	lockKey := fmt.Sprintf("%s_%s", libcache.LockComplianceEvent, uid)
	if lock := rd.Lock(lockKey, cache.LockExpiration(expire)); !lock {
		logger.Info("上报用户合规已被锁定", s, uid)
		return
	}
	go func(data *ScComplianceEvent) {
		if uid == "" || uid == "0" {
			return
		}
		err := sc.Track(uid, *data, true)
		if err != nil {
			logger.Error("神策过期事件上报失败", err)
		}
	}(s)
}

// getTodayEndDuration 获取当天的结束时间
func getTodayEndDuration() time.Duration {
	loc, _ := time.LoadLocation("Asia/Shanghai")
	now := time.Now()
	tomorrow := time.Date(now.Year(), now.Month(), now.Day()+1, 00, 00, 00, 0, loc)
	return tomorrow.Sub(now)
}
