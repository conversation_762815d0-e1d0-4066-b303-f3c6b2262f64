package sensor

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	sc "gitlab.dailyyoga.com.cn/gokit/sensorsdata"
	"gitlab.dailyyoga.com.cn/server/children/config"
)

type ScheduleStatus struct {
	CommData           `json:"common_data"`
	ScheduleTemplateID int64 `json:"schedule_template_id"`
}

// EventName 订阅产品订阅事件上报
func (ScheduleStatus) EventName() string {
	if config.Get().Service.Env != microservice.Product && config.Get().Service.Env != microservice.Mirror {
		return "h2_schedule_status_cs"
	}
	return "schedule_status_cs"
}
func (ScheduleStatus) Prefix() string {
	return ""
}
func (u *ScheduleStatus) Track(uid string) {
	go func() {
		if uid == "" || uid == "0" {
			return
		}
		err := sc.Track(uid, *u, true)
		if err != nil {
			logger.Error("神策过期事件上报失败", err)
		}
	}()
}
