package sensor

import (
	"context"
	"encoding/json"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/gokit/sensorsdata"
	"gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libcache "gitlab.dailyyoga.com.cn/server/children/library/cache"
	libclient "gitlab.dailyyoga.com.cn/server/children/library/client"
	libuser "gitlab.dailyyoga.com.cn/server/children/library/user"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type SignUpProfikeMsg struct {
	CreateTime       int64  `json:"create_time"`
	UID              int64  `json:"uid"`
	Channel          string `json:"channel"`
	ManualSignupTime int64  `json:"manual_signup_time"`
}

const JobSignUpProfileDelay = 180

// ReportSignUpProfile 上报神策注册属性
func ReportSignUpProfile(uid, manualSignupTime int64, client *library.AppClient) {
	if client == nil {
		return
	}
	msg := &SignUpProfikeMsg{
		CreateTime: time.Now().Unix(),
		UID:        uid,
		Channel:    strconv.Itoa(client.Channel),
	}
	if manualSignupTime > 0 {
		msg.ManualSignupTime = manualSignupTime
	}
	msgStr, err := json.Marshal(msg)
	if err != nil {
		logger.Error(err)
		return
	}
	rdc := cache.GetCRedis().GetClient()
	var delay int64 = 180
	member := &redis.Z{
		Score:  float64(msg.CreateTime + delay),
		Member: string(msgStr),
	}
	ret, err := rdc.ZAdd(context.Background(), libcache.SensorSignUpProfileSet, member).Result()
	if err != nil {
		logger.Error(err)
		return
	}
	if ret == 0 {
		logger.Error("神策上报用户注册属性写入redis失败", string(msgStr), ret)
	}
}

// nolint
func ReportSignUpProfileCron() {
	rd := cache.GetCRedis()
	lockKey := libcache.LockCronSignUpProfile
	if lock := rd.Lock(lockKey, cache.LockExpiration(time.Hour)); !lock {
		logger.Info("已被锁定")
		return
	}
	defer func() {
		if err := rd.Unlock(lockKey); err != nil {
			logger.Error(err)
		}
	}()
	logger.Info("注册渠道上报任务开始")
	rdc := cache.GetCRedis()
	dataKey := libcache.SensorSignUpProfileSet
	for {
		opt := &redis.ZRangeBy{
			Min:    "0",
			Max:    strconv.FormatInt(time.Now().Unix(), 10),
			Offset: 0,
			Count:  30,
		}
		jobs, err := rdc.GetClient().ZRangeByScore(context.Background(), dataKey, opt).Result()
		if err != nil {
			logger.Error("查询注册渠道上报任务失败：", err)
			break
		}
		if len(jobs) == 0 {
			break
		}
		uidArr := make([]string, 0)
		for _, job := range jobs {
			var msg = new(SignUpProfikeMsg)
			if err := json.Unmarshal([]byte(job), msg); err != nil {
				logger.Error("注册渠道json解析失败", err)
				continue
			}
			if msg == nil {
				continue
			}
			account := user.TbAccount.GetUserByID(msg.UID)
			if account == nil {
				continue
			}
			uidArr = append(uidArr, strconv.FormatInt(msg.UID, 10))
		}
		logger.Info("注册渠道上报请求值", uidArr)
		utmMap := util.GetSensorUserInfoMap(uidArr, library.FormTypeByUID)
		for k, v := range utmMap {
			logger.Infof("注册渠道上报返回值 %s %+v", k, v)
		}
		for _, job := range jobs {
			ret, err := rdc.GetClient().ZRem(context.Background(), dataKey, job).Result()
			// 删除失败一般是另一个进程在跑，直接返回
			if err != nil || ret <= 0 {
				logger.Warn("注册渠道上报任务删除任务失败:", job)
				return
			}
			// 处理神策注册渠道任务
			processSignUpProfile(job, utmMap)
		}
	}
	logger.Info("注册渠道上报任务结束")
}

// processSignUpProfile 处理注册属性上报
func processSignUpProfile(job string, utmMap map[string]*util.ScResponse) {
	var msg = new(SignUpProfikeMsg)
	err := json.Unmarshal([]byte(job), msg)
	account := user.TbAccount.GetUserByID(msg.UID)
	if err != nil {
		logger.Error("注册渠道json解析失败", err)
		return
	}
	signupTime := time.Now().Format(library.TimeFormat)
	if account != nil && account.CreateTime != 0 {
		signupTime = time.Unix(account.CreateTime, 0).Format(library.TimeFormat)
	}
	utm := utmMap[strconv.FormatInt(msg.UID, 10)]
	p := map[string]interface{}{
		"signup_time":           signupTime,
		"regist_channel":        "安装包",
		"regist_channel_detail": libclient.ChannelName[msg.Channel],
	}
	if msg.ManualSignupTime > 0 {
		p["manual_signup_time"] = time.Unix(msg.ManualSignupTime, 0).Format(library.TimeFormat)
	}
	if account != nil && account.Mobile != "" {
		p["mobile"] = account.Mobile
	}
	if utm != nil && utm.UtmSource != "" {
		p["regist_channel"] = "APP推广"
		p["regist_channel_detail"] = utm.UtmSource
	}
	aso := false
	if utm != nil && utm.Aso == "true" {
		aso = true
	}
	if utm != nil && aso {
		p["regist_channel"] = "积分墙"
		p["regist_channel_detail"] = utm.UtmSource
	}
	logger.Infof("用户属性注册渠道上报：%v,utm值:%v", p, utm)
	err = sensorsdata.ProfileSet(msg.UID, p, true)
	if err != nil {
		logger.Errorf("用户属性注册渠道上报失败 %v", err)
	}
}

func UserProfileVipType(uid int64) {
	account := user.TbAccount.GetUserByID(uid)
	if account == nil {
		return
	}
	isVip := libuser.ProfileVipTypeNotVip
	if account.EndTime > time.Now().Unix() {
		isVip = libuser.ProfileVipTypeVip
	}
	p := map[string]interface{}{
		"user_profile_vip_type": isVip,
	}
	logger.Info("user_profile_vip_type", uid, isVip)
	err := sensorsdata.ProfileSet(uid, p, true)
	if err != nil {
		logger.Errorf("用户属性注册渠道上报失败 %v", err)
	}
}

func UserProfileSubChannel(uid int64, client *library.AppClient) {
	if strconv.Itoa(client.Channel) == libclient.ChannelXiaomi {
		p := map[string]interface{}{
			"DownloadSecondChannel": client.SubChannel,
		}
		err := sensorsdata.ProfileSet(uid, p, true)
		if err != nil {
			logger.Errorf("用户属性注册渠道上报失败 %v", err)
		}
		return
	}
}
