package sensor

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	sc "gitlab.dailyyoga.com.cn/gokit/sensorsdata"
	srvconf "gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/databases/order"
	dbu "gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libc "gitlab.dailyyoga.com.cn/server/children/library/client"
	libs "gitlab.dailyyoga.com.cn/server/children/library/sensor"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type CommData struct {
	AppVersion string `json:"app_version"`
	Platform   string `json:"platform"`
	MiniName   string `json:"mini_name"`
	FromSource int    `json:"from_source"`
}

func InitCommDataByClient(client *library.AppClient) CommData {
	res := CommData{
		AppVersion: client.Version,
		Platform:   "android",
	}
	if client.OsType > 0 {
		res.Platform = libc.DeviceTypeDesc[libc.DeviceTypeInt(client.OsType)]
	}
	if strconv.Itoa(client.Channel) == libc.ChannelApple {
		res.Platform = "ios"
	}
	if strconv.Itoa(client.Channel) == libc.ChannelTiktok {
		res.MiniName = library.AppNameCN
	}
	if strconv.Itoa(client.Channel) == libc.ChannelWechatMiNiApp {
		res.MiniName = library.AppNameCN
	}
	res.FromSource = libc.FromSourceApp
	if strconv.Itoa(client.Channel) == libc.ChannelWechatMiNiApp {
		res.FromSource = libc.FromSourceWechat
	}
	if strconv.Itoa(client.Channel) == libc.ChannelTiktok {
		res.FromSource = libc.FromSourceTiktok
	}
	return res
}

// nolint
func GetAppPackageName(client *library.AppClient) string {
	if strconv.Itoa(client.Channel) == libc.ChannelApple {
		return "com.yinniu.children.ios"
	}
	return "com.yinniu.children.android"
}

type ClickEvent struct {
	CommData        `json:"common_data"`
	DownloadChannel string `json:"DownloadChannel"`
	ClickID         int    `json:"click_id"`
	ClickSourceURL  string `json:"click_source_url"`
	PageID          int    `json:"page_id"`
}

// EventName 注册事件上报
// nolint
func (c ClickEvent) EventName() string {
	if c.PageID > 0 {
		return "pageview_general_cs"
	}
	return "click_general_cs"
}

func (ClickEvent) Prefix() string {
	return ""
}
func (c *ClickEvent) Track(uid string) {
	go func() {
		err := sc.Track(uid, *c, true)
		if err != nil {
			logger.Error("神策过期事件上报失败", err)
		}
	}()
}

// TrackSignup 神策匿名ID和注册ID的绑定
func TrackSignup(uid int64, anonymousID string) {
	if uid <= 0 || anonymousID == "" {
		return
	}
	safelygo.GoSafelyByTraceID(func() {
		err := sc.TrackSignup(uid, anonymousID)
		if err != nil {
			logger.Error("神策过期事件上报失败", err)
		}
	})
}

type ScResponse struct {
	UID       string `json:"second_id"`
	UtmSource string `json:"$utm_source"`
	UtmMedium string `json:"$utm_medium"`
	Aso       bool   `json:"aso"`
}

// GetSensorUserInfo 请求神策 查询utmSource字段
func GetSensorUserInfo(params string, formType int) *ScResponse {
	var v = url.Values{}
	// 请求不同环境的通过project字段区分
	cfgEnv := srvconf.Get().Service.Env
	v.Set("project", "production")
	v.Set("token", libs.SensorToken)
	if cfgEnv == microservice.Test || cfgEnv == microservice.Dev {
		v.Set("project", "default")
	}
	v.Set("format", "json")
	if formType == libs.FormTypeByUID {
		v.Set("q", fmt.Sprintf("select second_id, $utm_source, $utm_medium, aso from users where second_id='%s'", params))
	} else {
		v.Set("q", fmt.Sprintf("select second_id, $utm_source, $utm_medium, aso from users where first_id='%s'", params))
	}
	address := libs.SensorQueryAPI + "?" + v.Encode()
	header := http.Header{}
	header.Add("Content-Type", "application/x-www-form-urlencoded")
	body, err := util.HTTPRequest(address, "GET", header, nil)
	if err != nil {
		logger.Warnf("神策查询注册渠道$utm_source地址:%s 错误:%s", address, err)
		return nil
	}
	var sensorRes ScResponse
	if err := json.Unmarshal(body, &sensorRes); err != nil {
		logger.Warnf("神策返回数据不能解析！%s", string(body))
		return nil
	}
	return &sensorRes
}

func GetSignupDatediff(uid int64) int {
	accItem := dbu.TbAccount.GetDBUserByID(uid)
	if accItem == nil {
		return 0
	}
	return int((util.FormatStartTime(time.Now().Unix()) - util.FormatStartTime(accItem.CreateTime)) / library.DayTime)
}

type ChargeResult struct {
	RetryCharge        bool
	RetryDays          int32
	RetryTimes         int32
	RetryTimesOfTheDay int32
	ContractID         string
}

func GetAlipayRetryInfoByOrderID(orderID string) *ChargeResult {
	res := &ChargeResult{
		RetryCharge:        false,
		RetryDays:          0,
		RetryTimes:         0,
		RetryTimesOfTheDay: 0,
		ContractID:         "",
	}
	charge := order.TbAlipayCharge.GetItemByOrderID(orderID)
	if charge == nil {
		return res
	}
	lastSuccess := order.TbAlipayCharge.GetLastSuccessCharge(charge.ContractCode, charge.ID)
	var minID int64 = 0
	if lastSuccess != nil {
		minID = lastSuccess.ID
	}
	chargeList := order.TbAlipayCharge.GetListByIDRange(charge.ContractCode, minID, charge.ID)
	if len(chargeList) == 0 {
		return res
	}
	var currentDateRetryTimes int64
	dayMap := make(map[string]int)
	for _, v := range chargeList {
		if charge.ChargeDate == v.ChargeDate {
			currentDateRetryTimes++
			continue
		}
		dayMap[v.ChargeDate] = 1
	}
	// 重试天数为失败天数 重试次数为
	res.RetryDays = int32(len(dayMap))
	res.RetryTimes = int32(len(chargeList) - 1)
	res.RetryCharge = res.RetryTimes > 0
	res.RetryTimesOfTheDay = int32(currentDateRetryTimes) - 1
	return res
}

func GetTiktokChargeRetryInfoByOrderID(orderID string) *ChargeResult {
	res := &ChargeResult{
		RetryCharge:        false,
		RetryDays:          0,
		RetryTimes:         0,
		RetryTimesOfTheDay: 0,
		ContractID:         "",
	}
	charge := order.TbTiktokCharge.GetItemByOrderID(orderID)
	// 没有扣款记录，则重试次数为0
	if charge == nil {
		return res
	}
	lastSuccess := order.TbTiktokCharge.GetLastSuccessCharge(charge.ContractCode, charge.ID)
	var minID int64 = 0
	if lastSuccess != nil {
		minID = lastSuccess.ID
	}
	chargeList := order.TbTiktokCharge.GetListByIDRange(charge.ContractCode, minID, charge.ID)
	if len(chargeList) == 0 {
		return res
	}
	var currentDateRetryTimes int64
	dayMap := make(map[string]int)
	for _, v := range chargeList {
		if charge.ChargeDate == v.ChargeDate {
			currentDateRetryTimes++
			continue
		}
		dayMap[v.ChargeDate] = 1
	}
	// 重试天数为失败天数 重试次数为
	res.RetryDays = int32(len(dayMap))
	res.RetryTimes = int32(len(chargeList) - 1)
	res.RetryCharge = res.RetryTimes > 0
	res.RetryTimesOfTheDay = int32(currentDateRetryTimes) - 1
	return res
}

func Starter(ms *microservice.Microservice) {
	if err := sc.Initial(srvconf.Get().Service.SensorsDataPath, false, srvconf.Get().Service.Name); err != nil {
		logger.Errorf("sensors data init failed. %s", err)
	}
}
