package sensor

import (
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	sc "gitlab.dailyyoga.com.cn/gokit/sensorsdata"
	"gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/databases/order"
	"gitlab.dailyyoga.com.cn/server/children/databases/product"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libproduct "gitlab.dailyyoga.com.cn/server/children/library/product"
	libsensor "gitlab.dailyyoga.com.cn/server/children/library/sensor"
)

const dayTime = 86400

type RefundDataIOS struct {
	ProductID                int     `json:"product_id"`
	ProductName              string  `json:"product_name"`
	IsSubscribe              bool    `json:"is_subscribe"`
	RefundMoney              float64 `json:"refund_money"`
	RefundRecentDiffDate     int     `json:"refund_recent_purchase_diffdate"`
	TheOrderChannel          string  `json:"the_order_channel"`
	Source                   string  `json:"source"`
	OrderID                  string  `json:"order_id"`
	OrderMoney               float64 `json:"order_money"`
	ConsecutiveOrderType     string  `json:"consecutive_order_type"`
	DiscountType             int     `json:"discount_type"`
	ExpiryDate               int     `json:"product_expiry_date"`
	ISTryConsecutiveFirstPay bool    `json:"is_try_consecutive_first_pay"`
	SignupDatediff           int     `json:"signup_datediff"`
	RefundPaymentInterval    int     `json:"refund_payment_interval"`
	IsGift                   bool    `json:"is_gift"`
	GiftName                 string  `json:"gift_name"`
}

// EventName Ios订单退款事件上报
// nolint
func (RefundDataIOS) EventName() string {
	if config.Get().Service.Env != microservice.Product && config.Get().Service.Env != microservice.Mirror {
		return "h2_refund_cs"
	}
	return "refund_cs"
}

func (RefundDataIOS) Prefix() string {
	return ""
}
func (o *RefundDataIOS) Track(uid string) {
	go func() {
		if uid == "" || uid == "0" {
			return
		}
		err := sc.Track(uid, *o, true)
		if err != nil {
			logger.Error("神策过期事件上报失败", err)
		}
	}()
}

func ReportIOSRefundOrder(webOrder *order.WebOrder) {
	if webOrder == nil {
		return
	}
	productID := webOrder.ProductID
	refundMoney := webOrder.OrderAmount
	productItem := product.TbWebProduct.GetItemByID(webOrder.ProductID)
	if productItem == nil {
		return
	}
	renewType := libsensor.RenewTypeNo
	if webOrder.IsRenew == library.Yes {
		renewType = libsensor.RenewTypeYes
	}

	event := RefundDataIOS{
		ProductID:                int(productID),
		ProductName:              productItem.Name,
		IsSubscribe:              productItem.IsSubscribe == library.Yes,
		RefundMoney:              refundMoney,
		TheOrderChannel:          libproduct.ProductVipTypeDesc[productItem.VipType],
		Source:                   webOrder.Source,
		OrderID:                  webOrder.OrderID,
		OrderMoney:               webOrder.OrderAmount,
		RefundRecentDiffDate:     diffNatureDays(time.Now().Unix(), webOrder.CreateTime),
		ConsecutiveOrderType:     renewType,
		DiscountType:             productItem.OfferType,
		ExpiryDate:               FormatDurationVal(productItem.DurationType, productItem.DurationValue),
		ISTryConsecutiveFirstPay: false,
		SignupDatediff:           GetSignupDatediff(webOrder.UID),
		RefundPaymentInterval:    GetRefundPaymentInterval(webOrder.OrderID, webOrder),
	}
	if webOrder.IsRenew == library.Yes && event.Source == "0" {
		event.Source = libsensor.RenewDefultOrderSource
	}
	giftInfo := GetOrderGiftInfo(webOrder.OrderID, productItem.ID)
	event.IsGift = giftInfo.IsGift
	event.GiftName = giftInfo.GiftName
	event.DiscountType, event.ISTryConsecutiveFirstPay = IosPayOfferType(webOrder.OrderID, webOrder.UID)
	event.Track(strconv.FormatInt(webOrder.UID, 10))
}

func diffNatureDays(t1, t2 int64) int {
	if t1 == t2 {
		return 0
	}
	if t1 > t2 {
		t1, t2 = t2, t1
	}

	diffDays := 0
	secDiff := t2 - t1
	if secDiff > dayTime {
		tmpDays := int(secDiff / dayTime)
		t1 += int64(tmpDays) * dayTime
		diffDays += tmpDays
	}

	st := time.Unix(t1, 0)
	et := time.Unix(t2, 0)
	dateFormatTpl := "20060102"
	if st.Format(dateFormatTpl) != et.Format(dateFormatTpl) {
		diffDays++
	}

	return diffDays
}
