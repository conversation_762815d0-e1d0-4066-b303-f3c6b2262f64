package sensor

import (
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	sc "gitlab.dailyyoga.com.cn/gokit/sensorsdata"
	"gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/databases/order"
	"gitlab.dailyyoga.com.cn/server/children/databases/product"
	dbu "gitlab.dailyyoga.com.cn/server/children/databases/user"
	dbyogacs "gitlab.dailyyoga.com.cn/server/children/databases/yogacs"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/pay"
	libproduct "gitlab.dailyyoga.com.cn/server/children/library/product"
	libsensor "gitlab.dailyyoga.com.cn/server/children/library/sensor"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type RefundOrder struct {
	CommData                 `json:"common_data"`
	OrderID                  string  `json:"order_id"`
	ProductID                int     `json:"product_id"`
	ProductName              string  `json:"product_name"`
	PurchaseType             int     `json:"purchase_type"`
	IsSubscribe              bool    `json:"is_subscribe"`
	Amount                   float64 `json:"order_money"`
	RefundMoney              float64 `json:"refund_money"`
	RefundType               int     `json:"refund_type"`
	RefundApplicant          string  `json:"refund_applicant"`
	RefundReason             string  `json:"refund_reason"`
	RetryTimes               int     `json:"retry_times"`
	RefundWay                string  `json:"refund_way"`
	Source                   int     `json:"source"`
	AppID                    string  `json:"app_id"`
	Account                  string  `json:"account"`
	RestOrderMoney           float64 `json:"rest_order_money"`
	SaveMoney                float64 `json:"save_money"`
	IsCount                  bool    `json:"is_count"`
	ConsecutiveOrderType     string  `json:"consecutive_order_type"`
	ExpiryDate               int     `json:"product_expiry_date"`
	DiscountType             int     `json:"discount_type"`
	ProductType              string  `json:"product_type"`
	TheOrderChannel          string  `json:"the_order_channel"`
	ISTryConsecutiveFirstPay bool    `json:"is_try_consecutive_first_pay"`
	IsHistory                bool    `json:"is_history"`
	SignupDatediff           int     `json:"signup_datediff"`
	IsTouSu                  bool    `json:"is_tousu"`
	RefundPaymentInterval    int     `json:"refund_payment_interval"`
	ThroughStage             int     `json:"through_stage"`
	RetrieveType             int     `json:"retrieve_type"` // 挽回类型 1接线挽回 2撤诉挽回 3外溢挽回
	RetryDays                int32   `json:"retry_days"`
	RetryCharge              bool    `json:"retry_charge"`
	RetryTimesOfTheDay       int32   `json:"retry_times_of_the_day"`
	PayTime                  string  `json:"pay_time"`
	RefundStatus             int
	IsGift                   bool   `json:"is_gift"`
	GiftName                 string `json:"gift_name"`
}

// EventName 订单退款事件上报
// nolint
func (o RefundOrder) EventName() string {
	if config.Get().Service.Env != microservice.Product && config.Get().Service.Env != microservice.Mirror {
		if o.RefundStatus == library.Yes {
			return "h2_submit_refund_cs"
		}
		return "h2_refund_cs"
	}
	if o.RefundStatus == library.Yes {
		return "submit_refund_cs"
	}
	return "refund_cs"
}

func (RefundOrder) Prefix() string {
	return ""
}

func (o *RefundOrder) Track(uid string) {
	go func() {
		if uid == "" || uid == "0" {
			return
		}
		err := sc.Track(uid, *o, true)
		if err != nil {
			logger.Error("神策过期事件上报失败", err)
		}
	}()
}

// nolint
func ReportRefundOrder(refundItem *order.Refund) {
	orderItem := order.TbWebOrder.GetItemByOrderID(refundItem.OrderID)
	if orderItem == nil {
		return
	}
	productItem := product.TbWebProduct.GetItemByID(orderItem.ProductID)
	if productItem == nil {
		return
	}
	// 神策上报 1 全部退款 2是部分退款 3全额挽回
	refundType := pay.RefundTypeEnum.Whole
	if refundItem.RefundAmount < orderItem.OrderAmount {
		refundType = pay.RefundTypeEnum.Part
	}
	if refundItem.IsRetrieve == 1 {
		refundType = pay.RefundTypeEnum.Retrieve
		refundItem.RefundAmount = 0
	}
	refundList := order.TbRefund.GetRefundListByOrderIDStatus(refundItem.OrderID,
		[]int{int(pay.RefundStatusEnum.Success), int(pay.RefundStatusEnum.Retrieve)})
	amount := orderItem.OrderAmount
	if refundList != nil {
		if len(refundList) > 1 {
			amount = 0
		}
	}
	refundApplicant := GetRefundApplicant(refundItem)
	refundWay := ""
	refundReason := ""
	switch refundItem.RefundType {
	case pay.RefundTypeGeneral:
		refundWay = "正常退费"
		refundReason = pay.ReasonTypeDesc[pay.ReasonTypeInt(refundItem.ReasonType)]
	case pay.RefundTypeSpecial:
		refundWay = "特殊退费"
		refundReason = pay.ReasonSpecialTypeDesc[pay.ReasonTypeInt(refundItem.ReasonType)]
	default:
	}
	renewType := libsensor.RenewTypeNo
	if orderItem.IsRenew == library.Yes {
		renewType = libsensor.RenewTypeYes
	}
	var (
		productID   = int(orderItem.ProductID)
		productName = productItem.Name
	)
	isHistory := false
	retryInfo := &ChargeResult{}
	if orderItem.PayType == pay.PayTypeAlipay {
		chargeItem := order.TbAlipayCharge.GetSuccessOrderByOrderID(orderItem.OrderID)
		if chargeItem != nil {
			contractItem := order.TbAlipayContract.GetItemByContractCode(chargeItem.ContractCode)
			if contractItem != nil && contractItem.IsHistoryDiscard == library.Yes {
				isHistory = true
			}
		}
		retryInfo = GetAlipayRetryInfoByOrderID(orderItem.OrderID)
	}
	productType := libproduct.ProductTypeDesc[productItem.ProductType]
	event := RefundOrder{
		CommData: InitCommDataByClient(&library.AppClient{
			Version: orderItem.Version,
			Channel: orderItem.Channel,
		}),
		OrderID:                  refundItem.OrderID,
		ProductID:                productID,
		ProductName:              productName,
		PurchaseType:             pay.PayTypeToSensor[orderItem.PayType],
		IsSubscribe:              productItem.IsSubscribe == library.Yes,
		Amount:                   amount,
		RefundMoney:              refundItem.RefundAmount,
		RefundType:               int(refundType),
		RefundApplicant:          refundApplicant,
		RefundReason:             refundReason,
		RefundWay:                refundWay,
		AppID:                    orderItem.MchID,
		Account:                  orderItem.MchID,
		IsCount:                  true,
		ConsecutiveOrderType:     renewType,
		ExpiryDate:               FormatDurationVal(productItem.DurationType, productItem.DurationValue),
		DiscountType:             productItem.OfferType,
		ProductType:              productType,
		ISTryConsecutiveFirstPay: false,
		IsHistory:                isHistory,
		SignupDatediff:           GetSignupDatediff(orderItem.UID),
		IsTouSu:                  IsTousu(refundItem),
		RefundPaymentInterval:    GetRefundPaymentInterval(orderItem.OrderID, orderItem),
		RefundStatus:             refundItem.RefundStatus,
		PayTime:                  time.Unix(orderItem.PayTime, 0).Format("2006-01-02 15:04:05"),
		TheOrderChannel:          "vip",
	}
	if retryInfo != nil {
		event.RetryDays = retryInfo.RetryDays
		event.RetryCharge = retryInfo.RetryCharge
		event.RetryTimes = int(retryInfo.RetryTimes)
		event.RetryTimesOfTheDay = retryInfo.RetryTimesOfTheDay
	}
	// 优惠订阅上报
	if orderItem.PayType == pay.PayTypeAlipay {
		event.DiscountType, event.ISTryConsecutiveFirstPay = AlipayOfferType(orderItem.OrderID, orderItem.UID)
	}
	if orderItem.PayType == pay.PayTypeApple {
		event.DiscountType, event.ISTryConsecutiveFirstPay = IosPayOfferType(orderItem.OrderID, orderItem.UID)
	}
	if refundItem.IsAssess == library.No {
		event.IsCount = false
	}
	// 上报source
	sourceInt, _ := strconv.Atoi(orderItem.Source)
	event.Source = sourceInt
	if event.Source == 0 {
		event.Source, _ = strconv.Atoi(libsensor.RenewDefultOrderSource)
	}
	// 上报剩余订单金额以及挽回金额
	var refundMoneyAll float64
	if len(refundList) > 0 {
		for _, v := range refundList {
			if v.RefundStatus == int(pay.RefundStatusEnum.Retrieve) || v.ID == refundItem.ID {
				continue
			}
			refundMoneyAll += v.RefundAmount
		}
	}
	event.RestOrderMoney = orderItem.OrderAmount - refundMoneyAll
	event.SaveMoney = event.RestOrderMoney - event.RefundMoney
	if orderItem.IsRenew == library.Yes {
		// 如果是订阅类产品platform和source需要处理
		userSubscribeInfo := dbu.TbWPSubU.GetItemByOrderID(refundItem.UID, refundItem.OrderID)
		if userSubscribeInfo != nil {
			originalOrderID := userSubscribeInfo.OriginalOrderID
			// 获取最初订单的设备类型
			originalOrderInfo := order.TbWebOrder.GetItemByOrderID(originalOrderID)
			if originalOrderInfo != nil {
				event.CommData = InitCommDataByClient(&library.AppClient{
					Version: originalOrderInfo.Version,
					Channel: originalOrderInfo.Channel,
				})
			}
		}
	}
	if refundItem.AdminName == libsensor.RobotAdminName {
		event.ThroughStage = libsensor.RobotAdminSatgeRobot
		if refundItem.StaffID > 0 {
			event.ThroughStage = libsensor.RobotAdminSatgeManual
		}
	}
	// 微信支付没有订阅
	if orderItem.PayType == pay.PayTypeWechat {
		event.IsSubscribe = false
	}
	giftInfo := GetOrderGiftInfo(orderItem.OrderID, productItem.ID)
	event.IsGift = giftInfo.IsGift
	event.GiftName = giftInfo.GiftName
	event.RetrieveType = refundItem.RetrieveType
	event.Track(strconv.FormatInt(orderItem.UID, 10))
}

func GetRefundApplicant(refundItem *order.Refund) string {
	refundApplicant := ""
	itemByRefundID := order.TbRefundUser.GetRefundUserItemByRefundID(refundItem.ID)
	if itemByRefundID != nil {
		if itemByRefundID.IsAutoRefund == library.Yes {
			refundApplicant = "用户-机审"
		} else {
			refundApplicant = "用户-客审-" + refundItem.AdminName
		}
	} else {
		if refundItem.TradeComplain == library.Yes {
			tradeComplain := dbyogacs.TbTradeComplain.GetItemByOrderID(refundItem.OrderID)
			if tradeComplain == nil {
				refundApplicant = refundItem.AdminName
			} else {
				if tradeComplain.PayType == pay.PayTypeAlipay {
					refundApplicant = "支付宝投诉"
				} else {
					refundApplicant = "微信投诉"
				}
			}
		} else {
			refundApplicant = refundItem.AdminName
		}
	}
	return refundApplicant
}

func GetRefundPaymentInterval(orderID string, orderItem *order.WebOrder) int {
	if orderItem == nil {
		orderItem = order.TbWebOrder.GetItemByOrderID(orderID)
	}
	if orderItem == nil {
		return 0
	}
	return int(util.FormatStartTime(time.Now().Unix())-util.FormatStartTime(orderItem.PayTime)) / library.DayTime
}

func IsTousu(orderRefund *order.Refund) bool {
	if orderRefund == nil {
		return false
	}
	if orderRefund.TradeComplain == library.Yes {
		return true
	}
	return len(order.TbRefund.GetTradeComplainListByOrderID(orderRefund.OrderID)) > 0
}
