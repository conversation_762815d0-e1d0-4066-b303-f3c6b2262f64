package sensor

import (
	"strconv"

	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	sc "gitlab.dailyyoga.com.cn/gokit/sensorsdata"
	"gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libuser "gitlab.dailyyoga.com.cn/server/children/library/user"
)

type SignUp struct {
	CommData          `json:"common_data"`
	SignUpMethod      string `json:"signUpMethod"`               // 注册方式
	IsChannelCallBack bool   `json:"$is_channel_callback_event"` // 深度回传所需字段
	ChannelDeviceInfo string `json:"$channel_device_info"`
	PackageName       string `json:"$app_id"`
	AdCustomDataType  string `json:"$ad_custom_data_type"`
	Channels          int    `json:"channels"`
}

func ReportSignUp(uid int64, loginType int, client *library.AppClient) {
	loginTypeDesc := libuser.LoginTypeToEnDesc(loginType)
	detail := &SignUp{
		CommData:          InitCommDataByClient(client),
		SignUpMethod:      loginTypeDesc,
		IsChannelCallBack: true,
		ChannelDeviceInfo: "toutiaochannel",
		PackageName:       GetAppPackageName(client),
		AdCustomDataType:  "app",
		Channels:          client.Channel,
	}
	detail.Track(strconv.FormatInt(uid, 10))
}

// EventName 注册事件上报
func (SignUp) EventName() string {
	if config.Get().Service.Env != microservice.Product && config.Get().Service.Env != microservice.Mirror {
		return "h2_signUp_cs"
	}
	return "signUp_cs"
}

func (SignUp) Prefix() string {
	return ""
}
func (o *SignUp) Track(uid string) {
	go func() {
		if uid == "" || uid == "0" {
			return
		}
		err := sc.Track(uid, *o, true)
		if err != nil {
			logger.Error("神策过期事件上报失败", err)
		}
	}()
}
