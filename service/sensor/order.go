package sensor

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	sc "gitlab.dailyyoga.com.cn/gokit/sensorsdata"
	"gitlab.dailyyoga.com.cn/server/children/config"
	dbc "gitlab.dailyyoga.com.cn/server/children/databases/client"
	"gitlab.dailyyoga.com.cn/server/children/databases/iap"
	dbo "gitlab.dailyyoga.com.cn/server/children/databases/order"
	dbp "gitlab.dailyyoga.com.cn/server/children/databases/product"
	dbu "gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library"
	lcli "gitlab.dailyyoga.com.cn/server/children/library/client"
	"gitlab.dailyyoga.com.cn/server/children/library/pay"
	libproduct "gitlab.dailyyoga.com.cn/server/children/library/product"
	libsensor "gitlab.dailyyoga.com.cn/server/children/library/sensor"
	serpay "gitlab.dailyyoga.com.cn/server/children/service/pay"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type SubmitOrder struct {
	CommData         `json:"common_data"`
	OrderID          string  `json:"order_id"`
	ProductID        int     `json:"product_id"`
	ProductName      string  `json:"product_name"`
	PurchaseType     int     `json:"purchase_type"`
	Source           string  `json:"source"`
	SourceID         string  `json:"source_id"`
	SourceRefer      string  `json:"source_refer"`
	SourceReferID    string  `json:"source_refer_id"`
	DiscountType     int     `json:"discount_type"`
	ProductType      string  `json:"product_type"`
	ChallengeID      int     `json:"challenge_id"`
	ChallengeName    string  `json:"challenge_name"`
	ExpiryDate       int     `json:"product_expiry_date"`
	Company          string  `json:"company"`
	Price            float64 `json:"price"`
	IsPackage        int     `json:"is_package"`
	TotalProductName string  `json:"total_product_name"`
	ProductCategory  string  `json:"product_category"`
	SignupDatediff   int     `json:"signup_datediff"`
	PaidPlanPage     int     `json:"paid_plan_page"`
	DealUpdateTimes  int     `json:"deal_update_times"`
	IsVipBefPur      bool    `json:"is_vip_before_purchase"`
	HisPurTimes      int     `json:"history_purchase_times"`
	IsChannelCall    bool    `json:"$is_channel_callback_event"`
	PackageName      string  `json:"$app_id"`
	AdCustomDataType string  `json:"$ad_custom_data_type"`
	TheOrderChannel  string  `json:"the_order_channel"`
	IsGift           bool    `json:"is_gift"`
	GiftName         string  `json:"gift_name"`
}

// ReportSubmitOrder 上报提交订单事件
func ReportSubmitOrder(order *dbo.WebOrder, product *dbp.WebProduct, client *library.AppClient) {
	productType := ""
	productID := int(order.ProductID)
	productName := product.Name
	productCategory := libproduct.ProductTypeDesc[product.ProductType]
	isPackage := 0
	productType = libproduct.ProductTypeDesc[product.ProductType]
	event := &SubmitOrder{
		OrderID:          order.OrderID,
		ProductID:        productID,
		ProductName:      productName,
		PurchaseType:     pay.PayTypeToSensor[order.PayType],
		Source:           order.Source,
		SourceID:         order.SourceID,
		SourceRefer:      order.SourceRefer,
		SourceReferID:    order.SourceReferID,
		CommData:         InitCommDataByClient(client),
		DiscountType:     product.OfferType,
		ProductType:      productType,
		ExpiryDate:       FormatDurationVal(product.DurationType, product.DurationValue),
		Company:          pay.CompanyNameMap[serpay.MerchantEmailToCompany(pay.AppIDToAccountMap[order.MchID])],
		Price:            product.Price,
		IsPackage:        isPackage,
		TotalProductName: product.Name,
		ProductCategory:  productCategory,
		PaidPlanPage:     serpay.GetPaymentPageIDCache(order.OrderID),
		DealUpdateTimes:  serpay.GetDealUpdateTimes(order.OrderID),
		IsVipBefPur:      serpay.GetUserVipStatus(order.UID),
		HisPurTimes:      serpay.GetUserOldBuyNum(order.UID),
		TheOrderChannel:  "vip",
	}
	event.SignupDatediff = GetSignupDatediff(order.UID)
	event.PackageName = GetAppPackageName(client)
	event.AdCustomDataType = "app"
	event.IsChannelCall = len(dbo.TbWebOrder.GetAllListByUID(order.UID)) == library.Yes
	giftInfo := GetOrderGiftInfo(order.OrderID, order.ProductID)
	event.IsGift = giftInfo.IsGift
	event.GiftName = giftInfo.GiftName
	event.Track(strconv.FormatInt(order.UID, 10))
}

// EventName 订单提交事件上报
func (SubmitOrder) EventName() string {
	if config.Get().Service.Env != microservice.Product && config.Get().Service.Env != microservice.Mirror {
		return "h2_submit_order_cs"
	}
	return "submit_order_cs"
}

func (SubmitOrder) Prefix() string {
	return ""
}
func (o *SubmitOrder) Track(uid string) {
	go func() {
		if uid == "" || uid == "0" {
			return
		}
		err := sc.Track(uid, *o, true)
		if err != nil {
			logger.Error("神策过期事件上报失败", err)
		}
	}()
}

type PurchaseOrder struct {
	CommData           `json:"common_data"`
	OrderID            string  `json:"order_id"`
	ProductID          int     `json:"product_id"`
	ProductName        string  `json:"product_name"`
	PurchaseType       int     `json:"purchase_type"`
	Source             string  `json:"source"`
	SourceID           string  `json:"source_id"`
	SourceRefer        string  `json:"source_refer"`
	SourceReferID      string  `json:"source_refer_id"`
	IsSubscribe        bool    `json:"is_subscribe"`
	Amount             float64 `json:"actually_pay_money"`
	ConsecutiveType    string  `json:"consecutive_order_type"`
	DiscountType       int     `json:"discount_type"`
	ProductType        string  `json:"product_type"`
	IsTryCoFirst       bool    `json:"is_try_consecutive_first_pay"`
	AppID              string  `json:"app_id"`
	Account            string  `json:"account"`
	ChallengeID        int     `json:"challenge_id"`
	ChallengeName      string  `json:"challenge_name"`
	ExpiryDate         int     `json:"product_expiry_date"`
	Company            string  `json:"company"`
	Price              float64 `json:"price"`
	IsPackage          int     `json:"is_package"`
	TotalProductName   string  `json:"total_product_name"`
	ProductCategory    string  `json:"product_category"`
	SignupDatediff     int     `json:"signup_datediff"`
	PaidPlanPage       int     `json:"paid_plan_page"`
	DealUpdateTimes    int     `json:"deal_update_times"`
	IsVipBefPur        bool    `json:"is_vip_before_purchase"`
	HisPurTimes        int     `json:"history_purchase_times"`
	RetryDays          int32   `json:"retry_days"`
	RetryTimes         int32   `json:"retry_times"`
	RetryCharge        bool    `json:"retry_charge"`
	RetryTimesOfTheDay int32   `json:"retry_times_of_the_day"`
	TheOrderChannel    string  `json:"the_order_channel"`
	IsGift             bool    `json:"is_gift"`
	GiftName           string  `json:"gift_name"`
}

func GetOriginalOrder(order *dbo.WebOrder) *dbo.WebOrder {
	sub := dbu.TbWPSubU.GetItemByOrderID(order.UID, order.OrderID)
	if sub == nil {
		return nil
	}
	return dbo.TbWebOrder.GetItemByOrderID(sub.OriginalOrderID)
}

// ReportPurchaseOrder 订单完成事件上报
// nolint
func ReportPurchaseOrder(order *dbo.WebOrder, product *dbp.WebProduct, client *library.AppClient) {
	if order.IsRenew == library.Yes {
		originalOrder := GetOriginalOrder(order)
		if originalOrder != nil {
			client.Channel = originalOrder.Channel
		}
	}
	productType := ""
	productID := int(order.ProductID)
	productName := product.Name
	productCategory := libproduct.ProductTypeDesc[product.ProductType]
	isPackage := 0
	productType = libproduct.ProductTypeDesc[product.ProductType]
	event := PurchaseOrder{
		OrderID:            order.OrderID,
		ProductID:          productID,
		ProductName:        productName,
		PurchaseType:       pay.PayTypeToSensor[order.PayType],
		Source:             order.Source,
		SourceID:           order.SourceID,
		SourceRefer:        order.SourceRefer,
		SourceReferID:      order.SourceReferID,
		IsSubscribe:        product.IsSubscribe == library.Yes,
		CommData:           InitCommDataByClient(client),
		ConsecutiveType:    libsensor.ConsecutiveTypeBuy,
		DiscountType:       libproduct.ConstOfferTypeNo,
		ProductType:        productType,
		IsTryCoFirst:       false,
		AppID:              order.MchID,
		Account:            order.MchID,
		ExpiryDate:         FormatDurationVal(product.DurationType, product.DurationValue),
		Company:            pay.CompanyNameMap[serpay.MerchantEmailToCompany(pay.AppIDToAccountMap[order.MchID])],
		Price:              product.Price,
		IsPackage:          isPackage,
		TotalProductName:   product.Name,
		ProductCategory:    productCategory,
		PaidPlanPage:       serpay.GetPaymentPageIDCache(order.OrderID),
		DealUpdateTimes:    serpay.GetDealUpdateTimes(order.OrderID),
		IsVipBefPur:        serpay.GetUserVipStatus(order.UID),
		HisPurTimes:        serpay.GetUserOldBuyNum(order.UID),
		RetryCharge:        false,
		RetryDays:          0,
		RetryTimesOfTheDay: 0,
		TheOrderChannel:    "vip",
	}
	if order.PayType == pay.PayTypeWechat {
		event.IsSubscribe = false
	}
	// 续订时 版本号为默认值 安卓续订订单来源以及续订类型
	if order.IsRenew == library.Yes {
		event.Source = libsensor.RenewDefultOrderSource
		event.ConsecutiveType = libsensor.ConsecutiveTypeRenew
	}
	// 优惠订阅上报
	if order.PayType == pay.PayTypeAlipay {
		retryInfo := GetAlipayRetryInfoByOrderID(order.OrderID)
		if retryInfo != nil {
			event.RetryDays = retryInfo.RetryDays
			event.RetryCharge = retryInfo.RetryCharge
			event.RetryTimes = retryInfo.RetryTimes
			event.RetryTimesOfTheDay = retryInfo.RetryTimesOfTheDay
		}
		event.DiscountType, event.IsTryCoFirst = AlipayOfferType(order.OrderID, order.UID)
	}
	if order.PayType == pay.PayTypeApple {
		event.DiscountType, event.IsTryCoFirst = IosPayOfferType(order.OrderID, order.UID)
	}
	giftInfo := GetOrderGiftInfo(order.OrderID, order.ProductID)
	event.IsGift = giftInfo.IsGift
	event.GiftName = giftInfo.GiftName
	event.Amount = order.OrderAmount
	event.SignupDatediff = GetSignupDatediff(order.UID)
	event.Track(strconv.FormatInt(order.UID, 10))
	otherOrder := dbo.TbWebOrder.GetOtherOrderByUID(order.UID, order.OrderID)
	// 非首次，直接返回
	if otherOrder != nil {
		return
	}
	// 巨量抖音小程序付费回传
	// safelygo.GoSafelyByTraceID(func() {
	// 	app.SrvTiktok.FirstPurchase(order.UID, order.OrderAmount)
	// 	UploadBuyReturn(order.UID, order.OrderAmount)
	// })
	// 上报首次付费事件
	ReportTotalCashFirst(order, client)
	// 上报首次付费事件市场部专用
	ReportTotalCashFirstMarket(order, client)
	// 用户首次完成现金交易(市场部专用&&使用神策匿名ID上报)
	safelygo.GoSafelyByTraceID(func() {
		data := order
		ReportFirstPurchaseByUseridFt(data, client)
	})
	// 上报首次付费投放归因
	// activation := &app.Activation{
	// 	UID:        order.UID,
	// 	Amount:     order.OrderAmount,
	// 	ActionType: int64(lcli.OppoOcpxTypeEnum.Purchase),
	// 	ChannelStr: lcli.ChannelOPPO, // 购买数据进入延迟队列 需要渠道参数
	// }
	// safelygo.GoSafelyByTraceID(func() {
	// 	activation.FirstPurchase()
	// })
}

func UploadBuyReturn(uid int64, amount float64) {
	// 付费上报神策
	account := dbu.TbAccount.GetUserByID(uid)
	if account != nil {
		days := float64(time.Now().Unix()-account.CreateTime) / library.DayTime
		event := &BuyReturn{
			ActuallyPayMoney: amount,
			RegistDiff:       int(days),
		}
		event.Track(strconv.Itoa(int(account.ID)))
	}
}

type BuyReturn struct {
	ActuallyPayMoney float64 `json:"actually_pay_money"`
	RegistDiff       int     `json:"regist_diff"`
	CommData         `json:"common_data"`
}

// EventName 事件名称
func (BuyReturn) EventName() string {
	if config.Get().Service.Env != microservice.Product && config.Get().Service.Env != microservice.Mirror {
		return "h2_douyin_buy_return_cs"
	}
	return "douyin_buy_return_cs"
}

func (BuyReturn) Prefix() string {
	return ""
}

func (o *BuyReturn) Track(uid string) {
	safelygo.GoSafelyByTraceID(func() {
		if uid == "" || uid == "0" {
			return
		}
		err := sc.Track(uid, *o, true)
		if err != nil {
			logger.Error("神策过期事件上报失败", err)
		}
	})
}

func AlipayOfferType(orderID string, uid int64) (int, bool) {
	offerType := libproduct.ConstOfferTypeNo
	isTryCoFirst := false
	sub := dbu.TbWPSubU.GetItemByOrderID(uid, orderID)
	if sub != nil && sub.ID > 0 {
		offerType = sub.OfferType
	} else {
		cont := dbo.TbAlipayContract.GetItemByOrderID(orderID)
		if cont != nil && cont.ID > 0 {
			offerType = cont.OfferType
		}
	}
	if library.IntInArray(offerType,
		[]int{libproduct.ConstOfferTypeTrial, libproduct.ConstOfferTypeTrialFirstBuy}) {
		charge := dbo.TbAlipayCharge.GetSuccessOrderByOrderID(orderID)
		if charge != nil {
			otherCharge := dbo.TbAlipayCharge.GetOtherSuccessOrder(orderID, charge.ContractCode)
			if otherCharge == nil {
				isTryCoFirst = true
			}
		}
	}
	return offerType, isTryCoFirst
}

func IosPayOfferType(orderID string, uid int64) (int, bool) {
	offerType := libproduct.ConstOfferTypeNo
	isTryCoFirst := false
	iosOrder := iap.TbIOSOrder.GetItemByOrderID(orderID)
	if iosOrder != nil {
		iosOrderOriginal := iap.TbIOSOrder.GetItemByTran(iosOrder.OriginalTranID, iosOrder.OriginalTranID)
		if iosOrderOriginal != nil {
			sub := dbu.TbWPSubU.GetItemByOriginalOrderID(uid, iosOrderOriginal.OrderID)
			if sub != nil {
				offerType = sub.OfferType
			}
		}
	}
	if iosOrder != nil && offerType > libproduct.ConstOfferTypeNo {
		iosOrderArr := iap.TbIOSOrder.GetNoOfferByOrig(iosOrder.OriginalTranID)
		if len(iosOrderArr) == 1 {
			isTryCoFirst = true
		}
	}
	return offerType, isTryCoFirst
}

// EventName 订单完成支付事件上报
func (PurchaseOrder) EventName() string {
	if config.Get().Service.Env != microservice.Product && config.Get().Service.Env != microservice.Mirror {
		return "h2_purchase_order_cs"
	}
	return "purchase_order_cs"
}

func (PurchaseOrder) Prefix() string {
	return ""
}
func (o *PurchaseOrder) Track(uid string) {
	go func() {
		if uid == "" || uid == "0" {
			return
		}
		err := sc.Track(uid, *o, true)
		if err != nil {
			logger.Error("神策过期事件上报失败", err)
		}
	}()
}

type TotalCashFirstPurchase struct {
	CommData          `json:"common_data"`
	ActuallyPayMoney  float64 `json:"actually_pay_money"`
	RegistDiff        int     `json:"regist_diff"`
	IsChannelCallBack bool    `json:"$is_channel_callback_event"` // 深度回传所需字段
	ChannelDeviceInfo string  `json:"$channel_device_info"`
	AppID             string  `json:"$app_id"`
	ADCustomDataType  string  `json:"$ad_custom_data_type"`
	OrderID           string  `json:"order_id"`
}

// EventName 订单完成支付事件上报
func (TotalCashFirstPurchase) EventName() string {
	if config.Get().Service.Env != microservice.Product && config.Get().Service.Env != microservice.Mirror {
		return "h2_total_cash_first_purchase_is_ture_cs"
	}
	return "total_cash_first_purchase_is_ture_cs"
}

func (TotalCashFirstPurchase) Prefix() string {
	return ""
}
func (o *TotalCashFirstPurchase) Track(uid string) {
	go func() {
		if uid == "" || uid == "0" {
			return
		}
		err := sc.Track(uid, *o, true)
		if err != nil {
			logger.Error("神策过期事件上报失败", err)
		}
	}()
}

// ReportTotalCashFirst 用户首次完成现金交易
func ReportTotalCashFirst(order *dbo.WebOrder, client *library.AppClient) {
	account := dbu.TbAccount.GetUserByID(order.UID)
	if account == nil {
		return
	}
	days := float64(time.Now().Unix()-account.CreateTime) / util.SecondsPerDay
	properties := &TotalCashFirstPurchase{
		OrderID:           order.OrderID,
		CommData:          InitCommDataByClient(client),
		ActuallyPayMoney:  order.OrderAmount,
		RegistDiff:        int(days),
		IsChannelCallBack: true,
		ChannelDeviceInfo: "toutiaochannel",
		ADCustomDataType:  "app",
		AppID:             GetAppPackageName(client),
	}
	properties.Track(strconv.FormatInt(order.UID, 10))
}

type TotalCashFirstPurchaseMarket struct {
	CommData          `json:"common_data"`
	ActuallyPayMoney  float64   `json:"actually_pay_money"`
	OrderID           string    `json:"order_id"`
	RegistDiff        int       `json:"regist_diff"`
	IsChannelCallBack bool      `json:"$is_channel_callback_event"` // 深度回传所需字段
	ChannelDeviceInfo string    `json:"$channel_device_info"`
	AppID             string    `json:"$app_id"`
	ADCustomDataType  string    `json:"$ad_custom_data_type"`
	Time              time.Time `json:"$time"`
}

// EventName 订单完成支付事件上报
func (TotalCashFirstPurchaseMarket) EventName() string {
	if config.Get().Service.Env != microservice.Product && config.Get().Service.Env != microservice.Mirror {
		return "h2_key_cs"
	}
	return "key_cs"
}

func (TotalCashFirstPurchaseMarket) Prefix() string {
	return ""
}
func (o *TotalCashFirstPurchaseMarket) Track(uid string, isLoginID bool) {
	go func() {
		if uid == "" || uid == "0" {
			return
		}
		err := sc.Track(uid, *o, isLoginID)
		if err != nil {
			logger.Error("神策过期事件上报失败", err)
		}
	}()
}

// ReportTotalCashFirstMarket 用户首次完成现金交易(市场部专用)
func ReportTotalCashFirstMarket(order *dbo.WebOrder, client *library.AppClient) {
	account := dbu.TbAccount.GetUserByID(order.UID)
	if account == nil {
		return
	}

	isLoginID := true
	sUID := strconv.FormatInt(order.UID, 10)
	adUIDChannel := dbc.TbAdUIDChannel.GetItemByUID(account.ID)
	if adUIDChannel != nil {
		sensorsData := dbc.TbAdSensorsData.GetItemByDeviceID(adUIDChannel.DeviceID)
		if sensorsData != nil {
			sUID = sensorsData.AnonymousID
			isLoginID = false
		}
	}
	days := float64(time.Now().Unix()-account.CreateTime) / util.SecondsPerDay
	properties := &TotalCashFirstPurchaseMarket{
		CommData:          InitCommDataByClient(client),
		OrderID:           order.OrderID,
		ActuallyPayMoney:  order.OrderAmount,
		RegistDiff:        int(days),
		IsChannelCallBack: true,
		ChannelDeviceInfo: "toutiaochannel",
		ADCustomDataType:  "app",
		AppID:             GetAppPackageName(client),
		Time:              time.Now().Add(libsensor.LaterMill * time.Millisecond),
	}
	properties.Track(sUID, isLoginID)
}

type FirstPurchaseByUseridFt struct {
	CommData          `json:"common_data"`
	ActuallyPayMoney  float64   `json:"actually_pay_money"`
	OrderID           string    `json:"order_id"`
	RegistDiff        int       `json:"regist_diff"`
	IsChannelCallBack bool      `json:"$is_channel_callback_event"` // 深度回传所需字段
	ChannelDeviceInfo string    `json:"$channel_device_info"`
	AppID             string    `json:"$app_id"`
	ADCustomDataType  string    `json:"$ad_custom_data_type"`
	Time              time.Time `json:"$time"`
}

// EventName 订单完成支付事件上报
func (FirstPurchaseByUseridFt) EventName() string {
	if config.Get().Service.Env != microservice.Product && config.Get().Service.Env != microservice.Mirror {
		return "h2_first_purchase_by_userid_cs"
	}
	return "first_purchase_by_userid_cs"
}

func (FirstPurchaseByUseridFt) Prefix() string {
	return ""
}
func (o *FirstPurchaseByUseridFt) Track(uid string, isLoginID bool) {
	go func() {
		if uid == "" || uid == "0" {
			return
		}
		err := sc.Track(uid, *o, isLoginID)
		if err != nil {
			logger.Error("神策过期事件上报失败", err)
		}
	}()
}

// ReportFirstPurchaseByUseridFt 用户首次完成现金交易(市场部专用&&使用神策匿名ID上报)
func ReportFirstPurchaseByUseridFt(order *dbo.WebOrder, client *library.AppClient) {
	account := dbu.TbAccount.GetUserByID(order.UID)
	if account == nil {
		return
	}
	isLoginID := true
	sUID := strconv.FormatInt(order.UID, 10)
	adUIDChannel := dbc.TbAdUIDChannel.GetItemByUID(account.ID)
	if adUIDChannel != nil {
		sensorsData := dbc.TbAdSensorsData.GetItemByDeviceID(adUIDChannel.DeviceID)
		if sensorsData != nil {
			sUID = sensorsData.AnonymousID
			isLoginID = false
		}
	}
	days := float64(time.Now().Unix()-account.CreateTime) / util.SecondsPerDay
	properties := &FirstPurchaseByUseridFt{
		CommData:          InitCommDataByClient(client),
		ActuallyPayMoney:  order.OrderAmount,
		OrderID:           order.OrderID,
		RegistDiff:        int(days),
		IsChannelCallBack: true,
		ChannelDeviceInfo: "toutiaochannel",
		ADCustomDataType:  "app",
		AppID:             GetAppPackageName(client),
		Time:              time.Now().Add(2 * libsensor.LaterMill * time.Millisecond),
	}
	properties.Track(sUID, isLoginID)
}

type UnSubscribe struct {
	CommData           `json:"common_data"`
	PurchaseType       int    `json:"purchase_type"`
	ProductID          int    `json:"product_id"`
	ProductName        string `json:"product_name"`
	SubscribeTimes     int    `json:"subscribe_times"`
	UnsubscribeType    string `json:"unsubscribe_type"`
	OrderID            string `json:"order_id"`
	ContractID         string `json:"contract_id"`
	RetryDays          int32  `json:"retry_days"`
	RetryTimes         int32  `json:"retry_times"`
	RetryCharge        bool   `json:"retry_charge"`
	RetryTimesOfTheDay int32  `json:"retry_times_of_the_day"`
}

// EventName 订阅产品取消订阅上报
func (UnSubscribe) EventName() string {
	if config.Get().Service.Env != microservice.Product && config.Get().Service.Env != microservice.Mirror {
		return "h2_unsubscribe_cs"
	}
	return "unsubscribe_cs"
}

func (UnSubscribe) Prefix() string {
	return ""
}
func (u *UnSubscribe) Track(uid string) {
	go func() {
		if uid == "" || uid == "0" {
			return
		}
		err := sc.Track(uid, *u, true)
		if err != nil {
			logger.Error("神策过期事件上报失败", err)
		}
	}()
}

// 上报取消订阅
func ReportUnSubscribe(order *dbo.WebOrder, contractCode string, unsubscribeType pay.UnsubscribeTypeInt) {
	product := dbp.TbWebProduct.GetItemByID(order.ProductID)
	if product == nil {
		return
	}
	var subscribeTimes int
	var commData CommData
	var contractOrderID string
	if order.PayType == pay.PayTypeTikTok {
		chargeList := dbo.TbTiktokCharge.GetValidChargeByCode(contractCode)
		contract := dbo.TbTiktokContract.GetItemByContractCode(contractCode)
		if contract == nil {
			logger.Warn("神策上报取消订阅，查询签约协议失败", order.OrderID, contractCode)
			return
		}

		if len(chargeList) > 0 {
			for _, v := range chargeList {
				if v.OrderID != contract.OrderID {
					subscribeTimes++
				}
			}
		}
		commData = InitCommDataByClient(&library.AppClient{
			Version:  order.Version,
			PlanForm: library.PlanFormMiNiAPP,
			Channel:  order.Channel,
		})
		contractOrderID = contract.OrderID
	} else {
		chargeList := dbo.TbAlipayCharge.GetValidChargeByCode(contractCode)
		contract := dbo.TbAlipayContract.GetItemByContractCode(contractCode)
		if contract == nil {
			logger.Warn("神策上报取消订阅，查询签约协议失败", order.OrderID, contractCode)
			return
		}
		var subscribeTimes int
		if len(chargeList) > 0 {
			for _, v := range chargeList {
				if v.OrderID != contract.OrderID {
					subscribeTimes++
				}
			}
		}
		contractOrderID = contract.OrderID
	}

	webOrder := dbo.TbWebOrder.GetItemByOrderID(contractOrderID)
	if webOrder != nil && webOrder.OrderStatus == pay.OrderStatus.Paid {
		subscribeTimes++
	}
	e := &UnSubscribe{
		ProductID:       int(product.ID),
		PurchaseType:    pay.PayTypeToSensor[order.PayType],
		ProductName:     product.Name,
		SubscribeTimes:  subscribeTimes,
		CommData:        commData,
		UnsubscribeType: pay.UnsubscribeTypeDesc[unsubscribeType],
		OrderID:         webOrder.OrderID,
		ContractID:      contractCode,
	}
	e.Track(strconv.FormatInt(order.UID, 10))
}

// 上报IOS内购取消订阅
func ReportIOSUnSubscribe(order *dbo.WebOrder, subscribeTimes int) {
	product := dbp.TbWebProduct.GetItemByID(order.ProductID)
	if product == nil {
		return
	}
	e := &UnSubscribe{
		ProductID:      int(product.ID),
		PurchaseType:   pay.PayTypeToSensor[order.PayType],
		ProductName:    product.Name,
		SubscribeTimes: subscribeTimes,
		CommData: InitCommDataByClient(&library.AppClient{
			OsType: int(lcli.DeviceTypeEnum.IOS),
		}),
		UnsubscribeType: pay.UnsubscribeTypeDesc[pay.UnsubscribeTypeEnum.User],
		OrderID:         order.OrderID,
	}
	e.Track(strconv.FormatInt(order.UID, 10))
}

type Subscribe struct {
	CommData           `json:"common_data"`
	PurchaseType       int    `json:"purchase_type"`
	ProductID          int64  `json:"product_id"`
	ProductName        string `json:"product_name"`
	SubscribeType      int    `json:"subscribe_type"`
	OrderID            string `json:"order_id"`
	ContractID         string `json:"contract_id"`
	RetryDays          int32  `json:"retry_days"`
	RetryTimes         int32  `json:"retry_times"`
	RetryCharge        bool   `json:"retry_charge"`
	RetryTimesOfTheDay int32  `json:"retry_times_of_the_day"`
}

// EventName 订阅产品订阅事件上报
func (Subscribe) EventName() string {
	if config.Get().Service.Env != microservice.Product && config.Get().Service.Env != microservice.Mirror {
		return "h2_subscribe_cs"
	}
	return "subscribe_cs"
}
func (Subscribe) Prefix() string {
	return ""
}
func (u *Subscribe) Track(uid string) {
	go func() {
		if uid == "" || uid == "0" {
			return
		}
		err := sc.Track(uid, *u, true)
		if err != nil {
			logger.Error("神策过期事件上报失败", err)
		}
	}()
}

// 上报订阅事件
func ReportSubscribe(order *dbo.WebOrder, subMode pay.SubscribeModeInt, contractCode string) {
	product := dbp.TbWebProduct.GetItemByID(order.ProductID)
	if product == nil {
		return
	}

	var commData CommData
	retryInfo := &ChargeResult{}
	if order.PayType == pay.PayTypeTikTok {
		commData = InitCommDataByClient(&library.AppClient{
			Version:  order.Version,
			PlanForm: library.PlanFormMiNiAPP,
			Channel:  order.Channel,
		})
		retryInfo = GetTiktokChargeRetryInfoByOrderID(order.OrderID)
	} else {
		commData = InitCommDataByClient(&library.AppClient{
			Version:  order.Version,
			PlanForm: library.PlanFormApp,
		})
		if order.PayType == pay.PayTypeAlipay {
			retryInfo = GetAlipayRetryInfoByOrderID(order.OrderID)
		}
	}
	e := &Subscribe{
		PurchaseType:  pay.PayTypeToSensor[order.PayType],
		ProductName:   product.Name,
		ProductID:     product.ID,
		SubscribeType: int(subMode),
		CommData:      commData,
		OrderID:       order.OrderID,
		ContractID:    contractCode,
	}
	if retryInfo != nil {
		e.RetryDays = retryInfo.RetryDays
		e.RetryCharge = retryInfo.RetryCharge
		e.RetryTimes = retryInfo.RetryTimes
		e.RetryTimesOfTheDay = retryInfo.RetryTimesOfTheDay
	}
	e.Track(strconv.FormatInt(order.UID, 10))
}

type ContractChargeSuccessFail struct {
	IsSucceed           bool    `json:"is_succeed"`
	FailReason          string  `json:"fail_reason"`
	RetryDays           int32   `json:"retry_days"`
	RetryCharge         bool    `json:"retry_charge"`
	RetryTimes          int32   `json:"retry_times"`
	SubscribeTimes      int32   `json:"subscribe_times"`
	RetryTimesOfTheDay  int32   `json:"retry_times_of_the_day"`
	FirstSubscribeTimes string  `json:"first_subscribe_time"`
	OrderID             string  `json:"order_id"`
	ProductID           int     `json:"product_id"`
	ProductName         string  `json:"product_name"`
	PurchaseType        int     `json:"purchase_type"`
	Amount              float64 `json:"actually_pay_money"`
	ConsecutiveType     string  `json:"consecutive_order_type"`
	ContractID          string  `json:"contract_id"`
	IsCoupon            bool    `json:"is_coupon"`
	APPID               string  `json:"app_id"`
	CouponMoney         float64 `json:"coupon_money"`
	IsHistory           bool    `json:"is_history"`
	ProductExpiryDate   int     `json:"product_expiry_date"`
}

type ChargeResultItem struct {
	IsSuccess          bool
	UID                int64
	OrderID            string
	ContractID         string // 避免支付宝签约回调过来的晚，协议id用我们自己生成的code
	Price              float64
	FirstSubscribeTime int64
	ProductID          int64
	PayType            int
	IsRenew            bool
	FailReason         string
	RetryDays          int32
	RetryTimes         int
	ChargeDate         string
	SubscribeTimes     int
	RetryTimesOfTheDay int
	IsCoupon           bool
	CouponMoney        float64
	APPID              string
	IsHistoryDiscard   bool
	OrderMoney         float64
	ProductName        string
}

// 订阅扣款结果上报
func ReportChargeSuccessFail(item *ChargeResultItem) {
	product := dbp.TbWebProduct.GetItemByID(item.ProductID)
	if product == nil {
		logger.Error("订阅扣款结果上报,商品为空", item.ProductID)
		return
	}
	// 避免支付宝签约回调过来的晚，协议id用我们自己生成的code
	event := &ContractChargeSuccessFail{
		OrderID:             item.OrderID,
		ContractID:          item.ContractID,
		FailReason:          item.FailReason,
		RetryDays:           item.RetryDays,
		RetryTimes:          int32(item.RetryTimes),
		RetryCharge:         item.RetryTimes > 0,
		SubscribeTimes:      int32(item.SubscribeTimes),
		ConsecutiveType:     libsensor.ConsecutiveTypeBuy,
		FirstSubscribeTimes: time.Unix(item.FirstSubscribeTime, 0).Format("2006-01-02 15:04:05"),
		ProductID:           int(item.ProductID),
		Amount:              item.Price,
		IsSucceed:           item.IsSuccess,
		RetryTimesOfTheDay:  int32(item.RetryTimesOfTheDay),
		IsCoupon:            item.IsCoupon,
		APPID:               item.APPID,
		CouponMoney:         item.CouponMoney,
		IsHistory:           item.IsHistoryDiscard,
		ProductExpiryDate:   FormatDurationVal(product.DurationType, product.DurationValue),
	}
	if item.IsRenew {
		event.ConsecutiveType = libsensor.ConsecutiveTypeRenew
	}
	event.ProductName = product.Name
	event.PurchaseType = pay.PayTypeToSensor[item.PayType]
	logger.Info("订阅扣款神策上报数据", item.UID, *event)
	event.Track(strconv.FormatInt(item.UID, 10))
}

// EventName 订阅扣款事件上报
func (ContractChargeSuccessFail) EventName() string {
	if config.Get().Service.Env != microservice.Product && config.Get().Service.Env != microservice.Mirror {
		return "h2_subscribe_success_fail_cs"
	}
	return "subscribe_success_fail_cs"
}
func (ContractChargeSuccessFail) Prefix() string {
	return ""
}
func (u *ContractChargeSuccessFail) Track(uid string) {
	go func() {
		if uid == "" || uid == "0" {
			return
		}
		err := sc.Track(uid, *u, true)
		if err != nil {
			logger.Error("神策过期事件上报失败", err)
		}
	}()
}

func FormatDurationVal(durationType, durationValue int) int {
	return durationValue * libproduct.DurationTypeToDaysSc[libproduct.DurationType(durationType)]
}

type GiftInfoItem struct {
	ProductMainType int `json:"product_main_type"`
	DurationType    int `json:"duration_type"`
	DurationValue   int `json:"duration_value"`
}

type OrderGiftInfo struct {
	IsGift   bool   `json:"is_gift"`
	GiftName string `json:"gift_name"`
}

func GetOrderGiftInfo(orderID string, productID int64) *OrderGiftInfo {
	result := &OrderGiftInfo{}
	orderItem := dbo.TbWebOrder.GetItemByOrderID(orderID)
	productItem := dbp.TbWebProduct.GetItemByID(productID)
	if productItem == nil || orderItem == nil {
		return result
	}
	if orderItem.HasGift == library.Yes && productItem.HasGift == library.Yes {
		giftList := make([]*GiftInfoItem, 0)
		if err := json.Unmarshal([]byte(productItem.GiftInfo), &giftList); err != nil {
			logger.Error("买赠权益Unmarshal失败，", orderID, err.Error())
			return result
		}
		for _, v := range giftList {
			giftItemDesc := fmt.Sprintf("%s%d%s,",
				libproduct.GiftProductTypeDesc[v.ProductMainType],
				v.DurationValue, libproduct.DurationTypeDesEnum[libproduct.DurationType(v.DurationType)],
			)
			result.GiftName += giftItemDesc
		}
		if len(giftList) > 0 {
			result.IsGift = true
		}
	}
	result.GiftName = strings.TrimRight(result.GiftName, ",")
	return result
}
