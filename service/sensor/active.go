package sensor

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	sc "gitlab.dailyyoga.com.cn/gokit/sensorsdata"
	"gitlab.dailyyoga.com.cn/server/children/config"
)

type MiniActive struct {
	ProjectName string `json:"project_name"`
	CommData    `json:"common_data"`
}

// EventName 事件名称
func (MiniActive) EventName() string {
	if config.Get().Service.Env != microservice.Product && config.Get().Service.Env != microservice.Mirror {
		return "h2_mini_program_active"
	}
	return "mini_program_active"
}

func (MiniActive) Prefix() string {
	return ""
}

func (s *MiniActive) Track(uid string) {
	go func() {
		if uid == "" || uid == "0" {
			return
		}
		err := sc.Track(uid, *s, true)
		if err != nil {
			logger.Error("神策过期事件上报失败", err)
		}
	}()
}
