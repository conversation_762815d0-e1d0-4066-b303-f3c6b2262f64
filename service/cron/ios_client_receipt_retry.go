package cron

import (
	"context"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases/iap"
	libcache "gitlab.dailyyoga.com.cn/server/children/library/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/order"
)

func IOSClientReceiptRetry() {
	rd := cache.GetCRedis()
	lockKey := libcache.LockCronIOSClientReceiptRetry
	expireTime := 5
	if lock := rd.Lock(lockKey, cache.LockExpiration(time.Duration(expireTime)*time.Minute)); !lock {
		logger.Info("已被锁定")
		return
	}
	defer func() {
		if err := rd.Unlock(lockKey); err != nil {
			logger.Error(err)
		}
	}()
	start := time.Now().Unix() - 1800
	end := time.Now().Unix() - 60
	renewList := iap.TbIOSReceipt.GetNotDealList(start, end)
	logger.Info("IOS 客户端交易凭证重试开始", len(renewList))
	if len(renewList) == 0 {
		return
	}
	for _, v := range renewList {
		ok, err := rd.GetClient().Expire(context.Background(), lockKey, time.Duration(expireTime)*time.Minute).Result()
		if err != nil || !ok {
			return
		}
		order.DealIAPReceiptLog(v, nil)
	}
	logger.Info("IOS 客户端交易凭证重试结束", len(renewList))
}
