package cron

import (
	"context"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	dbuser "gitlab.dailyyoga.com.cn/server/children/databases/user"
	libcache "gitlab.dailyyoga.com.cn/server/children/library/cache"
	libpay "gitlab.dailyyoga.com.cn/server/children/library/pay"
	libproduct "gitlab.dailyyoga.com.cn/server/children/library/product"
	libuser "gitlab.dailyyoga.com.cn/server/children/library/user"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	srvorder "gitlab.dailyyoga.com.cn/server/children/service/order"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

// TiktokContractCharge 抖音扣款脚本
func TiktokContractCharge(appID, domainURL string) {
	rd := cache.GetCRedis()
	lockKey := libcache.LockCronTiktokContractCharge
	expire := 15 * time.Minute
	if lock := rd.Lock(lockKey, cache.LockExpiration(expire)); !lock {
		logger.Info("已被锁定")
		return
	}
	defer func() {
		if err := rd.Unlock(lockKey); err != nil {
			logger.Error(err)
		}
	}()
	logger.Info("抖音自动续订自动扣款脚本开始")
	now := time.Now()
	todayStart := now.AddDate(0, 0, -1)
	oneDayFuture := now.AddDate(0, 0, 1)
	list := dbuser.TbWPSubU.GetTimeChargeUserList(util.FormatStartTime(todayStart.Unix()),
		util.FormatEndTime(oneDayFuture.Unix()), libpay.PayTypeTikTok)
	if len(list) == 0 {
		return
	}
	total := 0
	for k := range list {
		if list[k].OfferType > libproduct.ConstOfferTypeNo && list[k].ExpiresTime > now.Unix() {
			continue
		}
		ata := dbuser.TbAccountThirdAuth.GetBindThirdInfoByUID(list[k].UID, libuser.LoginTypeWechat)
		if ata == nil {
			continue
		}
		thirdMore := dbuser.TbMoreAppID.GetItemByThirdOpenIDAndUniqID(ata.ThirdOpenID, ata.ThirdUniqID)
		if thirdMore == nil {
			continue
		}
		if thirdMore.AppID != appID {
			continue
		}
		srvorder.SrvOrderComplete.TiktokChargeUserContract(list[k], &srvorder.ContractChargeParams{
			IsRenew: true,
		}, domainURL)
		total++
		ok, err := rd.GetClient().Expire(context.Background(), lockKey, expire).Result()
		if err != nil || !ok {
			logger.Info("抖音自动续订脚本锁续期失败", total, lockKey, err, ok)
			return
		}
	}
	logger.Info("抖音自动续订自动扣款脚本结束", total)
}
