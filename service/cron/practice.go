package cron

import (
	"sync"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/server/children/databases/practice"
	"gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libcache "gitlab.dailyyoga.com.cn/server/children/library/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
)

func SyncUserPracticeLog() {
	rd := cache.GetCRedis()
	lockKey := libcache.LockSyncUserPracticeLog
	var lockSecond = time.Duration(getLockSeconds())
	if lock := rd.Lock(lockKey, cache.LockExpiration(lockSecond*time.Second)); !lock {
		logger.Info("已被锁定")
		return
	}
	defer func() {
		if err := rd.Unlock(lockKey); err != nil {
			logger.Error(err)
		}
	}()
	logger.Info("同步练习数据脚本开始")
	syncUserPracticeLogByAccount()
	logger.Info("同步练习数据脚本结束")
}

func getLockSeconds() int {
	loc, _ := time.LoadLocation("Asia/Shanghai")
	now := time.Now()
	tomorrow := time.Date(now.Year(), now.Month(), now.Day()+1, 00, 00, 00, 0, loc)
	exp := tomorrow.Sub(now)
	return int(exp.Seconds()) + library.DayTime
}

const UIDStart = **********
const MaxSameTimeTask = 10

func syncUserPracticeLogByAccount() {
	start := UIDStart
	limit := 10000
	end := start + limit
	for {
		accountList := user.TbAccount.GetListByIDRange(start, end)
		if len(accountList) == 0 {
			break
		}
		var taskNum int64
		var wg sync.WaitGroup
		for i := range accountList {
			uid := accountList[i].ID
			wg.Add(1)
			safelygo.GoSafelyByTraceID(func() {
				taskNum++
				defer wg.Done()
				syncByUID(uid)
			})
			if taskNum >= MaxSameTimeTask {
				wg.Wait()
				taskNum = 0
			}
		}
		logger.Infof("同步练习数据脚本用户%d到%d执行结束", start, end)
		start += limit
		end += limit
	}
}

type DayStat struct {
	PracticeCount int64
	PlayTime      int
	Calorie       int
}

func syncByUID(uid int64) {
	logs := practice.TbPracticeLog.GetAllByUID(uid)
	practiceDays := practice.TbPlayDay.GetListAscDateIndex(uid)
	collectSummary(uid, logs, practiceDays)
}

func collectSummary(uid int64, logs []practice.Log, practiceDays []practice.Day) {
	if len(logs) == 0 {
		return
	}
	collect, err := practice.TbUserCollect.FindOrCreate(uid)
	if err != nil {
		logger.Warn("同步错误，", err)
	}
	collect.UID = uid
	dayStatMap := make(map[string]*DayStat)
	totalPlayTime, totalCalorie := 0, 0
	for i := range logs {
		// 完善历史数据
		log := modifyHistoryLog(logs[i])
		totalPlayTime += log.PlayTime
		totalCalorie += log.Calories
		dateIndex := time.Unix(log.PracticeStartTime, 0).Format("20060102")
		if _, ok := dayStatMap[dateIndex]; ok {
			dayStatMap[dateIndex].PracticeCount++
			dayStatMap[dateIndex].PlayTime += log.PlayTime
			dayStatMap[dateIndex].Calorie += log.Calories
		} else {
			dayStatMap[dateIndex] = &DayStat{
				PracticeCount: 1,
				PlayTime:      log.PlayTime,
				Calorie:       log.Calories,
			}
		}
	}
	collect.TotalCalorie = int64(totalCalorie)
	collect.TotalPlayTime = int64(totalPlayTime)
	collect.LastContinuePracticeDay = LastContinuePracticeDay(practiceDays)
	collect.TotalPracticeDay = int64(len(practiceDays))
	if err := collect.Update(); err != nil {
		logger.Warn("同步历史数据collect更新错误，", err, uid)
	}
	syncPracticeDay(uid, dayStatMap)
}

func syncPracticeDay(uid int64, dayStatMap map[string]*DayStat) {
	for dateIndex := range dayStatMap {
		day := practice.TbPlayDay.GetItemByMasterDB(uid, dateIndex)
		if day != nil {
			day.PlayTime = int64(dayStatMap[dateIndex].PlayTime)
			day.Calorie = int32(dayStatMap[dateIndex].Calorie)
			day.PracticeCount = dayStatMap[dateIndex].PracticeCount
			if err := day.Update(); err != nil {
				logger.Warn("同步历史数据day更新错误，", err, uid)
			}
		} else {
			day := &practice.PlayDay{}
			day.UID = uid
			day.PlayTime = int64(dayStatMap[dateIndex].PlayTime)
			day.Calorie = int32(dayStatMap[dateIndex].Calorie)
			day.PracticeCount = 1
			day.DateIndex = dateIndex
			if err := day.Save(); err != nil {
				logger.Warn("同步历史数据day新增错误，", err, uid)
			}
		}
	}
}

// nolint
func modifyHistoryLog(log practice.Log) practice.Log {
	if log.PlayTime == 0 && log.Minutes > 0 {
		log.PlayTime = log.Minutes * 60
	}
	if log.PracticeStartTime == 0 && log.CreateTime > 0 {
		log.PracticeStartTime = log.CreateTime
	}
	return log
}

func LastContinuePracticeDay(dayKeyList []practice.Day) int64 {
	var keepDay int64 = 1
	var lastDateIndex string
	if len(dayKeyList) < 1 {
		return keepDay
	}
	for _, v := range dayKeyList {
		if lastDateIndex == "" {
			lastDateIndex = v.DateIndex
		} else {
			lastDateIndexUnix := convertTimeUnix(lastDateIndex)
			curDateIndexUnix := convertTimeUnix(v.DateIndex)
			if curDateIndexUnix-lastDateIndexUnix == library.DayTime {
				keepDay++
			} else {
				keepDay = 1
			}
			lastDateIndex = v.DateIndex
		}
	}
	return keepDay
}

func convertTimeUnix(dateIndex string) int64 {
	tUnix, _ := time.Parse("20060102", dateIndex)
	return tUnix.Unix()
}
