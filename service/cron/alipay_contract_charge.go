package cron

import (
	"context"
	"fmt"
	"math/rand"
	"reflect"
	"strconv"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	dborder "gitlab.dailyyoga.com.cn/server/children/databases/order"
	dbuser "gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libcache "gitlab.dailyyoga.com.cn/server/children/library/cache"
	libpay "gitlab.dailyyoga.com.cn/server/children/library/pay"
	libproduct "gitlab.dailyyoga.com.cn/server/children/library/product"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	srvorder "gitlab.dailyyoga.com.cn/server/children/service/order"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

const AliMaxSameTimeTask = 3

// AlipayContractCharge 支付宝
func AlipayContractCharge() {
	rd := cache.GetCRedis()
	lockKey := libcache.LockCronAlipayContractCharge
	expire := 10 * time.Minute
	if lock := rd.Lock(lockKey, cache.LockExpiration(expire)); !lock {
		logger.Info("支付宝自动续订脚本已被锁定")
		return
	}
	defer func() {
		if err := rd.Unlock(lockKey); err != nil {
			logger.Error(err)
		}
	}()
	logger.Info("支付宝自动续订自动扣款脚本开始")
	now := time.Now()
	todayStart := util.FormatStartTime(now.Unix())
	fiveDayFuture := now.AddDate(0, 0, 1)
	list := dbuser.TbWPSubU.GetTimeChargeUserList(todayStart,
		util.FormatEndTime(fiveDayFuture.Unix()), libpay.PayTypeAlipay)
	if len(list) == 0 {
		return
	}
	randomShuffle(list)
	logger.Info("支付宝自动续订脚本数据量", len(list))
	total := 0
	var taskNum int64
	var wg sync.WaitGroup
	for k := range list {
		if list[k].OfferType != libproduct.ConstOfferTypeNo {
			continue
		}
		item := list[k]
		taskNum++
		wg.Add(1)
		safelygo.GoSafelyByTraceID(func() {
			defer wg.Done()
			srvorder.SrvOrderComplete.ChargeUserContract(item, &srvorder.ContractChargeParams{
				IsRenew: true,
				IsStep:  false,
			})
			total++
		})
		if taskNum >= MaxSameTimeTask {
			wg.Wait()
			taskNum = 0
		}
		ok, err := rd.GetClient().Expire(context.Background(), lockKey, expire).Result()
		total++
		if err != nil || !ok {
			logger.Info("支付宝自动续订脚本锁续期失败", total, lockKey, err, ok)
			return
		}
	}
	logger.Info("支付宝自动续订自动扣款脚本结束", total)
}

func AlipayRateInit() {
	ctx := context.Background()
	rd := cache.GetCRedis().GetClient()
	totalStr, _ := rd.Get(ctx, libcache.AlipayChargeDayTotal).Result()
	totalInt, _ := strconv.ParseInt(totalStr, 10, 64)
	if totalStr != "" && totalInt > 0 {
		return
	}
	var total int64
	now := time.Now()
	todayStr := now.Format("20060102")
	todayStart := util.FormatStartTime(now.Unix())
	fiveDayFuture := now.AddDate(0, 0, 1)
	list := dbuser.TbWPSubU.GetTimeChargeUserList(todayStart,
		util.FormatEndTime(fiveDayFuture.Unix()), libpay.PayTypeAlipay)
	for k := range list {
		if list[k].OfferType != libproduct.ConstOfferTypeNo {
			continue
		}
		item := dborder.DailyChargePlan{
			SubID:     list[k].ID,
			DateIndex: todayStr,
		}
		if err := item.Save(); err != nil {
			logger.Error(err)
		}
		total++
	}
	todayStart = now.AddDate(0, 0, -1).Unix()
	fiveDayFuture = now.AddDate(0, 0, 1)
	list = dbuser.TbWPSubU.GetTimeChargeUserList(util.FormatStartTime(todayStart),
		util.FormatEndTime(fiveDayFuture.Unix()), libpay.PayTypeAlipay)
	for k := range list {
		if list[k].OfferType == libproduct.ConstOfferTypeNo {
			continue
		}
		if list[k].ExpiresTime > (util.FormatStartTime(now.Unix()) + libpay.AlipayChargeControlEnd*3600) {
			continue
		}
		item := dborder.DailyChargePlan{
			SubID:     list[k].ID,
			DateIndex: todayStr,
		}
		if err := item.Save(); err != nil {
			logger.Error(err)
		}
		total++
	}
	todaySecond := time.Duration(library.DayTime-(time.Now().Unix()-util.FormatStartTime(time.Now().Unix()))) * time.Second
	ok, err := rd.SetNX(ctx, libcache.AlipayChargeDayTotal, total, todaySecond).Result()
	if !ok || err != nil {
		return
	}
	rd.Set(ctx, libcache.AlipayChargeDayHasCharge, 0, todaySecond)
}

// nolint
func randomShuffle(slice interface{}) {
	rand.Seed(time.Now().UnixNano())
	value := reflect.ValueOf(slice)
	if value.Kind() != reflect.Slice {
		return
	}
	// 使用随机排序算法
	length := value.Len()
	if length == 0 {
		return
	}
	swap := reflect.Swapper(slice)
	for i := length - 1; i > 0; i-- {
		j := rand.Intn(i + 1) //nolint
		swap(i, j)
	}
}

// AlipayOfferContractCharge 支付宝优惠扣款脚本 0731
func AlipayOfferContractCharge() {
	rd := cache.GetCRedis()
	lockKey := libcache.LockCronAlipayOfferContractCharge
	expire := 10 * time.Minute
	if lock := rd.Lock(lockKey, cache.LockExpiration(expire)); !lock {
		logger.Info("已被锁定")
		return
	}
	defer func() {
		if err := rd.Unlock(lockKey); err != nil {
			logger.Error(err)
		}
	}()
	logger.Info("支付宝自动续订自动扣款脚本开始")
	now := time.Now()
	todayStart := now.AddDate(0, 0, -1).Unix()
	fiveDayFuture := now.AddDate(0, 0, 1)
	list := dbuser.TbWPSubU.GetTimeChargeUserList(util.FormatStartTime(todayStart),
		util.FormatEndTime(fiveDayFuture.Unix()), libpay.PayTypeAlipay)
	if len(list) == 0 {
		return
	}
	randomShuffle(list)
	logger.Info("支付宝自动续订脚本数据量", len(list))
	total := 0
	for k := range list {
		if list[k].OfferType == libproduct.ConstOfferTypeNo {
			continue
		}
		if list[k].ExpiresTime > now.Unix() {
			continue
		}
		srvorder.SrvOrderComplete.ChargeUserContract(list[k], &srvorder.ContractChargeParams{
			IsRenew: true,
			IsStep:  false,
		})
		ok, err := rd.GetClient().Expire(context.Background(), lockKey, expire).Result()
		total++
		if err != nil || !ok {
			logger.Info("支付宝自动续订脚本锁续期失败", total, lockKey, err, ok)
			return
		}
	}
	logger.Info("支付宝自动续订自动扣款脚本结束", total)
}

// nolint
func DelayStepCharge() {
	rd := cache.GetCRedis()
	lockKey := libcache.LockStepDelayCharge
	expire := 10 * time.Minute
	if lock := rd.Lock(lockKey, cache.LockExpiration(expire)); !lock {
		logger.Info("支付宝自动续订脚本已被锁定")
		return
	}
	defer func() {
		if err := rd.Unlock(lockKey); err != nil {
			logger.Error(err)
		}
	}()
	rdc := cache.GetCRedis()
	dataKey := libcache.StepDelayCharge
	var taskNum int64
	var wg sync.WaitGroup
	total := 0
	logger.Info("阶梯延迟扣款任务执行任务开始")
	loc, _ := time.LoadLocation(library.TimeZoneBeijing)
	for {
		opt := &redis.ZRangeBy{
			Min:    "0",
			Max:    strconv.FormatInt(time.Now().Unix(), 10),
			Offset: 0,
			Count:  30,
		}
		jobsList, err := rdc.GetClient().ZRangeByScoreWithScores(context.Background(), dataKey, opt).Result()
		if err != nil {
			logger.Warn("阶梯延迟扣款任务获取任务失败：", err)
			break
		}
		if len(jobsList) == 0 {
			logger.Warn("阶梯延迟扣款任务获取任务为空")
			break
		}
		logger.Info("阶梯延迟扣款任务获取到任务量", len(jobsList))
		for _, job := range jobsList {
			ret, err := rdc.GetClient().ZRem(context.Background(), dataKey, job.Member).Result()
			if err != nil || ret <= 0 {
				logger.Error("阶梯延迟扣款删除任务失败:", job)
				return
			}
			ts := time.Now()
			start := time.Date(ts.Year(), ts.Month(), ts.Day(), 9, 0, 0, 0, loc)
			end := time.Date(ts.Year(), ts.Month(), ts.Day(), 21, 0, 0, 0, loc)
			if ts.After(end) || ts.Before(start) {
				logger.Warn("阶梯扣款不在时间范围内,直接跳过", job)
				continue
			}
			if util.FormatStartTime(int64(job.Score)) != util.FormatStartTime(time.Now().Unix()) {
				logger.Warn("阶梯延迟扣款任务时间非当天", job.Member, job.Score)
				continue
			}
			jobID, ok := job.Member.(string)
			if !ok || jobID == "" {
				logger.Error("阶梯延迟扣款任务jobID为空", job.Member)
				continue
			}
			intID, err := strconv.ParseInt(jobID, 10, 64)
			if err != nil {
				logger.Error(err)
				continue
			}
			logger.Info("阶梯延迟扣款任务执行", intID)
			taskNum++
			wg.Add(1)
			safelygo.GoSafelyByTraceID(func() {
				defer wg.Done()
				item := dbuser.TbWPSubU.GetItem(intID)
				if item == nil {
					return
				}
				srvorder.SrvOrderComplete.ChargeUserContract(item, &srvorder.ContractChargeParams{
					IsRenew: true,
					IsStep:  true,
				})
				total++
			})
			if taskNum >= MaxSameTimeTask {
				wg.Wait()
				taskNum = 0
			}
			ok, err = rd.GetClient().Expire(context.Background(), lockKey, expire).Result()
			if err != nil || !ok {
				logger.Info("阶梯延迟扣款任务锁续期失败", total, lockKey, err, ok)
				return
			}
		}
	}
	logger.Info("阶梯延迟扣款任务执行任务量", total)
}

func DailyChargePlan() {
	rd := cache.GetCRedis()
	lockKey := libcache.LockDailyChargePlan
	expire := 1 * time.Hour
	if lock := rd.Lock(lockKey, cache.LockExpiration(expire)); !lock {
		logger.Info("已被锁定")
		return
	}
	AlipayRateInit()
}

// nolint
func DailyChargePlanReport() {
	rd := cache.GetCRedis()
	lockKey := libcache.LockDailyChargePlanReport
	expire := 10 * time.Minute
	if lock := rd.Lock(lockKey, cache.LockExpiration(expire)); !lock {
		logger.Info("已被锁定")
		return
	}
	planList := dborder.TbDailyChargePlan.GetPlanList(time.Now().Format("20060102"))
	if len(planList) == 0 {
		logger.Error("支付宝扣款计划报表:当天无扣款计划")
		return
	}
	total := len(planList)
	var success, fail, logoff, unsign, maxCharge int
	errMap := make(map[string]int)
	for _, v := range planList {
		if v.IsSuccess == library.Yes {
			success++
			continue
		}
		if v.IsSuccess == library.No {
			fail++
			if al, ok := libpay.AliErrCategoryMap[v.ErrSubCode]; ok {
				errMap[libpay.AliErrCodeMsg[al]]++
			} else {
				if v.ErrSubCode != "" {
					errMap[v.ErrSubCode]++
				} else {
					errMap["未知"]++
				}
			}
			continue
		}
		switch v.AbnormalType {
		case libpay.AbnormalTypeLogoff:
			logoff++
		case libpay.AbnormalTypeUnsign:
			unsign++
		case libpay.AbnormalTypeMaxChargeDay:
			maxCharge++
		}
		if v.IsSuccess != 0 || v.AbnormalType != 0 {
			continue
		}
		subItem := dbuser.TbWPSubU.GetItem(v.SubID)
		if subItem == nil {
			continue
		}
		if subItem.Status != library.Yes {
			unsign++
			continue
		}
	}
	msg := fmt.Sprintf("【小树苗运动】今日支付宝扣款报表\n本应扣款【%d】单\n成功【%d】单\n失败【%d】单", total, success, fail)
	for k, v := range errMap {
		msg += fmt.Sprintf("\n因%s失败【%d】单", k, v)
	}
	if maxCharge > 0 {
		msg += fmt.Sprintf("\n周期后推未扣款【%d】单", maxCharge)
	}
	if logoff > 0 {
		msg += fmt.Sprintf("\n用户注销未扣款【%d】单", logoff)
	}
	if unsign > 0 {
		msg += fmt.Sprintf("\n用户解约未扣款【%d】单", unsign)
	}
	diff := total - success - fail - (logoff + unsign + maxCharge)
	if diff > 0 {
		msg += fmt.Sprintf("\n差额【%d】单", diff)
	}
	err := util.WorkWechatContentMsg(
		"https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ce1fac97-a2a3-4f38-8b58-37f42cbda9ef", msg, nil)
	if err != nil {
		logger.Error(err)
	}
}
