package cron

import (
	"context"
	"encoding/json"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/service/app"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

func HandleAppleAds() {
	ctx, cancel := context.WithCancel(context.Background())
	resCh, errCh := util.GetFromQueue(ctx, cache.GetYoga01Redis().GetClient(), app.QueueKey)
	isStop := false
	for {
		select {
		case msg, ok := <-resCh:
			if !ok {
				isStop = true
				break
			}
			var data app.AdsInfoAdService
			err := json.Unmarshal([]byte(msg), &data)
			if err != nil {
				logger.Info("apple-asa 出错了", msg, err.Error())
				continue
			}
			logger.Infof("apple-asa 输出 %#v", data)
			data.UploadScAppleAdsInfoAdService()
			if err != nil {
				logger.Warn("apple-asa 报错", err.<PERSON>rror())
			}
		case <-errCh:
		}
		if isStop {
			cancel()
			break
		}
	}
}
