package cron

import (
	"context"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases/iap"
	libcache "gitlab.dailyyoga.com.cn/server/children/library/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/order"
)

func IOSNotificationRetry() {
	rd := cache.GetCRedis()
	lockKey := libcache.LockCronIOSNotificationRetry
	var expireTime time.Duration = 5
	if lock := rd.Lock(lockKey, cache.LockExpiration(expireTime*time.Minute)); !lock {
		logger.Info("已被锁定")
		return
	}
	defer func() {
		if err := rd.Unlock(lockKey); err != nil {
			logger.Error(err)
		}
	}()
	start := time.Now().Unix() - 1800
	end := time.Now().Unix() - 30
	renewList := iap.TbNotification.GetNotDealList(start, end)
	logger.Info("IOS 服务端通知重试开始", len(renewList))
	if len(renewList) == 0 {
		return
	}
	for _, v := range renewList {
		ok, err := rd.GetClient().Expire(context.Background(), lockKey, expireTime*time.Minute).Result()
		if err != nil || !ok {
			return
		}
		order.DealIOSNotification(v)
	}
	logger.Info("IOS 服务端通知重试结束", len(renewList))
}
