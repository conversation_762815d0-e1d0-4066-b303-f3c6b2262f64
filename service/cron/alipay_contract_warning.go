package cron

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/databases/order"
	"gitlab.dailyyoga.com.cn/server/children/databases/product"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libcache "gitlab.dailyyoga.com.cn/server/children/library/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

var AlipayMchIDMap = map[int64]string{
	2021004101645726: "<EMAIL>",
	2021003181662991: "<EMAIL>",
	2021003182655111: "<EMAIL>",
	2021003177646314: "<EMAIL>",
}

// AlipayContractCharge 支付宝
func AlipayContractWarning() {
	cfg := config.Get()
	if cfg.Service.Env != microservice.Product {
		return
	}
	rd := cache.GetYogaRedis()
	lockKey := libcache.LockCronAlipayContractWarning
	var exp time.Duration = 10
	if lock := rd.Lock(lockKey, cache.LockExpiration(exp*time.Minute)); !lock {
		logger.Info("【小树苗运动】支付宝订阅扣款脚本已被锁定:会员非优惠")
		return
	}
	defer func() {
		if err := rd.Unlock(lockKey); err != nil {
			logger.Error(err)
		}
	}()
	validMchID := getValidMchID()
	logger.Info("支付宝自动续订检查签约", validMchID)
	now := time.Now().Unix()
	start := now - 1200
	AlipayContractList := order.TbAlipayContract.GetValidListByTimeRange(start, now)
	content := make([]string, 0)
	if len(AlipayContractList) < 1 {
		for _, mchID := range validMchID {
			message := getMessage(mchID)
			content = append(content, message)
		}
	} else {
		ContractMchIDMap := make(map[string]int64)
		for _, item := range AlipayContractList {
			if _, ok := ContractMchIDMap[item.AppID]; !ok {
				ContractMchIDMap[item.AppID] = 1
			}
		}
		for _, mchID := range validMchID {
			if _, ok := ContractMchIDMap[mchID]; !ok {
				message := getMessage(mchID)
				content = append(content, message)
			}
		}
	}
	if len(content) > 0 {
		title := fmt.Sprintf(
			"【小树苗运动】警告！！！当前支付宝自动订阅新增签约异常，请立即排查问题 \n 检查区间:%s至%s \n",
			time.Unix(start, 0).Format("2006-01-02 15:04:05"),
			time.Unix(now, 0).Format("2006-01-02 15:04:05"),
		)
		address := "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ce1fac97-a2a3-4f38-8b58-37f42cbda9ef"
		err := util.WorkWechatContentMsg(address,
			fmt.Sprintf("%s%s", title, strings.Join(content, "\n")),
			nil)
		if err != nil {
			logger.Warn(err)
		}
	}
}

func getMessage(mchID string) string {
	var message string
	mchIDInt, _ := strconv.ParseInt(mchID, 10, 64)
	if mail, ok := AlipayMchIDMap[mchIDInt]; ok {
		message = fmt.Sprintf("异常账号：%s  APPID:%s", mail, mchID)
	} else {
		message = fmt.Sprintf("异常账号：请查看枚举增加对应账户 APPID:%s", mchID)
	}
	return message
}

func getValidMchID() []string {
	machIDMap := make(map[string]int64)
	mchIDList := make([]string, 0)
	list := order.TbMchIDAccountConfig.GetValidList()
	for _, v := range list {
		if v.ProductID != 0 {
			item := product.TbWebProduct.GetItemByID(v.ProductID)
			if item != nil && item.IsSubscribe != library.Yes {
				continue
			}
		}
		if _, ok := machIDMap[v.MchID]; !ok {
			machIDMap[v.MchID] = 1
			mchIDList = append(mchIDList, v.MchID)
		}
	}
	return mchIDList
}
