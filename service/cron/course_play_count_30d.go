package cron

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	db "gitlab.dailyyoga.com.cn/server/children/databases/course"
	libcache "gitlab.dailyyoga.com.cn/server/children/library/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
)

const (
	oneDay   = 24 * time.Hour
	pageSize = 100
)

func HandleCoursePlayTotal30Days() {
	rdc := cache.GetCRedis().GetClient()
	ctx := context.Background()
	// 使用当前日期作为字段
	page := 1
	for {
		list := db.TbCourseLibrary.GetListByQuery(&db.QueryParam{
			Page:     page,
			PageSize: pageSize,
		})
		if len(list) < pageSize {
			break
		}
		for i := range list {
			item := list[i]
			count30Days := GetCoursePlayCount30Days(item.ID)
			if err := rdc.HSet(ctx, libcache.CoursePlayTotal30Days, item.ID, count30Days).Err(); err != nil {
				logger.Error("设置课程总播放量失败", item.ID, err)
			}
		}
		page++
	}
	// 设置过期时间为1天
	rdc.Expire(ctx, libcache.CoursePlayTotal30Days, oneDay)
}

// GetCoursePlayCount30Days 获取课程近30天播放量
func GetCoursePlayCount30Days(courseID int64) int64 {
	rdc := cache.GetCRedis().GetClient()
	ctx := context.Background()
	cacheKey := fmt.Sprintf("%s%d", libcache.CoursePlayCount30Days, courseID)

	// 获取所有日期的播放量
	playCountMap, err := rdc.HGetAll(ctx, cacheKey).Result()
	if err != nil {
		logger.Error("获取课程播放量失败", courseID, err)
		return 0
	}
	if len(playCountMap) == 0 {
		return 0
	}
	// 计算30天前的日期
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30).Format("20060102")
	var totalCount int64
	for dateStr, countStr := range playCountMap {
		// 只统计近30天的数据
		if dateStr >= thirtyDaysAgo {
			count, _ := strconv.ParseInt(countStr, 10, 64)
			totalCount += count
		}
	}
	return totalCount
}
