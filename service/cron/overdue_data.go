package cron

import (
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	sc "gitlab.dailyyoga.com.cn/gokit/sensorsdata"
	"gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/databases/order"
	dbp "gitlab.dailyyoga.com.cn/server/children/databases/product"
	"gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libUser "gitlab.dailyyoga.com.cn/server/children/library/user"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
)

func DealOverDue() {
	rd := cache.GetCRedis()
	lockKey := "lock:overdue:deal"
	expire := 1 * time.Minute
	if lock := rd.Lock(lockKey, cache.LockExpiration(expire)); !lock {
		logger.Info("处理过期数据脚本已被锁定")
		return
	}
	defer func() {
		if err := rd.Unlock(lockKey); err != nil {
			logger.Error(err)
		}
	}()
	logger.Info("脚本开始执行")
	// 脚本开始
	start := 0
	limit := 10000
	end := start + limit
	for {
		logger.Infof("循环:%v,结束%v", start, end)
		historyList := user.TbVipHistory.GetListByIDRange(start, end)
		if len(historyList) == 0 {
			logger.Infof("同步过期数据脚本没有数据")
			break
		}
		for i := range historyList {
			data := historyList[i]
			dealVipHistory(data)
		}
		logger.Infof("同步会员记录脚本，%d到%d执行结束", start, end)
		start += limit
		end += limit
	}
	logger.Infof("同步过期数据脚本结束")
}

func dealVipHistory(history *user.VipHistory) {
	// 查找该笔记录与之相符合（范围前后15min）的订单
	minTime := history.CreateTime - 30*60
	maxTime := history.CreateTime + 30*60
	orderList := order.TbWebOrder.GetListByUIDAndTime(history.UID, minTime, maxTime)
	if len(orderList) == 0 {
		return
	}
	if history.OrderID != "" {
		return
	}
	history.OrderID = orderList[0].OrderID
	if err := history.Update(); err != nil {
		logger.Warn("同步会员数据更新错误，", err, history.UID)
	}
}

func SensorVipOverdue() {
	rd := cache.GetCRedis()
	lockKey := "Lock:overdue:vip"
	expire := 5 * time.Minute
	if lock := rd.Lock(lockKey, cache.LockExpiration(expire)); !lock {
		logger.Info("处理过期数据脚本已被锁定")
		return
	}
	defer func() {
		if err := rd.Unlock(lockKey); err != nil {
			logger.Error(err)
		}
	}()
	last := user.TbAccount.GetLastData()
	// 昨天会员过期的用户进行上报
	loc, _ := time.LoadLocation(library.TimeZoneBeijing)
	now := time.Now()
	end := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, loc).Unix()
	start := end - 86400
	var idMin, idMax int64 = UIDStart, UIDStart + 10000
	var limit int64 = 10000
	for {
		if idMin >= last.ID {
			break
		}
		list := user.TbAccount.GetListByIDAndEndTime(idMin, idMax, start, end)
		if len(list) == 0 {
			idMin = idMax
			idMax += limit
			continue
		}
		for _, v := range list {
			// 处理
			ov := &VipOverdue{
				IsVipPurchase: false,
				ProductType:   libUser.Unknown,
				VipSourceType: libUser.Unknown,
				HasSubscribe:  false,
				ProductName:   libUser.Unknown,
			}
			ov.reportSensorVipOverdue(v)
		}
		idMin = idMax
		idMax += limit
	}
	logger.Infof("上报定时任务结束%v,%v", idMin, idMax)
}

func (o *VipOverdue) reportSensorVipOverdue(userInfo *user.Account) {
	// 先查询用户account_vip_record记录
	record := user.TbVipHistory.GetLastByUID(userInfo.ID)
	if record != nil {
		// 若表里order_id为空则再查一下
		if record.OrderID == "" {
			createStart := record.CreateTime - 10*60
			createEnd := record.CreateTime + 10*60
			orderList := order.TbWebOrder.GetListByUIDAndTime(userInfo.ID, createStart, createEnd)
			if len(orderList) > 0 {
				record.OrderID = orderList[0].OrderID
			}
		}
		// 产品类型和名称
		if record.OrderID != "" {
			userOrder := order.TbWebOrder.GetItemByOrderID(record.OrderID)
			// 获取产品信息
			product := dbp.TbWebProduct.GetItemByID(userOrder.ProductID)
			// 获取产品名称和类型
			productType := ""
			// 产品名称取支付事件产品名称
			o.ProductName = product.Name
			o.ProductID = product.ID
			// 产品类型取支付事件order_channel
			o.ProductType = productType
		}
		// 是否订阅
		if subList := user.TbWPSubU.GetValidSubscribeUser(userInfo.ID); len(subList) != 0 {
			o.HasSubscribe = true
		}
		// 是否会员购买
		if record.ChangeReason == libUser.VipChangeReasonEnum.Purchase &&
			record.SourceType == libUser.VipSourceTypeEnum.System {
			o.IsVipPurchase = true
		}
		// 权益转移处理
		if record.ChangeReason == libUser.VipChangeReasonEnum.TransferEquity && record.OrderID != "" {
			o.IsVipPurchase = true
		}
		// 会员来源
		if record.SourceType == libUser.VipSourceTypeEnum.System {
			// 系统发放
			o.VipSourceType = libUser.VipChangeReasonDesc[record.ChangeReason]
		} else {
			// 后台操作
			if record.ChangeReason == libUser.VipChangeReasonEnum.Activity ||
				record.ChangeReason == libUser.VipChangeReasonEnum.TransferEquity {
				o.VipSourceType = libUser.VipChangeReasonDesc[record.ChangeReason]
			} else {
				o.VipSourceType = "后台发放"
			}
		}
	}
	logger.Infof("神策过期事件上报 %+v", o)
	o.Track(strconv.FormatInt(userInfo.ID, 10))
}

// nolint
type VipOverdue struct {
	// 是否会员购买，bool，会员购买为真，会员未购买为假
	IsVipPurchase bool `json:"is_vip_purchase"`
	// 购买类型 目前有的类型有：会员、精选会员、挑战赛、单课、瑜伽会员
	ProductType string `json:"product_type"`
	// 会员来源: 购买会员相关商品、管理后台添加、活动奖励（购买优先）
	VipSourceType string `json:"vip_source_type"`
	// 是否订阅，bool，为真代表过期时用户还是订阅状态，为假代表过期时用户已经取消订阅
	HasSubscribe bool `json:"has_subscribe"`
	// 购买产品名称
	ProductName string `json:"product_name"`
	// 购买产品ID
	ProductID int64 `json:"product_id"`
}

// EventName 会员过期事件上报
func (VipOverdue) EventName() string {
	if config.Get().Service.Env != microservice.Product && config.Get().Service.Env != microservice.Mirror {
		return "h2_vip_overdue_cs"
	}
	return "vip_overdue_cs"
}

func (VipOverdue) Prefix() string {
	return ""
}
func (o *VipOverdue) Track(uid string) {
	if uid == "" || uid == "0" {
		return
	}
	err := sc.Track(uid, *o, true)
	if err != nil {
		logger.Error("神策过期事件上报失败", err)
	}
}
