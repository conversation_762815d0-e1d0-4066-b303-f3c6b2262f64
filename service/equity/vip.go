package equity

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	dbo "gitlab.dailyyoga.com.cn/server/children/databases/order"
	dbp "gitlab.dailyyoga.com.cn/server/children/databases/product"
	dbu "gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libproduct "gitlab.dailyyoga.com.cn/server/children/library/product"
	libuser "gitlab.dailyyoga.com.cn/server/children/library/user"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/sensor"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type OperateEquityParam struct {
	UID           int64
	VipType       int
	DurationType  int
	DurationValue int
	SourceType    libuser.VipSourceType
	ChangeReason  libuser.VipChangeReason
	OperateType   libuser.VipOperateType
	OrderID       string
}

// OperateEquity 操作用户会员权益
func OperateEquity(session *xorm.Session, op *OperateEquityParam) (ok bool, historyID int64) {
	if op.DurationType == 0 || op.DurationValue == 0 {
		logger.Error("会员产品时间有误", *op)
		return false, 0
	}
	account := dbu.TbAccount.GetUserSessionByID(op.UID, session)
	if account == nil {
		logger.Error("充值会员,用户信息为空", op.UID)
		return false, 0
	}
	history := &dbu.VipHistory{
		UID:             op.UID,
		OriginStartTime: account.StartTime,
		OriginEndTime:   account.EndTime,
		DurationType:    op.DurationType,
		DurationValue:   op.DurationValue,
		OperateType:     op.OperateType,
		SourceType:      op.SourceType,
		ChangeReason:    op.ChangeReason,
		VipType:         op.VipType,
	}
	if op.OrderID != "" {
		history.OrderID = op.OrderID
	}
	startTime, endTime := int64(0), int64(0)
	startTime, endTime = OperateEquityVip(op, session)
	account.StartTime = startTime
	account.EndTime = endTime
	history.StartTime = startTime
	history.EndTime = endTime
	if err := account.UpdateByTranMustCols(session); err != nil {
		logger.Error(err)
		return false, 0
	}
	if err := history.SaveByTran(session); err != nil {
		logger.Error(err)
		return false, 0
	}
	if err := util.AddToQueue(context.Background(),
		cache.GetYoga01Redis().GetClient(),
		libuser.ChangeUserVipKey, strconv.FormatInt(history.UID, 10),
		int64(libuser.ChangeVipQueueDelaySeconds)); err != nil {
		logger.Error("change user vip 延迟队列错误", err.Error())
	}
	return true, history.ID
}

// CalcDurationTime 计算会员时间
func CalcDurationTime(startTime int64, durationType, durationValue int) int64 {
	if durationType == 0 || durationValue == 0 {
		return 0
	}
	if startTime == 0 {
		startTime = time.Now().Unix()
	}
	var y, m, d int
	switch libproduct.DurationType(durationType) {
	case libproduct.DurationTypeEnum.Day:
		d = durationValue
	case libproduct.DurationTypeEnum.Month:
		m = durationValue
	case libproduct.DurationTypeEnum.Year:
		y = durationValue
	}
	return time.Unix(startTime, 0).AddDate(y, m, d).Unix()
}
func OperateEquityVip(op *OperateEquityParam, session *xorm.Session) (startTime, endTime int64) {
	account := dbu.TbAccount.GetUserSessionByID(op.UID, session)
	if account == nil {
		logger.Error("充值会员,用户信息为空", op.UID)
		return startTime, endTime
	}
	// 会员已过期，新的开始时间为当前
	if account.EndTime < time.Now().Unix() {
		startTime = time.Now().Unix()
	}
	durationValue := op.DurationValue
	if op.OperateType == libuser.VipOperateTypeEnum.Sub {
		durationValue = -op.DurationValue
	}
	endTime = util.FormatEndTime(CalcVipTime(account.EndTime, op.DurationType, durationValue))
	// 结束时间小于开始时间或者小于当前时，更新为0
	if endTime <= startTime || endTime < time.Now().Unix() {
		startTime = 0
		endTime = 0
	}
	return startTime, endTime
}

// CalcVipTime 计算会员时间
func CalcVipTime(startTime int64, durationType, durationValue int) int64 {
	if startTime == 0 || startTime < time.Now().Unix() {
		startTime = time.Now().Unix()
	}
	var y, m, d int
	switch libproduct.DurationType(durationType) {
	case libproduct.DurationTypeEnum.Day:
		d = durationValue
	case libproduct.DurationTypeEnum.Month:
		m = durationValue
	case libproduct.DurationTypeEnum.Year:
		y = durationValue
	}
	return time.Unix(startTime, 0).AddDate(y, m, d).Unix()
}

// CalcVipTimeByStartTime 计算会员时间
func CalcVipTimeByStartTime(startTime int64, durationType, durationValue int) int64 {
	if startTime == 0 {
		startTime = time.Now().Unix()
	}
	var y, m, d int
	switch libproduct.DurationType(durationType) {
	case libproduct.DurationTypeEnum.Day:
		d = durationValue
	case libproduct.DurationTypeEnum.Month:
		m = durationValue
	case libproduct.DurationTypeEnum.Year:
		y = durationValue
	}
	return time.Unix(startTime, 0).AddDate(y, m, d).Unix()
}

func OperateRefundEquity(uid int64, durationType, durationValue int,
	product *dbp.WebProduct, webOrder *dbo.WebOrder) error {
	account := dbu.TbAccount.GetUserByID(uid)
	if account == nil {
		logger.Warn("退款充值会员,用户信息为空", uid)
		return nil
	}
	session := databases.GetEngineMaster().NewSession()
	defer session.Close()
	if err := session.Begin(); err != nil {
		logger.Error(err)
		return err
	}
	var err error
	defer func() {
		if err != nil {
			logger.Error(err)
			if err = session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()
	err = SubEquityProcess(session, webOrder, product, durationType, durationValue)
	if err != nil {
		return err
	}
	if err := session.Commit(); err != nil {
		return err
	}
	return nil
}

func OperateRefundEquityNoP(uid int64, durationType, durationValue, vipType int) error {
	account := dbu.TbAccount.GetUserByID(uid)
	if account == nil {
		logger.Warn("充值会员,用户信息为空", uid)
		return nil
	}
	session := databases.GetEngineMaster().NewSession()
	defer session.Close()
	if err := session.Begin(); err != nil {
		logger.Error(err)
		return err
	}
	var err error
	defer func() {
		if err != nil {
			logger.Error(err)
			if err = session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()
	if ok, _ := OperateEquity(session, &OperateEquityParam{
		UID:           uid,
		DurationType:  durationType,
		DurationValue: durationValue,
		VipType:       vipType,
		SourceType:    libuser.VipSourceTypeEnum.System,
		ChangeReason:  libuser.VipChangeReasonEnum.Refund,
		OperateType:   libuser.VipOperateTypeEnum.Sub,
	}); !ok {
		logger.Error("充值会员失败")
		err = errors.New("充值会员失败")
		return err
	}
	if err := session.Commit(); err != nil {
		return err
	}
	return nil
}

type UserVipHistoryItem struct {
	DateStr string `json:"date_str"`
	Desc    string `json:"desc"`
}

func GetUserVipHistory(uid int64) []*UserVipHistoryItem {
	list := dbu.TbVipHistory.GetListByUID(uid)
	if len(list) == 0 {
		return nil
	}
	res := make([]*UserVipHistoryItem, 0)
	for _, v := range list {
		vipTypeName := "会员"
		subOrAdd := "加"
		if v.OperateType == libuser.VipOperateTypeEnum.Sub {
			subOrAdd = "减"
		}
		durationDesc := GetDurationDesc(int32(v.DurationType), int32(v.DurationValue))
		if durationDesc == "" {
			continue
		}
		reasonDesc := libuser.VipChangeReasonDesc[v.ChangeReason]
		if reasonDesc == "" {
			continue
		}
		// 永久会员特殊逻辑处理
		if libproduct.DurationType(v.DurationType) == libproduct.DurationTypeEnum.Year &&
			v.DurationValue == libproduct.ForPermanentlyYears {
			durationDesc = "永久"
		}
		item := &UserVipHistoryItem{
			DateStr: time.Unix(v.CreateTime, 0).Format("2006-01-02"),
			Desc:    fmt.Sprintf("%s %s%s%s", reasonDesc, subOrAdd, durationDesc, vipTypeName),
		}
		res = append(res, item)
	}
	return res
}

func GetDurationDesc(durationType, durationValue int32) string {
	switch libproduct.DurationType(durationType) {
	case libproduct.DurationTypeEnum.Day:
		return fmt.Sprintf("%d%s", durationValue,
			libproduct.DurationTypeDesEnum[libproduct.DurationType(durationType)])
	case libproduct.DurationTypeEnum.Month:
		return fmt.Sprintf("%d个%s", durationValue,
			libproduct.DurationTypeDesEnum[libproduct.DurationType(durationType)])
	case libproduct.DurationTypeEnum.Year:
		return fmt.Sprintf("%d%s", durationValue,
			libproduct.DurationTypeDesEnum[libproduct.DurationType(durationType)])
	default:
	}
	return ""
}

func AddEquity(session *xorm.Session, order *dbo.WebOrder, product *dbp.WebProduct) error {
	vipType := product.VipType
	if ok, _ := OperateEquity(session, &OperateEquityParam{
		UID:           order.UID,
		VipType:       vipType,
		DurationType:  product.DurationType,
		DurationValue: product.DurationValue,
		SourceType:    libuser.VipSourceTypeEnum.System,
		ChangeReason:  libuser.VipChangeReasonEnum.Purchase,
		OperateType:   libuser.VipOperateTypeEnum.Add,
		OrderID:       order.OrderID,
	}); !ok {
		logger.Error("订单完成充值会员失败", order.OrderID)
		return errors.New("订单完成充值会员失败")
	}
	if order.IsRenew != library.Yes && product.HasGift == library.Yes {
		giftList := make([]*sensor.GiftInfoItem, 0)
		if err := json.Unmarshal([]byte(product.GiftInfo), &giftList); err != nil {
			logger.Error("买赠权益Unmarshal失败，", order.OrderID, err.Error())
			return errors.New("买赠权益Unmarshal失败")
		}
		for _, v := range giftList {
			if v.DurationType == 0 || v.DurationValue == 0 {
				continue
			}
			if ok, _ := OperateEquity(session, &OperateEquityParam{
				UID:           order.UID,
				VipType:       libproduct.ProductVipTypeVIP,
				DurationType:  v.DurationType,
				DurationValue: v.DurationValue,
				SourceType:    libuser.VipSourceTypeEnum.System,
				ChangeReason:  libuser.VipChangeReasonEnum.Purchase,
				OperateType:   libuser.VipOperateTypeEnum.Add,
				OrderID:       order.OrderID,
			}); !ok {
				logger.Error("订单完成充值会员失败", order.OrderID)
				return errors.New("订单完成充值会员失败")
			}
		}
		if len(giftList) > 0 {
			order.HasGift = library.Yes
		}
	}
	return nil
}

func SubEquity(session *xorm.Session, webOrder *dbo.WebOrder, productItem *dbp.WebProduct,
	durationType, durationValue int) error {
	vipType := productItem.VipType
	if ok, _ := OperateEquity(session, &OperateEquityParam{
		UID:           webOrder.UID,
		DurationType:  durationType,
		DurationValue: durationValue,
		SourceType:    libuser.VipSourceTypeEnum.System,
		ChangeReason:  libuser.VipChangeReasonEnum.Refund,
		OperateType:   libuser.VipOperateTypeEnum.Sub,
		VipType:       vipType,
	}); !ok {
		return errors.New("订单退款扣减会员失败" + webOrder.OrderID)
	}
	return nil
}

func AddEquityProcess(session *xorm.Session, order *dbo.WebOrder, product *dbp.WebProduct) error {
	err := AddEquity(session, order, product)
	if err != nil {
		return err
	}
	return nil
}

func SubEquityProcess(session *xorm.Session, webOrder *dbo.WebOrder, product *dbp.WebProduct,
	durationType, durationValue int) error {
	err := SubEquity(session, webOrder, product, durationType, durationValue)
	if err != nil {
		return err
	}
	return nil
}
