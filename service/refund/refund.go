package refund

import (
	"encoding/json"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases/order"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/pay"
	libp "gitlab.dailyyoga.com.cn/server/children/library/product"
	"gitlab.dailyyoga.com.cn/server/children/service/equity"
	srvorder "gitlab.dailyyoga.com.cn/server/children/service/order"
	"gitlab.dailyyoga.com.cn/server/children/service/sensor"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type DurationAll struct {
	VipDurationType       int `json:"vip_duration_type"`
	VipDurationValue      int `json:"vip_duration_value"`
	PlanDurationType      int `json:"plan_duration_type"`
	PlanDurationValue     int `json:"plan_duration_value"`
	KegelPlanDurationType int `json:"kegel_plan_duration_type"`
	KegelVipDurationValue int `json:"kegel_vip_duration_value"`
}

type Duration struct {
	DurationType  int
	DurationValue int
	VipType       int
}

func AfterCompleteRefund(refundItem *order.Refund) {
	// 退款成功，则处理权益
	if refundItem.RefundStatus != int(pay.RefundStatusEnum.Success) {
		return
	}
	if refundItem.ChallengeVipType == 0 {
		buff := (util.FormatStartTime(time.Now().Unix()) - util.FormatStartTime(refundItem.CreateTime)) / 86400
		durationAll := DurationAll{}
		if err := json.Unmarshal([]byte(refundItem.OriginalEquity), &durationAll); err != nil {
			logger.Error(err)
		}
		durationList := []Duration{
			{
				DurationType:  durationAll.VipDurationType,
				DurationValue: durationAll.VipDurationValue,
				VipType:       libp.ProductVipTypeVIP,
			},
		}
		for _, v := range durationList {
			if v.DurationType == 0 || v.DurationValue == 0 {
				continue
			}
			durationValue := libp.DurationTypeToDays[libp.DurationType(v.DurationType)] * v.DurationValue
			if buff > 0 {
				durationValue -= int(buff)
			}
			logger.Infof("OperateRefundEquityNoP uid=%d, durationType=%d durationValue=%d vipType=%d", refundItem.UID,
				int(libp.DurationTypeEnum.Day), durationValue, v.VipType)
			if err := equity.OperateRefundEquityNoP(refundItem.UID,
				int(libp.DurationTypeEnum.Day), durationValue,
				v.VipType); err != nil {
				logger.Error(err)
				return
			}
		}
	}
	// 如果要解约
	if refundItem.IsUnsubscribe == library.Yes {
		srvorder.UnsubscribeByOrderID(refundItem.OrderID, pay.UnsubscribeTypeEnum.Admin)
	}

	// 神策上报退款事件
	sensor.ReportRefundOrder(refundItem)
}
