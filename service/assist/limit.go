package assist

import (
	"encoding/json"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	admin "gitlab.dailyyoga.com.cn/server/children/databases/yogacs"
	"gitlab.dailyyoga.com.cn/server/children/library/pay"
)

type limitAssist struct {
}

var LimitAssist limitAssist

type LimitSaveRequest struct {
	YogaRefundLimit             float64 `json:"yoga_refund_limit"`      // 每日瑜伽退款限额
	YogaTerminationLimit        float64 `json:"yoga_termination_limit"` // 每日瑜伽解约限制
	DanceRefundLimit            float64 `json:"dance_refund_limit"`
	DanceTerminationLimit       float64 `json:"dance_termination_limit"`
	HardSweatRefundLimit        float64 `json:"hard_sweat_refund_limit"`
	HardSweatTerminationLimit   float64 `json:"hard_sweat_termination_limit"`
	RefundReviewType            int     `json:"refund_review_type"`
	DelayedPaymentTime          float64 `json:"delayed_payment_time"`
	YogaSpecialRefundLimit      float64 `json:"yoga_special_refund_limit"` // 每日瑜伽退款限额
	DanceSpecialRefundLimit     float64 `json:"dance_special_refund_limit"`
	HardSpecialSweatRefundLimit float64 `json:"hard_special_sweat_refund_limit"`
	OrderTimeLimitDay           int     `json:"order_time_limit_day"`
	AutoRefundReviewUser        int     `json:"auto_refund_review_user"`
}

func (*limitAssist) Get() *LimitSaveRequest {
	item := admin.TbBaseConfig.GetItemByKey(0, pay.AssistLimit)
	if item == nil {
		return nil
	}
	var resp LimitSaveRequest
	err := json.Unmarshal([]byte(item.Value), &resp)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return &resp
}
