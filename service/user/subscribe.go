package user

import (
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/server/children/databases/order"
	dbproduct "gitlab.dailyyoga.com.cn/server/children/databases/product"
	"gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library/pay"
	srvorder "gitlab.dailyyoga.com.cn/server/children/service/order"
)

type SubscribeItem struct {
	ID               int64  `json:"id"`
	Name             string `json:"name"`
	Price            string `json:"price"`
	PayTypeDesc      string `json:"pay_type_desc"`
	PayType          int32  `json:"pay_type"`
	NextChargingDate string `json:"next_charging_date"`
	BusinessType     int32  `json:"business_type"`
}

func GetUserSubscribe(uid int64) []*SubscribeItem {
	list := user.TbWPSubU.GetValidSubscribeUser(uid)
	res := make([]*SubscribeItem, 0)
	for _, v := range list {
		product := dbproduct.TbWebProduct.GetItemByID(int64(v.ProductID))
		if product == nil {
			continue
		}
		if v.OfferType > 1 {
			contract := order.TbAlipayContract.GetItemByContractCode(v.ThirdPartTransactionID)
			if contract != nil {
				srvorder.FormatOffer(contract, true, product)
			}
		}
		item := &SubscribeItem{
			ID:               v.ID,
			Price:            strconv.FormatFloat(product.Price, 'f', 2, 64),
			PayTypeDesc:      pay.PayTypeDesc[v.PayType],
			PayType:          int32(v.PayType),
			NextChargingDate: time.Unix(v.ExpiresTime, 0).Format("2006/01/02"),
			BusinessType:     1,
			Name:             product.Name,
		}
		res = append(res, item)
	}
	return res
}
