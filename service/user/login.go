package user

import (
	"context"
	"crypto/rand"
	"encoding/json"
	"errors"
	"fmt"
	"math/big"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/go-xorm/xorm"

	"gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	dbuser "gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libcache "gitlab.dailyyoga.com.cn/server/children/library/cache"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	libuser "gitlab.dailyyoga.com.cn/server/children/library/user"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/sensor"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/gokit/sensorsdata"
	srvutil "gitlab.dailyyoga.com.cn/server/children/service/util"
)

type Login struct {
	AppClient   *library.AppClient
	AnonymousID string
	LoginType   int // 登录方式
}

var SrvLogin Login

type LoginParams struct {
	LoginType   int    `json:"login_type"`     // 登录方式
	Mobile      string `json:"mobile_phone"`   // 手机号
	UnionID     string `json:"union_id"`       // 微信unionID
	OpenID      string `json:"open_id"`        // 微信openID
	OaID        string `json:"oaid"`           // OAID
	AccessToken string `json:"access_token"`   // 微信token
	Password    string `json:"password"`       // 密码 账号密码登录使用
	Vercode     int    `json:"mobile_vercode"` // 验证码 手机号验证码登录
	LoginToken  string `json:"login_token"`    // 极光一键登录的loginToken，验证后获取手机号
	IOSUniqID   string `json:"ios_uniq_id"`    // IOS 唯一登陆key
	NickName    string `json:"nick_name"`      // 昵称
	AnonymousID string `json:"anonymous_id"`   // 神策匿名ID
	UID         int64  `json:"uid"`            // 如果已经获取了神策ID，客户端会传上来
	SubChannel  string `json:"ft_sub_channel"` // 子渠道
}

type LoginUserDetail struct {
	SID            string         `json:"sid"`
	UID            int64          `json:"uid"`
	IsRegister     bool           `json:"is_register"`
	NickName       string         `json:"nick_name"`
	MemberDuration MemberDuration `json:"member_duration"`
	BindInfo       []BindInfo     `json:"bind_info,omitempty"`
	Avatar         string         `json:"avatar"`
}

// 验证码登录
func (l *Login) LoginVercode(loginParams *LoginParams) (*LoginUserDetail, errorcode.ErrorCode) {
	// 检验验证码是否正确
	if !SrvVercode.VerifyVercode(loginParams.Mobile, loginParams.Vercode, libuser.VercodeTypeLogin) {
		return nil, errorcode.VercodeError
	}
	return l.loginMobile(loginParams.Mobile, loginParams.LoginType)
}

// LoginOneKey 极光一键登录
func (l *Login) LoginOneKey(loginParams *LoginParams) (*LoginUserDetail, errorcode.ErrorCode) {
	if loginParams.LoginToken == "" {
		return nil, errorcode.InvalidParams
	}
	mobile := verifyMobileByToken(loginParams.LoginToken)
	if mobile == "" {
		return nil, errorcode.SystemError
	}
	return l.loginMobile(mobile, loginParams.LoginType)
}

// LoginWechat 微信登录
// nolint
func (l *Login) LoginWechat(loginParams *LoginParams) (*LoginUserDetail, errorcode.ErrorCode) {
	var wxUserInfo *WxUserInfo
	var accountThird *dbuser.AccountThirdAuth
	if l.AppClient.PlanForm == library.PlanFormMiNiAPP {
		wxUserInfo = l.verifyMiNiWxUser(loginParams.AccessToken)
		if wxUserInfo == nil || wxUserInfo.UnionID == "" {
			jsonWxUserInfo, _ := json.Marshal(wxUserInfo)
			fmt.Println("wxUserInfo======1111============", string(jsonWxUserInfo))
			return nil, errorcode.SystemError
		}
		lockKey := fmt.Sprintf("%s%s", libcache.LockTiktokLogin, wxUserInfo.OpenID)
		lock := cache.GetCRedis().Lock(lockKey)
		if !lock {
			return nil, errorcode.SystemError
		}
		defer func() {
			err := cache.GetCRedis().Unlock(lockKey)
			if err != nil {
				logger.Warn(err)
			}
		}()
		if wxUserInfo.NickName == "" {
			wxUserInfo.NickName = l.generateUsername(loginParams.LoginType)
		}
		// 是否已注册
		accountThird = dbuser.TbAccountThirdAuth.GetBindThirdUniqOpenID(wxUserInfo.UnionID, wxUserInfo.OpenID)
	} else {
		wxUserInfo = verifyWxUser(loginParams.AccessToken, loginParams.OpenID)
		if wxUserInfo == nil || wxUserInfo.UnionID == "" || wxUserInfo.UnionID != loginParams.UnionID {
			return nil, errorcode.SystemError
		}
		// 是否已注册
		accountThird = dbuser.TbAccountThirdAuth.GetBindThirdUniqID(wxUserInfo.UnionID, libuser.LoginTypeWechat)
	}
	var account *dbuser.Account
	if accountThird != nil {
		account = dbuser.TbAccount.GetUserByID(accountThird.UID)
		detail, code := l.FormatLoginUserDetail(account, false)
		return detail, code
	}
	session := databases.GetEngineMaster().NewSession()
	defer session.Close()
	if err := session.Begin(); err != nil {
		logger.Error(err)
		return nil, errorcode.DBError
	}
	var err error
	defer func() {
		if err != nil {
			logger.Error(err)
			if err = session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()
	account = l.register(session, &dbuser.Account{
		Nickname:  wxUserInfo.NickName,
		Avatar:    wxUserInfo.HeadImg,
		LoginType: libuser.LoginTypeWechat,
	})
	if account == nil {
		return nil, errorcode.DBError
	}
	accountThird = &dbuser.AccountThirdAuth{
		UID:         account.ID,
		LoginType:   libuser.LoginTypeWechat,
		ThirdUniqID: wxUserInfo.UnionID,
		Nickname:    wxUserInfo.NickName,
		IsBind:      library.Yes,
		ThirdOpenID: wxUserInfo.OpenID,
	}
	if err = accountThird.SaveByTran(session); err != nil {
		logger.Error(err)
		return nil, errorcode.DBError
	}
	if err = session.Commit(); err != nil {
		return nil, errorcode.DBError
	}
	detail, code := l.FormatLoginUserDetail(account, true)
	if l.AppClient.PlanForm == library.PlanFormMiNiAPP {
		// 写入用户属性
		profile := make(map[string]interface{})
		profile["mini_program_channel"] = strconv.Itoa(l.AppClient.Channel)
		sensorsdata.ProfileSet(account.ID, profile, true)
	}
	return detail, code
}

// LoginApple 苹果登录
func (l *Login) LoginApple(loginParams *LoginParams) (*LoginUserDetail, errorcode.ErrorCode) {
	// 是否已注册
	accountThird := dbuser.TbAccountThirdAuth.GetBindThirdUniqID(loginParams.IOSUniqID, libuser.LoginTypeApple)
	var account *dbuser.Account
	if accountThird != nil {
		account = dbuser.TbAccount.GetUserByID(accountThird.UID)
		detail, code := l.FormatLoginUserDetail(account, false)
		return detail, code
	}
	session := databases.GetEngineMaster().NewSession()
	defer session.Close()
	if err := session.Begin(); err != nil {
		logger.Error(err)
		return nil, errorcode.DBError
	}
	var err error
	defer func() {
		if err != nil {
			logger.Error(err)
			if err = session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()
	account = l.register(session, &dbuser.Account{
		Nickname:  loginParams.NickName,
		LoginType: libuser.LoginTypeApple,
	})
	if account == nil {
		err = errors.New("苹果注册用户失败")
		return nil, errorcode.DBError
	}
	accountThird = &dbuser.AccountThirdAuth{
		UID:         account.ID,
		LoginType:   libuser.LoginTypeApple,
		ThirdUniqID: loginParams.IOSUniqID,
		Nickname:    loginParams.NickName,
		IsBind:      library.Yes,
	}
	if err = accountThird.SaveByTran(session); err != nil {
		logger.Error(err)
		return nil, errorcode.DBError
	}
	if err = session.Commit(); err != nil {
		return nil, errorcode.DBError
	}
	detail, code := l.FormatLoginUserDetail(account, true)
	return detail, code
}

func (l *Login) LoginShenCe(loginParams *LoginParams) (*LoginUserDetail, errorcode.ErrorCode) {
	if l.AnonymousID == "" {
		return nil, errorcode.SystemError
	}
	loginParams.NickName = fmt.Sprintf("SC用户_%d", time.Now().Unix())
	// 是否已注册
	accountThird := dbuser.TbAccountThirdAuth.GetBindThirdUniqID(l.AnonymousID, libuser.LoginTypeShenCe)
	var account *dbuser.Account
	if accountThird != nil {
		account = dbuser.TbAccount.GetUserByID(accountThird.UID)
		detail, code := l.FormatLoginUserDetail(account, false)
		return detail, code
	}
	session := databases.GetEngineMaster().NewSession()
	defer session.Close()
	if err := session.Begin(); err != nil {
		logger.Error(err)
		return nil, errorcode.DBError
	}
	var err error
	defer func() {
		if err != nil {
			logger.Error(err)
			if err = session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()
	account = l.register(session, &dbuser.Account{
		Nickname:  loginParams.NickName,
		LoginType: libuser.LoginTypeShenCe,
	})
	if account == nil {
		err = errors.New("神策注册用户失败")
		return nil, errorcode.DBError
	}
	accountThird = &dbuser.AccountThirdAuth{
		UID:         account.ID,
		LoginType:   libuser.LoginTypeShenCe,
		ThirdUniqID: l.AnonymousID,
		Nickname:    loginParams.NickName,
		IsBind:      library.Yes,
	}
	if err = accountThird.SaveByTran(session); err != nil {
		logger.Error(err)
		return nil, errorcode.DBError
	}
	if err = session.Commit(); err != nil {
		return nil, errorcode.DBError
	}
	detail, code := l.FormatLoginUserDetail(account, true)
	return detail, code
}

// FormatLoginUserDetail 格式化
func (l *Login) FormatLoginUserDetail(account *dbuser.Account,
	isRegister bool) (*LoginUserDetail, errorcode.ErrorCode) {
	if account == nil {
		return nil, errorcode.Success
	}
	detail := &LoginUserDetail{
		UID:      account.ID,
		NickName: account.Nickname,
		MemberDuration: MemberDuration{
			StartTime: account.StartTime,
			EndTime:   account.EndTime,
			IsValid:   account.EndTime > time.Now().Unix(),
		},
		IsRegister: isRegister,
		Avatar:     account.Avatar,
	}
	// 非注册的老用户登陆 检查用户的匿名id下面有没有账号，如果有，需要进行数据迁移
	if !isRegister {
		safelygo.GoSafelyByTraceID(func() {
			TransferUserEquity(l.AnonymousID, account.ID)
		})
	}
	detail.BindInfo = GetAccountBindInfo(account)
	return detail, errorcode.Success
}

// loginMobile 通过手机号获取登录信息
func (l *Login) loginMobile(mobile string, loginType int) (*LoginUserDetail, errorcode.ErrorCode) {
	// 是否已注册
	account := dbuser.TbAccount.GetUserByMobile(mobile)
	isRegister := false
	if account == nil {
		session := databases.GetEngineMaster().NewSession()
		defer session.Close()
		if err := session.Begin(); err != nil {
			logger.Error(err)
			return nil, errorcode.DBError
		}
		var err error
		defer func() {
			if err != nil {
				logger.Error(err)
				if err = session.Rollback(); err != nil {
					logger.Error(err)
				}
			}
		}()
		account = l.register(session, &dbuser.Account{
			Nickname:  fmt.Sprintf("手机用户_%d", time.Now().Unix()),
			Mobile:    mobile,
			LoginType: loginType,
		})
		if account == nil {
			err = errors.New("手机号注册用户失败")
			return nil, errorcode.DBError
		}
		if err = session.Commit(); err != nil {
			return nil, errorcode.DBError
		}
		isRegister = true
	}
	detail, code := l.FormatLoginUserDetail(account, isRegister)
	return detail, code
}

// register 账号注册
func (l *Login) register(session *xorm.Session, account *dbuser.Account) *dbuser.Account {
	account.IsRecommend = library.Yes
	account.Gender = libuser.GenderMan
	if account.LoginType != libuser.LoginTypeShenCe && l.AnonymousID != "" {
		anonymousThird := dbuser.TbAccountThirdAuth.GetBindThirdUniqID(l.AnonymousID, libuser.LoginTypeShenCe)
		if anonymousThird != nil {
			oldAcc := dbuser.TbAccount.GetUserByID(anonymousThird.UID)
			if oldAcc != nil && oldAcc.LoginType == libuser.LoginTypeShenCe {
				oldAcc.Nickname = account.Nickname
				oldAcc.Mobile = account.Mobile
				oldAcc.Avatar = account.Avatar
				oldAcc.LoginType = account.LoginType
				anonymousThird.IsBind = library.No
				if err := anonymousThird.UpdateByTran(session); err != nil {
					return nil
				}
				if err := oldAcc.UpdateByTran(session); err != nil {
					return nil
				}
				// 神策上报注册事件 用户属性上报
				sensor.ReportSignUp(oldAcc.ID, oldAcc.LoginType, l.AppClient)
				sensor.ReportSignUpProfile(oldAcc.ID, time.Now().Unix(), l.AppClient)
				return oldAcc
			}
		}
	}
	if err := account.SaveByTran(session); err != nil {
		logger.Error(err)
		return nil
	}
	// 神策上报注册事件 用户属性上报
	sensor.ReportSignUp(account.ID, account.LoginType, l.AppClient)
	sensor.ReportSignUpProfile(account.ID, 0, l.AppClient)
	return account
}

// LoginPassword 账号密码登录
func (l *Login) LoginPassword(loginParams *LoginParams) (*LoginUserDetail, errorcode.ErrorCode) {
	// 是否已注册
	account := dbuser.TbAccount.GetUserByMobile(loginParams.Mobile)
	if account == nil {
		return nil, errorcode.PwdLoginUserNotExist
	}
	if !srvutil.BcryptValidatePwd(account.Password, loginParams.Password) {
		return nil, errorcode.UserPasswdError
	}
	detail, code := l.FormatLoginUserDetail(account, false)
	return detail, code
}

// GenerateSID 生成用户注册的sid
func (l *Login) GenerateSID(uid int64) (string, errorcode.ErrorCode) {
	length := 36
	sid := srvutil.Byte2Md5([]byte(srvutil.GetRandomString(length)))
	cacheKey := fmt.Sprintf("%s%s", libcache.LoginSIDCacheKeyPrefix, sid)
	rclient := cache.GetCRedis().GetClient()
	exp := time.Now().Unix() + 30*86400
	v := fmt.Sprintf("%d_%d", uid, exp)
	_, err := rclient.Set(context.Background(), cacheKey, v,
		libcache.LoginSIDExpirationTime).Result()
	if err != nil {
		logger.Error(err)
		return "", errorcode.SystemError
	}
	if errCode := l.LimitDeviceID(uid, sid); errCode != errorcode.Success {
		return "", errCode
	}
	return sid, errorcode.Success
}

// GenerateSID 生成用户注册的sid
func (l *Login) SIDToUID(sid string) int64 {
	cacheKey := fmt.Sprintf("%s%s", libcache.LoginSIDCacheKeyPrefix, sid)
	rclient := cache.GetCRedis().GetClient()
	ret, err := rclient.Get(context.Background(), cacheKey).Result()
	if err != nil {
		return 0
	}
	if ret == "" {
		return 0
	}
	info := strings.Split(ret, "_")
	num := 2
	if len(info) < num {
		return 0
	}
	uid, err := strconv.ParseInt(info[0], 10, 64)
	if err != nil {
		return 0
	}
	if uid <= 0 {
		return uid
	}
	exp, err := strconv.ParseInt(info[1], 10, 64)
	if exp-time.Now().Unix() <= 7*86400 {
		exp = time.Now().Unix() + 30*3*86400
		v := fmt.Sprintf("%d_%d", uid, exp)
		_, err := rclient.Set(context.Background(), cacheKey, v, libcache.LoginSIDExpirationTime).Result()
		if err != nil {
			logger.Warn(err)
		}
	}
	if err != nil {
		logger.Warn(err)
	}
	return uid
}

// LogoutSID 生成用户注册的sid
func (l *Login) LogoutSID(sid string, uid int64) bool {
	cacheKey := fmt.Sprintf("%s%s", libcache.LoginSIDCacheKeyPrefix, sid)
	rClient := cache.GetCRedis().GetClient()
	ctx := context.Background()
	ret, err := rClient.Del(ctx, cacheKey).Result()
	if ret > 0 && err == nil {
		// 在线设备数中剔除退款登录的
		userOnlineKey := fmt.Sprintf("%s%d", libcache.UserOnlineCacheKeyPrefix, uid)
		rClient.LRem(ctx, userOnlineKey, 1, sid)
		return true
	}
	return false
}

// LogoffAllSID 注销所有sid
func (l *Login) LogoffAllSID(uid int64) bool {
	rdc := cache.GetCRedis().GetClient()
	dataKey := fmt.Sprintf("%s%d", libcache.UserOnlineCacheKeyPrefix, uid)
	ctx := context.Background()
	resultArr, err := rdc.LRange(ctx, dataKey, 0, libuser.UIDBindSidLen).Result()
	if err != nil {
		logger.Warn(err)
		return false
	}
	for _, v := range resultArr {
		cacheKey := fmt.Sprintf("%s%s", libcache.LoginSIDCacheKeyPrefix, v)
		if _, err := rdc.Del(context.Background(), cacheKey).Result(); err == nil {
			logger.Warn(err)
		}
	}
	return true
}

type TokenVerifyMsg struct {
	ID      int64  `json:"id"`
	Code    int64  `json:"code"`
	Content string `json:"content"`
	Phone   string `json:"phone"`
}

// verifyMobileByToken 通过loginToken验证获取
func verifyMobileByToken(loginToken string) string {
	cfg := config.Get().JiGuangKey
	address := libuser.JiGuangLoginVerifyURL
	params := map[string]string{
		"loginToken": loginToken,
	}
	p, err := json.Marshal(params)
	if err != nil {
		return ""
	}
	header := http.Header{}
	header.Add("Content-Type", "application/json")
	header.Add("Authorization", fmt.Sprintf("Basic %s",
		srvutil.B64Encode(cfg.AppKey+":"+cfg.MasterSecret)))
	resp, err := GetLoginClient().DoRequest(address, "POST", header, strings.NewReader(string(p)))
	if err != nil {
		logger.Error(err)
		return ""
	}
	var phoneMsg TokenVerifyMsg
	err = json.Unmarshal(resp, &phoneMsg)
	if err != nil {
		logger.Warn(err)
		return ""
	}
	if phoneMsg.Code != libuser.JiGuangSuccessCode {
		logger.Warn("极光一键登录验证失败", phoneMsg)
		return ""
	}
	// 极光返回是base64加密过的，需要base64解密
	phoneDecode := srvutil.B64Decode(phoneMsg.Phone)
	if phoneDecode == "" {
		return ""
	}
	// rsa 私钥解密
	phoneByte := srvutil.RSADecrypt([]byte(phoneDecode), cfg.PrivateKeyPath)
	return string(phoneByte)
}

// WxUserInfo 微信验证用户信息接口返回值
type WxUserInfo struct {
	ErrorCode int    `json:"errcode"`
	ErrorMsg  string `json:"errmsg"`
	OpenID    string `json:"openid"`
	UnionID   string `json:"unionid"`
	NickName  string `json:"nickname"`
	HeadImg   string `json:"headimgurl"`
	Sex       int    `json:"sex"`
	AppID     string `json:"appid"`
}

// verifyWxUser 微信验证用户信息
func verifyWxUser(token, openID string) *WxUserInfo {
	header := http.Header{}
	header.Add("Content-Type", "application/x-www-form-urlencoded")
	address := fmt.Sprintf(libuser.WechatVerifyURL+
		"?access_token=%s&openid=%s&lang=zh_CN", token, openID)
	resp, err := GetLoginClient().DoRequest(address, "GET", header, nil)
	if err != nil {
		logger.Error(err)
		return nil
	}
	var wxUserInfo WxUserInfo
	err = json.Unmarshal(resp, &wxUserInfo)
	if err != nil {
		logger.Warn(err)
		return nil
	}
	if wxUserInfo.ErrorCode != 0 {
		logger.Warn("微信登录验证失败", wxUserInfo)
		return nil
	}
	return &wxUserInfo
}

// verifyMiNiWxUser 微信验证用户信息
// verifyMiNiWxUser 微信验证用户信息
func (l *Login) verifyMiNiWxUser(jsCode string) *WxUserInfo {
	conf := srvutil.GetWxMiNiAppAccessByAppClient(l.AppClient)
	if conf.AppID == "" || conf.AppSecret == "" {
		return nil
	}
	header := http.Header{}
	header.Add("Content-Type", "application/x-www-form-urlencoded")
	address := fmt.Sprintf(libuser.WechatMiNiVerTokenURL+
		"?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code", conf.AppID, conf.AppSecret, jsCode)
	resp, err := GetLoginClient().DoRequest(address, "GET", header, nil)
	if err != nil {
		logger.Error(err)
		return nil
	}
	var wxUserInfo WxUserInfo
	err = json.Unmarshal(resp, &wxUserInfo)
	if err != nil {
		logger.Warn(err)
		return nil
	}
	if wxUserInfo.ErrorCode != 0 {
		logger.Warn("微信登录验证失败", wxUserInfo)
		return nil
	}
	wxUserInfo.AppID = conf.AppID
	return &wxUserInfo
}

// FindPassword 找回密码
func (l *Login) FindPassword(mobile, passwd string) errorcode.ErrorCode {
	// 是否已注册
	account := dbuser.TbAccount.GetUserByMobile(mobile)
	if account == nil {
		return errorcode.DBError
	}
	account.Password = srvutil.BCryptAndSalt(passwd)
	if err := account.Update(); err != nil {
		return errorcode.DBError
	}
	return errorcode.Success
}

type LogoffAccountReq struct {
	MobileVercode int    `json:"mobile_vercode"`
	UID           int64  `json:"uid"`
	UnionID       string `json:"union_id"`     // 微信unionID
	OpenID        string `json:"open_id"`      // 微信openID
	AccessToken   string `json:"access_token"` // 微信token
	IOSUniqID     string `json:"ios_uniq_id"`  // 苹果唯一id
}

// LogoffAccount 注销账号
func (l *Login) LogoffAccount(params *LogoffAccountReq) errorcode.ErrorCode {
	account := dbuser.TbAccount.GetUserByID(params.UID)
	if account == nil {
		return errorcode.DBError
	}
	thirdList := dbuser.TbAccountThirdAuth.GetThirdBindInfoByUID(params.UID)
	if ecode := l.LogoffVerify(params, thirdList, account); ecode > 0 {
		return ecode
	}
	account.IsLogoff = library.Yes
	err := account.Update()
	if err != nil {
		return errorcode.DBError
	}
	for _, v := range thirdList {
		v.IsBind = library.No
		err = v.Update()
		if err != nil {
			return errorcode.DBError
		}
	}
	return errorcode.Success
}

// LogoffVerify 注销验证
func (l *Login) LogoffVerify(params *LogoffAccountReq,
	thirdList []*dbuser.AccountThirdAuth, account *dbuser.Account) errorcode.ErrorCode {
	verify := false
	if account == nil {
		return errorcode.DBError
	}
	if params.MobileVercode > 0 && account.Mobile != "" {
		// 检验验证码是否正确
		if !SrvVercode.VerifyVercode(account.Mobile, params.MobileVercode, libuser.VercodeTypeLogoff) {
			return errorcode.VercodeError
		}
		verify = true
	}
	if len(thirdList) > 0 {
		wxUser := &WxUserInfo{}
		if params.AccessToken != "" && params.OpenID != "" {
			wxUser = verifyWxUser(params.AccessToken, params.OpenID)
		}
		for _, v := range thirdList {
			if wxUser != nil && v.ThirdUniqID == wxUser.UnionID {
				verify = true
			}
			if v.ThirdUniqID == params.IOSUniqID {
				verify = true
			}
		}
	}
	if !verify {
		return errorcode.ThirdAccountError
	}
	return errorcode.Success
}

// nolint
func (l *Login) LimitDeviceID(uid int64, sid string) errorcode.ErrorCode {
	rdc := cache.GetCRedis().GetClient()
	ctx := context.Background()
	// 记录设备
	safelygo.GoSafelyByTraceID(func() {
		saveDeviceInfo(uid, l.AppClient.DeviceID)
	})
	errCode := IsDeviceLock(uid)
	if errCode != errorcode.Success {
		return errCode
	}
	// 检查用户的设备数是否超过限制
	deviceNum, err := rdc.HLen(ctx, fmt.Sprintf("%s%d", libcache.DeviceLimitCacheKeyPrefix, uid)).Result()
	if err != nil {
		logger.Error("deviceLimitKey HLen err ", err)
		return errorcode.Success
	}
	exist, err := rdc.HExists(ctx, fmt.Sprintf("%s%d",
		libcache.DeviceLimitCacheKeyPrefix, uid),
		l.AppClient.DeviceID).Result()
	if err != nil {
		logger.Error("deviceLimitKey HExists err ", err)
		return errorcode.Success
	}
	if deviceNum >= libuser.UserDeviceLimitLen && !exist {
		// 白名单逻辑
		if whiteUser := dbuser.TbDeviceWhite.GetWhiteUser(uid); whiteUser != nil {
			return errorcode.Success
		}
		// 将用户加入锁定集合
		_, err := rdc.SAdd(ctx, libcache.UIDByDeviceIDLock, uid).Result()
		if err == nil {
			// 记录锁定用户
			safelygo.GoSafelyByTraceID(func() {
				l.saveDeviceIDLockItem(uid)
			})
		}
		//return errorcode.LimitUserHasLock
	}
	// 设备限制处理
	safelygo.GoSafelyByTraceID(func() {
		l.setLimitDeviceID(uid, sid)
	})
	return errorcode.Success
}

// nolint
func IsDeviceLock(uid int64) errorcode.ErrorCode {
	rdc := cache.GetCRedis().GetClient()
	ctx := context.Background()
	isLocked, err := rdc.SIsMember(ctx, libcache.UIDByDeviceIDLock, uid).Result()
	if err != nil {
		logger.Error("LimitDeviceID Locked err ", err)
		return errorcode.Success
	}
	if isLocked {
		// return errorcode.LimitUserHasLock
	}
	return errorcode.Success
}

func (l *Login) setLimitDeviceID(uid int64, sid string) {
	// 白名单逻辑
	if whiteUser := dbuser.TbDeviceWhite.GetWhiteUser(uid); whiteUser != nil {
		return
	}
	rdc := cache.GetCRedis().GetClient()
	ctx := context.Background()
	userOnlineKey := fmt.Sprintf("%s%d", libcache.UserOnlineCacheKeyPrefix, uid)
	// 检查用户的在线设备数是否超过限制
	onlineNum, err := rdc.LLen(ctx, userOnlineKey).Result()
	if err != nil {
		logger.Error("userOnlineKey err ", err)
		return
	}
	if onlineNum >= libuser.UserOlineLimitLen {
		// 从列表的左端弹出一个设备id
		oldSid, err := rdc.LPop(ctx, userOnlineKey).Result()
		if err != nil {
			logger.Error("userOnlineKey err ", err)
			return
		}
		// 使该设备的token失效
		rdc.Del(ctx, fmt.Sprintf("%s%s", libcache.LoginSIDCacheKeyPrefix, oldSid))
	}
	// 将设备信息存入hash
	if l.AppClient.DeviceID != "" {
		rdc.HSet(ctx, fmt.Sprintf("%s%d", libcache.DeviceLimitCacheKeyPrefix, uid), l.AppClient.DeviceID, sid)
	}
	// 将设备id推入列表的右端
	rdc.RPush(ctx, userOnlineKey, sid)
}

func (l *Login) saveDeviceIDLockItem(uid int64) {
	userInfo := dbuser.TbAccount.GetUserByID(uid)
	if userInfo == nil {
		logger.Error("saveDeviceIDLockItem 用户数据未获取到", uid)
		return
	}
	lockUser := dbuser.TbDeviceLock.GetLockUser(uid)
	if lockUser != nil {
		return
	}
	validType := libuser.UserDeviceValidbill
	if userInfo.Mobile != "" {
		validType = libuser.UserDeviceValidMobile
	}
	lock := dbuser.DeviceLock{
		UID:        userInfo.ID,
		Mobile:     userInfo.Mobile,
		ValidType:  int32(validType),
		LockStatus: library.Yes,
	}
	if err := lock.Save(); err != nil {
		logger.Warn(err)
		return
	}
	// 清空所有的有效的sid
	rdc := cache.GetCRedis().GetClient()
	ctx := context.Background()
	for {
		oldSid, err := rdc.LPop(ctx, fmt.Sprintf("%s%d", libcache.UserOnlineCacheKeyPrefix, uid)).Result()
		if err != nil {
			if err == redis.Nil {
				break
			}
			logger.Error("userOnlineKey err uid", uid, err)
			break
		}
		if oldSid == "" {
			break
		}
		// 使该设备的token失效
		rdc.Del(ctx, fmt.Sprintf("%s%s", libcache.LoginSIDCacheKeyPrefix, oldSid))
	}
}

func saveDeviceInfo(uid int64, deviceID string) {
	if deviceID == "" {
		return
	}
	info := dbuser.TbDeviceInfo.GetDeviceInfo(uid, deviceID)
	now := time.Now().Unix()
	if info != nil {
		info.LoginTime = now
		if err := info.Update(); err != nil {
			logger.Warn(err)
			return
		}
		return
	}
	info = &dbuser.DeviceInfo{
		UID:       uid,
		DeviceID:  deviceID,
		LoginTime: now,
	}
	if err := info.Save(); err != nil {
		logger.Warn(err)
	}
}

// LoginDouYin 抖音登录
// nolint
func (l *Login) LoginDouYin(loginParams *LoginParams) (*LoginUserDetail, errorcode.ErrorCode) {
	if loginParams.AccessToken == "" {
		return nil, errorcode.SystemError
	}
	userInfo := l.verifyDYUser(loginParams.AccessToken)
	if userInfo == nil || userInfo.UnionID == "" || userInfo.OpenID == "" {
		return nil, errorcode.SystemError
	}
	lockKey := fmt.Sprintf("%s%s", libcache.LockTiktokLogin, userInfo.OpenID)
	lock := cache.GetCRedis().Lock(lockKey)
	if !lock {
		return nil, errorcode.SystemError
	}
	defer func() {
		err := cache.GetCRedis().Unlock(lockKey)
		if err != nil {
			logger.Warn(err)
		}
	}()
	// 是否已注册
	accountThird := dbuser.TbAccountThirdAuth.GetBindThirdUniqOpenID(userInfo.UnionID, userInfo.OpenID)
	var account *dbuser.Account
	if accountThird != nil {
		account = dbuser.TbAccount.GetUserByID(accountThird.UID)
		if accountThird.ThirdOpenID == "" {
			accountThird.ThirdOpenID = userInfo.OpenID
			if err := accountThird.Update(); err != nil {
				return nil, errorcode.DBError
			}
		}
		detail, code := l.FormatLoginUserDetail(account, false)
		if err := l.setCode2Session(accountThird.UID, &Code2Session{
			OpenID:     userInfo.OpenID,
			UnionID:    userInfo.UnionID,
			SessionKey: userInfo.SessionKey,
			AppID:      userInfo.AppID,
		}); err != nil {
			logger.Error("setCode2Session", err)
		}
		return detail, code
	}
	session := databases.GetEngineMaster().NewSession()
	defer session.Close()
	if err := session.Begin(); err != nil {
		logger.Error(err)
		return nil, errorcode.DBError
	}
	var err error
	defer func() {
		if err != nil {
			logger.Error(err)
			if err = session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()
	account = l.register(session, &dbuser.Account{
		Nickname:  l.generateUsername(libuser.LoginTypeTikTok),
		Avatar:    "",
		LoginType: libuser.LoginTypeTikTok,
	})
	if account == nil {
		return nil, errorcode.DBError
	}
	accountThird = &dbuser.AccountThirdAuth{
		UID:         account.ID,
		LoginType:   libuser.LoginTypeTikTok,
		ThirdUniqID: userInfo.UnionID,
		ThirdOpenID: userInfo.OpenID,
		IsBind:      library.Yes,
	}
	if err = accountThird.SaveByTran(session); err != nil {
		logger.Error(err)
		return nil, errorcode.DBError
	}
	if err = session.Commit(); err != nil {
		return nil, errorcode.DBError
	}
	if err = l.setCode2Session(accountThird.UID, &Code2Session{
		OpenID:     userInfo.OpenID,
		UnionID:    userInfo.UnionID,
		SessionKey: userInfo.SessionKey,
		AppID:      userInfo.AppID,
	}); err != nil {
		logger.Error("setCode2Session", err)
	}
	// 写入用户属性
	profile := make(map[string]interface{})
	profile["mini_program_channel"] = strconv.Itoa(l.AppClient.Channel)
	sensorsdata.ProfileSet(account.ID, profile, true)
	detail, code := l.FormatLoginUserDetail(account, true)
	return detail, code
}

const maxInt = 900

func (l *Login) generateUsername(loginType int) string {
	// 生成时间戳
	currentTime := time.Now().Unix()
	randomNumber, _ := rand.Int(rand.Reader, big.NewInt(maxInt))
	randomInt := randomNumber.Int64() + 100
	name := "用户"
	switch loginType {
	case libuser.LoginTypeTikTok:
		name = "抖音用户"
	case libuser.LoginTypeWechat:
		name = "微信用户"
	}
	// 结合时间戳和随机数生成用户名
	username := fmt.Sprintf("%s_%d_%d", name, currentTime, randomInt)
	return username
}

// DouYinInfo 用户信息接口返回值
type DouYinInfo struct {
	ErrorNo   int            `json:"err_no"`
	ErrorTips string         `json:"err_tips"`
	Data      DouYinInfoItem `json:"data"`
}
type DouYinInfoItem struct {
	OpenID          string `json:"openid"`
	UnionID         string `json:"unionid"`
	SessionKey      string `json:"session_key"`
	AnonymousOpenID string `json:"anonymous_openid"`
	AppID           string `json:"-"`
}

// verifyDYUser 抖音验证用户信息
func (l *Login) verifyDYUser(token string) *DouYinInfoItem {
	conf := srvutil.GetTikTokAccessByAppClient(l.AppClient)
	header := http.Header{}
	header.Add("Content-Type", "application/json")
	p, err := json.Marshal(struct {
		AppID  string `json:"appid"`
		Secret string `json:"secret"`
		Code   string `json:"code"`
	}{
		AppID:  conf.AppID,
		Secret: conf.AppSecret,
		Code:   token,
	})
	if err != nil {
		logger.Error(err)
		return nil
	}
	resp, err := GetLoginClient().DoRequest(libuser.DouYinVerifyTokenURL,
		"POST", header, strings.NewReader(string(p)))
	if err != nil {
		logger.Error(err)
		return nil
	}
	var userInfo DouYinInfo
	err = json.Unmarshal(resp, &userInfo)
	if err != nil {
		logger.Warnf("抖音返回值:%s err:%s", string(resp), err.Error())
		return nil
	}
	if userInfo.ErrorNo != 0 {
		logger.Warn("抖音登录验证失败", userInfo)
		return nil
	}
	userInfo.Data.AppID = conf.AppID
	return &userInfo.Data
}

type Code2Session struct {
	OpenID     string `json:"openid"`
	UnionID    string `json:"unionid"`
	SessionKey string `json:"session_key"`
	AppID      string `json:"app_id"`
}

func (l *Login) setCode2Session(uid int64, code2Session *Code2Session) error {
	cacheData, err := json.Marshal(code2Session)
	if err != nil {
		return err
	}
	safelygo.GoSafelyByTraceID(func() {
		RecordAccountThirdMoreAppID(&dbuser.AccountThirdMoreAppID{
			ThirdUniqID: code2Session.UnionID,
			ThirdOpenID: code2Session.OpenID,
			AppID:       code2Session.AppID,
			SessionKey:  code2Session.SessionKey,
			LoginType:   int32(l.LoginType),
		})
	})
	cache.GetCRedis().Storage(libcache.TiktokCode2Session+strconv.Itoa(int(uid)),
		string(cacheData), 72*time.Hour) //nolint
	return nil
}

// RecordAccountThirdMoreAppID 存储第三方信息
func RecordAccountThirdMoreAppID(item *dbuser.AccountThirdMoreAppID) {
	lockKey := fmt.Sprintf("acc:more:appid:%s", item.ThirdOpenID)
	lock := cache.GetCRedis().Lock(lockKey, cache.LockExpiration(time.Minute))
	if !lock {
		return
	}
	defer func() {
		_ = cache.GetCRedis().Unlock(lockKey)
	}()
	data := dbuser.TbMoreAppID.GetUserByThirdOpenID(item.ThirdUniqID, item.ThirdOpenID, item.AppID)
	if data == nil {
		if err := item.Save(); err != nil {
			logger.Errorf("RecordAccountThirdMoreAppID err:%s", err.Error())
		}
		return
	}
	if item.SessionKey != "" {
		data.SessionKey = item.SessionKey
		if err := data.Update(); err != nil {
			logger.Errorf("RecordAccountThirdMoreAppID err:%s", err.Error())
		}
	}
}
