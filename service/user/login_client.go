package user

import (
	"context"
	"fmt"
	"io"
	"net"
	"net/http"
	"strings"
	"sync"
	"time"
)

type LoginClient struct {
	c *http.Client
}

var lc *LoginClient
var loginClientOnce sync.Once

func GetLoginClient() *LoginClient {
	loginClientOnce.Do(func() {
		httpTransport := &http.Transport{
			DialContext: (&net.Dialer{
				Timeout:   30 * time.Second,
				KeepAlive: 30 * time.Second,
			}).DialContext,
			MaxIdleConns:          10,
			MaxIdleConnsPerHost:   10,
			MaxConnsPerHost:       100,
			IdleConnTimeout:       60 * time.Second,
			TLSHandshakeTimeout:   10 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,
		}
		lc = &LoginClient{
			c: &http.Client{
				Timeout:   30 * time.Second,
				Transport: httpTransport,
			}}
	})
	return lc
}

// PostForm 请求
func (l *LoginClient) DoRequest(url, method string, header http.Header, body io.Reader) ([]byte, error) {
	if url == "" || method == "" || header == nil {
		return nil, fmt.Errorf("请检查请求参数")
	}
	method = strings.ToUpper(method)
	r, err := http.NewRequestWithContext(context.Background(), method, url, body)
	if err != nil {
		return nil, err
	}
	r.Close = true
	r.Header = header
	resp, err := l.c.Do(r)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	return io.ReadAll(resp.Body)
}
