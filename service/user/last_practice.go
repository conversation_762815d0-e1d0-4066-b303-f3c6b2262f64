package user

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/go-redis/redis/v8"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libc "gitlab.dailyyoga.com.cn/server/children/library/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
)

type LastPractice struct{}

var SrvLastPractice LastPractice

type LastPracticeItem struct {
	CourseID          int64    `json:"course_id"`
	PracticeStartTime int64    `json:"practice_start_time"`
	SceneType         int32    `json:"scene_type"`
	PracticeLabels    []string `json:"practice_labels,omitempty"`
	SceneTypeID       int64    `json:"scene_type_id"`
	SceneTypeIDIndex  int64    `json:"scene_type_index"`
}

const MaxLastPracticeCourse = 20

// nolint
func (*LastPractice) UpdateUserLastPractice(param *PracticeReportParam) {
	rc := cache.GetCRedis()
	ctx := context.Background()
	cacheKey := fmt.Sprintf("%s:%d", libc.UserLastPracticeInfo, param.UID)
	current, err := rc.GetClient().ZRange(ctx, cacheKey, 0, -1).Result()
	if err != nil && err != redis.Nil {
		logger.Error(err)
	}
	logger.Info("当前最近练习数量", param.UID, current)
	exist := false
	for _, v := range current {
		item := &LastPracticeItem{}
		err := json.Unmarshal([]byte(v), item)
		if err != nil {
			logger.Error(err)
			continue
		}
		// 如果已存在，将对应数据删除，然后插入新数据
		if item.CourseID == param.CourseID {
			// 上报数据晚于最新的数据
			if item.PracticeStartTime > param.PracticeStartTime {
				return
			}
			exist = true
			remInt, err := rc.GetClient().ZRem(ctx, cacheKey, v).Result()
			if remInt <= 0 || err != nil {
				logger.Error("最近练习删除重复数据失败", cacheKey, v)
				continue
			}
		}
	}
	if param.PracticeLabelsStr != "" {
		practiceLabels := strings.Split(param.PracticeLabelsStr, ",")
		if len(practiceLabels) > 0 {
			param.PracticeLabels = make([]string, len(practiceLabels))
			for i, v := range practiceLabels {
				param.PracticeLabels[i] = v
			}
		}
	}
	item := &LastPracticeItem{
		CourseID:          param.CourseID,
		PracticeStartTime: param.PracticeStartTime,
		SceneType:         int32(param.SceneType),
		PracticeLabels:    param.PracticeLabels,
		SceneTypeID:       param.SceneTypeID,
	}
	itemByte, _ := json.Marshal(item)
	addItem := &redis.Z{
		Score:  float64(param.PracticeStartTime),
		Member: string(itemByte),
	}
	addNum, _ := rc.GetClient().ZAdd(ctx, cacheKey, addItem).Result()
	if addNum <= 0 {
		return
	}
	// 有效期180天
	rc.GetClient().Expire(ctx, cacheKey, 180*24*time.Hour)
	// 大于20个 删除
	if !exist && len(current) >= MaxLastPracticeCourse {
		delNum := len(current) + 1 - MaxLastPracticeCourse
		rc.GetClient().ZRemRangeByRank(ctx, cacheKey, 0, int64(delNum-1))
	}
}

func (*LastPractice) GetUserLastPractice(uid int64) []*LastPracticeItem {
	rc := cache.GetCRedis()
	ctx := context.Background()
	cacheKey := fmt.Sprintf("%s:%d", libc.UserLastPracticeInfo, uid)
	current, err := rc.GetClient().ZRange(ctx, cacheKey, 0, -1).Result()
	if err != nil && err != redis.Nil {
		logger.Error(err)
	}
	res := make([]*LastPracticeItem, 0)
	for _, v := range current {
		item := &LastPracticeItem{}
		err := json.Unmarshal([]byte(v), item)
		if err != nil {
			logger.Error(err)
			continue
		}
		if item.CourseID == 0 {
			continue
		}
		res = append(res, item)
		if len(res) >= MaxLastPracticeCourse {
			break
		}
	}
	sort.Slice(res, func(i, j int) bool {
		return res[i].PracticeStartTime > res[j].PracticeStartTime
	})
	return res
}

func (*LastPractice) GetLastPracticeCount(uid int64, appClient *library.AppClient) int64 {
	rc := cache.GetCRedis()
	ctx := context.Background()
	cacheKey := fmt.Sprintf("%s:%d", libc.UserLastPracticeInfo, uid)
	count, err := rc.GetClient().ZCard(ctx, cacheKey).Result()
	if err != nil && err != redis.Nil {
		logger.Error(err)
	}
	return count
}
