package user

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/crypto"
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/protogen/children-user-group-go/childrenusergroup"
	"gitlab.dailyyoga.com.cn/protogen/moderation-go"
	proto "gitlab.dailyyoga.com.cn/protogen/srv-kafka-go"
	"gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	dbcollect "gitlab.dailyyoga.com.cn/server/children/databases/collect"
	"gitlab.dailyyoga.com.cn/server/children/databases/course"
	"gitlab.dailyyoga.com.cn/server/children/databases/iap"
	"gitlab.dailyyoga.com.cn/server/children/databases/order"
	"gitlab.dailyyoga.com.cn/server/children/databases/practice"
	dbuser "gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/databases/usersetting"
	"gitlab.dailyyoga.com.cn/server/children/grpc"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libcache "gitlab.dailyyoga.com.cn/server/children/library/cache"
	lib "gitlab.dailyyoga.com.cn/server/children/library/collect"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	"gitlab.dailyyoga.com.cn/server/children/library/kafka"
	"gitlab.dailyyoga.com.cn/server/children/library/product"
	libuser "gitlab.dailyyoga.com.cn/server/children/library/user"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	srvcourse "gitlab.dailyyoga.com.cn/server/children/service/course"
	"gitlab.dailyyoga.com.cn/server/children/service/equity"
	"gitlab.dailyyoga.com.cn/server/children/service/sensor"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
	"gitlab.dailyyoga.com.cn/server/h2-artifact/uuid"
)

type suser struct{}

var SrvUser suser

type AttributeInfo struct {
	SID                string         `json:"sid"`
	UID                int64          `json:"uid"`
	UUID               string         `json:"uuid"`
	NickName           string         `json:"nick_name"`
	Gender             int            `json:"gender"`
	Avatar             string         `json:"avatar"`
	PracticeMinutes    int            `json:"practice_minutes"`
	PracticeCount      int            `json:"practice_count"`
	PracticeCalories   int            `json:"practice_calories"`
	PracticeDays       int            `json:"practice_days"`
	CollectCourseCount int            `json:"collect_course_count"`
	LastPracticeCount  int64          `json:"last_practice_count"`
	IsRecommend        bool           `json:"is_recommend"`
	RegisterTime       int64          `json:"register_time"`
	RegisterDays       int64          `json:"register_days"`
	MemberDuration     MemberDuration `json:"member_duration,omitempty"`
	IsFirstBuy         bool           `json:"is_first_buy"`
	BindInfo           []BindInfo     `json:"bind_info,omitempty"`
	RealMobile         string         `json:"real_mobile,omitempty"`
	Weight             int            `json:"weight"`
	PracticeCourseNum  int            `json:"practice_course_num"`
	HasPasswd          bool           `json:"has_passwd"`
	Height             int            `json:"height"`
	Age                int            `json:"age"`
}

type BindInfo struct {
	LoginType int    `json:"login_type"`
	Name      string `json:"name"`
}

type MemberDuration struct {
	StartTime      int64 `json:"start_time"`
	EndTime        int64 `json:"end_time"`
	IsValid        bool  `json:"is_valid"`
	PermanentlyVip int   `json:"permanently_vip"`
}

type VoiceDuration struct {
	StartTime int64 `json:"start_time"`
	EndTime   int64 `json:"end_time"`
	IsValid   bool  `json:"is_valid"`
}

type KegelDuration struct {
	StartTime int64 `json:"start_time"`
	EndTime   int64 `json:"end_time"`
	IsValid   bool  `json:"is_valid"`
}

type PlanMemberDuration struct {
	StartTime int64 `json:"start_time"`
	EndTime   int64 `json:"end_time"`
	IsValid   bool  `json:"is_valid"`
}

type IntegralWallSubmitReq struct {
	IP       string `json:"ip"`
	OS       string `json:"os"`
	Device   string `json:"device"`
	Idfa     string `json:"idfa"`
	AppID    string `json:"appid"`
	Keywords string `json:"keywords"`
}

type IntegralWallReportReq struct {
	OS     string `json:"os"`
	Device string `json:"device"`
	Idfa   string `json:"idfa"`
}

// nolint
func (suser) GetUserAttribute(uid int64, attributes []string, appClient *library.AppClient) *AttributeInfo {
	account := dbuser.TbAccount.GetUserByID(uid)
	if account == nil {
		return nil
	}
	res := &AttributeInfo{
		UID:          account.ID,
		UUID:         uuid.IdToShortKey(account.ID),
		NickName:     account.Nickname,
		Gender:       account.Gender,
		RegisterTime: account.CreateTime,
		RegisterDays: (util.FormatStartTime(time.Now().Unix()) -
			util.FormatStartTime(account.CreateTime)) / util.SecondsPerDay,
		Avatar:             account.Avatar,
		PracticeMinutes:    account.Minutes,
		PracticeCalories:   account.Calories,
		PracticeDays:       account.PracticeDays,
		PracticeCount:      account.PracticeCount,
		CollectCourseCount: account.CollectCourseCount,
		IsRecommend:        util.ToBool(account.IsRecommend),
		MemberDuration: MemberDuration{
			StartTime: account.StartTime,
			EndTime:   account.EndTime,
			IsValid:   account.EndTime > time.Now().Unix(),
		},
		RealMobile:        account.Mobile,
		PracticeCourseNum: GetPracticeCourseNum(uid, account.CreateTime, true),
	}
	if len(res.RealMobile) == libuser.MobileLen {
		res.RealMobile = res.RealMobile[0:3] + "****" + res.RealMobile[7:]
	}
	if account.Password != "" {
		res.HasPasswd = true
	}
	partInformation := GetUserPartInformation(uid)
	if partInformation != nil {
		res.Age = partInformation.Age
		res.Height = partInformation.Height
		res.Weight = partInformation.Weight
	}
	if res.Age == 0 {
		res.Age = 8
	}
	if res.Height == 0 {
		res.Height = 120
	}
	if res.Weight == 0 {
		res.Weight = 30
	}
	if len(attributes) == 0 {
		return res
	}
	list := GetUserCourseCollectListAll(res.UID)
	res.CollectCourseCount = len(course.TbCourseLibrary.GetListByIDArrNotImport(list))
	var wg sync.WaitGroup
	for _, attr := range attributes {
		wg.Add(1)
		attrTmp := attr
		safelygo.GoSafelyByTraceID(
			func() {
				defer wg.Done()
				switch attrTmp {
				case libuser.AttributeBindInfo:
					res.BindInfo = GetAccountBindInfo(account)
				case libuser.AttributeLastPracticeCount:
					res.LastPracticeCount = SrvLastPractice.GetLastPracticeCount(account.ID, appClient)
				case libuser.OBChoiceInfo:
					obChoice := GetObChoiceInfo(uid)
					// 个人资料内的体重优先获取
					if res.Weight < 0 {
						res.Weight = obChoice.Weight
					}
				case libuser.AttributePermanentlyVip:
					res.MemberDuration.PermanentlyVip = library.No
					if IsPermanentlyVip(account) {
						res.MemberDuration.PermanentlyVip = library.Yes
					}
				default:
				}
			})
	}
	wg.Wait()
	// 更新用户标签
	safelygo.GoSafelyByTraceID(
		func() {
			SrvLabel.CalcUserLabel(uid, appClient)
		})
	return res
}

func GetUserCourseCollectListAll(uid int64) []int64 {
	collects := dbcollect.TbCollect.GetListAll(uid, int(lib.ResourceTypeEnum.Course))
	ids := make([]int64, 0)
	for _, collect := range collects {
		ids = append(ids, collect.ResourceID)
	}
	return ids
}

// nolint
func GetPracticeCourseNum(uid, startTime int64, isCache bool) int {
	rd := cache.GetCRedis().GetClient()
	cacheKey := libcache.UniqueCourseIDs + fmt.Sprintf("%d", uid)
	if isCache {
		cacheNum := rd.Get(context.Background(), cacheKey).Val()
		if cacheNum != "" && cacheNum != "0" {
			numResult, _ := strconv.Atoi(cacheNum)
			return numResult
		}
	}
	// 查询练习过的课程
	endTime := time.Now().Unix()
	list := practice.TbPracticeLog.GetLogByRange(uid, startTime, endTime, 1, 100)
	if len(list) == 0 {
		return 0
	}
	// 使用 map 存储不重复的 CourseID
	uniqueCourseIDs := make(map[int64]bool)
	for _, log := range list {
		uniqueCourseIDs[log.CourseID] = true
	}
	num := len(uniqueCourseIDs)
	if err := rd.Set(context.Background(), libcache.UniqueCourseIDs+fmt.Sprintf("%d", uid), num,
		time.Hour*24*library.Const30).Err(); err != nil {
		logger.Error("设置练习课程失败, ", uid, err.Error())
	}
	return num
}

func GetObChoiceInfo(uid int64) *srvcourse.ObUserChoice {
	res := &srvcourse.ObUserChoice{}
	obProgram := course.TbObProgram.GetInProcessProgram(uid)
	if obProgram == nil {
		return res
	}
	err := json.Unmarshal([]byte(obProgram.ChoiceData), res)
	if err != nil {
		return res
	}
	return res
}

// GetAccountBindInfo 获取账号的绑定信息
func GetAccountBindInfo(account *dbuser.Account) []BindInfo {
	res := make([]BindInfo, 0)
	if account.Mobile != "" {
		item := BindInfo{
			LoginType: libuser.LoginTypePassword,
			Name:      account.Mobile,
		}
		item.Name = item.Name[0:3] + "****" + item.Name[7:]
		res = append(res, item)
	}
	thirdList := dbuser.TbAccountThirdAuth.GetThirdBindInfoByUID(account.ID)
	for _, third := range thirdList {
		if third.IsBind != library.Yes {
			continue
		}
		res = append(res, BindInfo{
			LoginType: third.LoginType,
			Name:      third.Nickname,
		})
	}
	return res
}

type BindWechatReq struct {
	SID         string `json:"sid"`
	UID         int64  `json:"uid"`
	UnionID     string `json:"union_id"`     // 微信unionID
	OpenID      string `json:"open_id"`      // 微信openID
	AccessToken string `json:"access_token"` // 微信token
}

// BindAccountWechat 绑定微信
func BindAccountWechat(params *BindWechatReq) errorcode.ErrorCode {
	accout := dbuser.TbAccount.GetUserByID(params.UID)
	if accout == nil {
		return errorcode.DBError
	}
	wxUser := verifyWxUser(params.AccessToken, params.OpenID)
	if wxUser == nil || wxUser.UnionID == "" || wxUser.UnionID != params.UnionID {
		return errorcode.SystemError
	}
	third := dbuser.TbAccountThirdAuth.GetBindThirdUniqID(wxUser.UnionID, libuser.LoginTypeWechat)
	if third != nil {
		if third.UID == params.UID {
			return errorcode.Success
		}
		return errorcode.WechatHasBindOther
	}
	ata := dbuser.TbAccountThirdAuth.GetBindThirdInfoByUID(params.UID, libuser.LoginTypeWechat)
	if ata != nil {
		return errorcode.Success
	}
	if third == nil {
		third = &dbuser.AccountThirdAuth{
			UID:         accout.ID,
			Nickname:    wxUser.NickName,
			ThirdUniqID: wxUser.UnionID,
			LoginType:   libuser.LoginTypeWechat,
			IsBind:      library.Yes,
		}
		if err := third.Save(); err != nil {
			logger.Error(err)
			return errorcode.DBError
		}
	}
	return errorcode.Success
}

type BindAppleReq struct {
	SID       string `json:"sid"`
	UID       int64  `json:"uid"`
	IOSUniqID string `json:"ios_uniq_id"` // IOS 唯一登陆key
	NickName  string `json:"nick_name"`   // 昵称
}

// BindAccountApple 绑定苹果
func BindAccountApple(params *BindAppleReq) errorcode.ErrorCode {
	accout := dbuser.TbAccount.GetUserByID(params.UID)
	if accout == nil {
		return errorcode.DBError
	}
	third := dbuser.TbAccountThirdAuth.GetBindThirdUniqID(params.IOSUniqID, libuser.LoginTypeApple)
	if third != nil && third.IsBind == library.Yes {
		if third.UID == params.UID {
			return errorcode.Success
		}
		return errorcode.AppleHasBindOther
	}
	ata := dbuser.TbAccountThirdAuth.GetBindThirdInfoByUID(params.UID, libuser.LoginTypeApple)
	if ata != nil && ata.IsBind == library.Yes {
		return errorcode.Success
	}
	if third == nil {
		third = &dbuser.AccountThirdAuth{
			UID:         accout.ID,
			Nickname:    params.NickName,
			ThirdUniqID: params.IOSUniqID,
			LoginType:   libuser.LoginTypeApple,
			IsBind:      library.Yes,
		}
		if err := third.Save(); err != nil {
			logger.Error(err)
			return errorcode.DBError
		}
	}
	third.UID = accout.ID
	third.IsBind = library.Yes
	if err := third.Update(); err != nil {
		logger.Error(err)
		return errorcode.DBError
	}
	return errorcode.Success
}

type BindOneKeyReq struct {
	SID        string `json:"sid"`
	UID        int64  `json:"uid"`
	LoginToken string `json:"login_token"` // 极光一键登录的loginToken，验证后获取手机号
}

// BindAccountOneKey 一键登录绑定手机号
func BindAccountOneKey(params *BindOneKeyReq) errorcode.ErrorCode {
	accout := dbuser.TbAccount.GetUserByID(params.UID)
	if accout == nil {
		return errorcode.DBError
	}
	mobile := verifyMobileByToken(params.LoginToken)
	if mobile == "" {
		return errorcode.SystemError
	}
	accountMobile := dbuser.TbAccount.GetUserByMobile(mobile)
	if accountMobile != nil && accountMobile.ID != accout.ID {
		return errorcode.PhoneHasBindUser
	}
	accout.Mobile = mobile
	if err := accout.Update(); err != nil {
		return errorcode.DBError
	}
	return errorcode.Success
}

// LimitModifyRate 用户更新信息接口
func LimitModifyRate(uid int64, gender int, nickName, avatar string) bool {
	if gender == 0 && nickName == "" && avatar == "" {
		return true
	}
	cacheKey := libcache.ModifyInfoLimitRate + strconv.FormatInt(uid, 10)
	client := cache.GetCRedis().GetClient()
	ret, err := client.Incr(context.Background(), cacheKey).Result()
	if err != nil {
		return false
	}
	if ret > libuser.ModifyInfoLimitPerMonth {
		return false
	}
	exp := util.GetLastDateOfMonth(time.Now().Unix()) - time.Now().Unix()
	ok, err := client.Expire(context.Background(), cacheKey, time.Duration(exp)*time.Second).Result()
	if err != nil {
		return false
	}
	if !ok {
		return false
	}
	return true
}

const SensitiveCode = "block"

// SensitiveWordCheck 敏感词检查
func SensitiveWordCheck(text []string) bool {
	textByte, err := json.Marshal(text)
	if err != nil {
		return false
	}
	client := grpc.GetModerationClient()
	resp, err := client.TextModeration(context.Background(), &moderation.TModerationRequest{
		Content: string(textByte),
	})
	if err != nil {
		logger.Warn(err)
		return false
	}
	if resp.GetResultCode() != errorcode.ServiceResultSuccess {
		return false
	}
	if !resp.GetVerify() {
		for _, v := range resp.GetList() {
			if v.Suggestion == SensitiveCode {
				return false
			}
		}
	}
	return true
}

// SensitiveImageCheck 敏感图片检查
func SensitiveImageCheck(image []string) bool {
	imageByte, err := json.Marshal(image)
	if err != nil {
		return false
	}
	client := grpc.GetModerationClient()
	resp, err := client.ImageModeration(context.Background(), &moderation.IModerationRequest{
		Content: string(imageByte),
		Scene:   1,
	})
	if err != nil {
		logger.Warn(err)
		return false
	}
	if resp.GetResultCode() != errorcode.ServiceResultSuccess {
		return false
	}
	if !resp.GetVerify() {
		for _, v := range resp.GetList() {
			if v.Suggestion == SensitiveCode {
				return false
			}
		}
	}
	return true
}

type TransferUserEquityReq struct {
	DistinctID  string
	DistinctUID int64
	LoginUID    int64
	LogoutFlag  bool
}

// TransferUserEquity 权益迁移
// nolint
func TransferUserEquity(anonymousID string, loginUID int64) {
	if anonymousID == "" || loginUID == 0 {
		return
	}
	logger.Info("权益转移开始", anonymousID, loginUID)
	aThird := dbuser.TbAccountThirdAuth.GetBindThirdUniqID(anonymousID, libuser.LoginTypeShenCe)
	if aThird == nil {
		logger.Info("权益转移:无神策账号", anonymousID, libuser.LoginTypeShenCe)
		return
	}
	account := dbuser.TbAccount.GetUserByID(aThird.UID)
	// 神策ID关联的账号类型为神策注册，且uid与当前登录的uid不同
	if account == nil || account.LoginType != libuser.LoginTypeShenCe ||
		account.ID == loginUID || account.Mobile != "" {
		logger.Info("权益转移:账号无效，不转移", anonymousID, libuser.LoginTypeShenCe)
		return
	}
	if transferLog := dbuser.TbUserEquityTransfer.GetItemByDUID(account.ID); transferLog != nil {
		logger.Info("权益转移:已经转移过，不转移", aThird.UID, loginUID)
		return
	}
	cacheKey := libcache.LockEquityTransferDistinct + strconv.FormatInt(aThird.UID, 10)
	client := cache.GetCRedis()
	expireNum := 5
	if !client.Lock(cacheKey, cache.LockExpiration(time.Duration(expireNum)*time.Minute)) {
		logger.Info("权益转移:加锁失败", aThird.UID, loginUID)
		return
	}
	defer client.Unlock(cacheKey)
	session := databases.GetEngineMaster().NewSession()
	defer session.Close()
	if err := session.Begin(); err != nil {
		logger.Error(err)
		return
	}
	var err error
	defer func() {
		if err != nil {
			logger.Error(err)
			if err = session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()

	td := &TransferUserEquityReq{
		DistinctID:  anonymousID,
		DistinctUID: aThird.UID,
		LoginUID:    loginUID,
	}
	if err = td.TransferOrder(session); err != nil {
		return
	}
	if err = td.transferEquity(session); err != nil {
		return
	}
	if !td.LogoutFlag {
		logger.Info("权益转移:无需转移", anonymousID, loginUID)
		return
	}
	transferItem := dbuser.EquityTransfer{
		DUID: aThird.UID,
		UID:  loginUID,
	}
	if err = transferItem.Save(); err != nil {
		return
	}
	aThird.IsBind = library.No
	if err = aThird.UpdateByTran(session); err != nil {
		return
	}
	if account.EndTime > 0 {
		account.EndTime = time.Now().Unix()
	}
	account.IsLogoff = library.Yes
	if err = account.UpdateByTran(session); err != nil {
		return
	}
	safelygo.GoSafelyByTraceID(func() {
		srvcourse.CleanUserObProgram(account.ID)
	})
	if err := session.Commit(); err != nil {
		return
	}
}

func (td *TransferUserEquityReq) TransferOrder(session *xorm.Session) error {
	wList := order.TbWebOrder.GetAllListByUID(td.DistinctUID)
	if len(wList) == 0 {
		return nil
	}
	td.LogoutFlag = true
	for _, v := range wList {
		v.UID = td.LoginUID
		if err := v.UpdateByTran(session); err != nil {
			return err
		}
	}
	if err := td.transferIapOrder(session); err != nil {
		return err
	}
	if err := td.transferSubscribeOrder(session); err != nil {
		return err
	}
	if err := td.transferRefundOrder(session); err != nil {
		return err
	}
	return nil
}

func (td *TransferUserEquityReq) transferEquity(session *xorm.Session) error {
	distinctAccount := dbuser.TbAccount.GetUserByID(td.DistinctUID)
	if distinctAccount == nil {
		return errors.New("错误的神策UID,无对应用户信息" + strconv.FormatInt(td.DistinctUID, 10))
	}
	if distinctAccount.EndTime <= time.Now().Unix() {
		return nil
	}
	expireDays := (distinctAccount.EndTime - time.Now().Unix()) / 86400
	if expireDays <= 0 {
		return nil
	}
	td.LogoutFlag = true
	ok, _ := equity.OperateEquity(session, &equity.OperateEquityParam{
		UID:           td.LoginUID,
		DurationType:  int(product.DurationTypeEnum.Day),
		DurationValue: int(expireDays),
		SourceType:    libuser.VipSourceTypeEnum.System,
		ChangeReason:  libuser.VipChangeReasonEnum.TransferEquity,
		OperateType:   libuser.VipOperateTypeEnum.Add,
	})
	if !ok {
		return errors.New("转移会员权益失败")
	}
	return nil
}

func (td *TransferUserEquityReq) transferRefundOrder(session *xorm.Session) error {
	rList := order.TbRefund.GetRefundListByUID(td.DistinctUID)
	if len(rList) > 0 {
		for _, v := range rList {
			v.UID = td.LoginUID
			if err := v.UpdateByTran(session); err != nil {
				return err
			}
		}
	}
	uRefundList := order.TbRefundUser.GetRefundListByUID(td.DistinctUID)
	for _, v := range uRefundList {
		v.UID = td.LoginUID
		if err := v.UpdateByTran(session); err != nil {
			return err
		}
	}
	return nil
}
func (td *TransferUserEquityReq) transferIapOrder(session *xorm.Session) error {
	rList := iap.TbIOSOrder.GetListByUID(td.DistinctUID)
	if len(rList) > 0 {
		for _, v := range rList {
			v.UID = td.LoginUID
			if err := v.UpdateByTran(session); err != nil {
				return err
			}
		}
	}
	oList := iap.TbIOSReceipt.GetListByUID(td.DistinctUID)
	if len(oList) > 0 {
		for _, v := range oList {
			v.UID = td.LoginUID
			if err := v.UpdateByTran(session); err != nil {
				return err
			}
		}
	}
	return nil
}

func (td *TransferUserEquityReq) transferSubscribeOrder(session *xorm.Session) error {
	uList := dbuser.TbWPSubU.GetUserSubscribeList(td.DistinctUID)
	if len(uList) > 0 {
		for _, v := range uList {
			v.UID = td.LoginUID
			if err := v.UpdateByTran(session); err != nil {
				return err
			}
		}
	}
	oList := order.TbAlipayContract.GetListByUID(td.DistinctUID)
	if len(oList) > 0 {
		for _, v := range oList {
			v.UID = td.LoginUID
			if err := v.UpdateByTran(session); err != nil {
				return err
			}
		}
		cList := order.TbAlipayCharge.GetListByUID(td.DistinctUID)
		if len(cList) > 0 {
			for _, c := range cList {
				c.UID = td.LoginUID
				if err := c.UpdateByTran(session); err != nil {
					return err
				}
			}
		}
	}
	return nil
}

type PracticeReportParam struct {
	SID               string   `json:"sid"`
	UID               int64    `json:"uid"`
	CourseID          int64    `json:"course_id"`
	Calories          int      `json:"calorie"`
	Minutes           int      `json:"minutes"`
	IsExit            int      `json:"is_exit"`
	OrderDay          int      `json:"order_day"`
	ObProgramID       int64    `json:"ob_program_id"`
	PracticeStartTime int64    `json:"practice_start_time"`
	PracticeType      int      `json:"practice_type"`
	SceneType         int      `json:"scene_type"`
	PracticeLabels    []string `json:"practice_labels,omitempty"`
	PracticeLabelsStr string   `json:"practice_labels_string,omitempty"`
	PlayTime          int      `json:"play_time"`
	SceneTypeID       int64    `json:"scene_type_id"`
	SceneTypeIDIndex  int64    `json:"scene_type_index"`
	ActionNum         int64    `json:"action_num"`
	PracticeProgress  int      `json:"practice_progress"`
}

const MaxPlayTime = 3600

// PracticeReport 练习上报
// nolint
func (suser) PracticeReport(params *PracticeReportParam) error {
	if params.PracticeType == PracticeTypeStart {
		item := course.TbCourseLibrary.GetItem(params.CourseID)
		if item != nil {
			item.PracticeNum++
			if err := item.Update(); err != nil {
				logger.Error(err)
			}
		}
		safelygo.GoSafelyByTraceID(func() {
			recordCoursePlayCountByDate(params.CourseID)
		})
	}
	// 兼容旧版本
	if params.PlayTime == 0 && params.Minutes > 0 {
		params.PlayTime = params.Minutes * 60
	}
	if params.PracticeType == 0 {
		params.PracticeType = PracticeTypeFinish
	}
	if params.SceneType == 0 {
		params.SceneType = int(libuser.SceneTypeEnum.CourseDetail)
	}
	// 最长不超过1小时，超过1小时按1小时算
	if params.PlayTime > MaxPlayTime {
		params.PlayTime = MaxPlayTime
	}
	PracticeLogToKafka(params) // kafka上报
	return nil
}

// recordCoursePlayCountByDate 按日期记录课程播放量
func recordCoursePlayCountByDate(courseID int64) {
	rdc := cache.GetCRedis().GetClient()
	ctx := context.Background()
	// 使用当前日期作为字段
	dateKey := time.Now().Format("20060102")
	cacheKey := fmt.Sprintf("%s%d", libcache.CoursePlayCount30Days, courseID)
	// 增加当天播放次数
	_, err := rdc.HIncrBy(ctx, cacheKey, dateKey, 1).Result()
	if err != nil {
		logger.Error("记录课程日播放量失败", courseID, err)
		return
	}
	// 设置过期时间为31天（保留1天缓冲）
	rdc.Expire(ctx, cacheKey, 31*24*time.Hour)
	// 清理30天前的数据
	cleanOldPlayCount(cacheKey)
}

// cleanOldPlayCount 清理30天前的播放数据
func cleanOldPlayCount(cacheKey string) {
	rdc := cache.GetCRedis().GetClient()
	ctx := context.Background()
	// 计算30天前的日期
	oldDate := time.Now().AddDate(0, 0, -30).Format("20060102")
	// 删除30天前的数据
	rdc.HDel(ctx, cacheKey, oldDate)
}

func PracticeFeelToKafka(params *PracticeFeelReportParam) {
	payload, err := json.Marshal(kafka.AsyncPayload{
		Type:   kafka.AsyncPayloadType.PracticeFeel,
		Params: params,
	})
	if err != nil {
		logger.Error("练习感受上报,序列化JSON失败", err)
		return
	}
	client := grpc.GetKafkaClient()
	key := strconv.Itoa(int(params.UID))
	if _, err = client.Produce(context.Background(), &proto.ProducerRequest{
		Topic:   kafka.GetTopicName(kafka.TopicEnum.Practice),
		Payload: string(payload),
		Key:     key,
	}); err != nil {
		logger.Errorf("练习感受上报,发送 kafka 失败 %s", err)
	}
}

func PracticeLogToKafka(params *PracticeReportParam) {
	payload, err := json.Marshal(kafka.AsyncPayload{
		Type:   kafka.AsyncPayloadType.Practice,
		Params: params,
	})
	if err != nil {
		logger.Error("练习记录上报,序列化JSON失败", err)
		return
	}
	client := grpc.GetKafkaClient()
	key := strconv.Itoa(int(params.UID))
	if _, err = client.Produce(context.Background(), &proto.ProducerRequest{
		Topic:   kafka.GetTopicName(kafka.TopicEnum.Practice),
		Payload: string(payload),
		Key:     key,
	}); err != nil {
		logger.Errorf("练习记录上报,发送 kafka 失败 %s", err)
	}
}

func UpdateAfterPractice(params *PracticeReportParam) error {
	dayList := practice.TbPlayDay.GetList(params.UID)
	totalDay := len(dayList)
	// 更新用户练习时长、卡路里、练习天数
	userDetail := dbuser.TbAccount.GetUserByID(params.UID)
	if userDetail != nil && userDetail.ID > 0 {
		userDetail.PracticeCount++
		userDetail.Calories += params.Calories
		userDetail.Minutes += params.Minutes
		userDetail.PracticeDays = totalDay
		if err := userDetail.Update(); err != nil {
			return err
		}
	}
	courseItem := course.TbCourseLibrary.GetItem(params.CourseID)
	half := 2
	if courseItem != nil && float64(params.Minutes*util.SecondsPerMinute) > courseItem.Duration/float64(half) {
		SrvLastPractice.UpdateUserLastPractice(params)
	}
	logger.Info("练习上报", params.UID, params.ObProgramID, params.CourseID, params.OrderDay)
	// 更新ob计划完成进度
	if params.IsExit != library.Yes {
		if params.PracticeType == 0 || params.PracticeType == PracticeTypeFinish {
			err := UpdateObProgress(params.UID, params.ObProgramID, params.CourseID, params.OrderDay)
			if err != nil {
				logger.Error(err)
				return err
			}
		}
	}
	return nil
}

// UpdateObProgress 更新OB进度
func UpdateObProgress(uid, obProgramID, sessionID int64, orderDay int) error {
	var obSessionItem *course.ObProgramCourse
	obProgram := course.TbObProgram.GetInProcessProgram(uid)
	if obProgram == nil {
		return nil
	}
	// 如果是从课表处练习，会带上相应课表对应哪一节课程的参数，如果是
	if obProgramID == 0 || orderDay == 0 {
		obProgramID = obProgram.ID
		obSessionItem = course.TbObProgramCourse.GetItemByPracticeDate(uid, obProgramID, sessionID,
			util.FormatStartTime(time.Now().Unix()))
	} else {
		obSessionItem = course.TbObProgramCourse.GetItem(uid, obProgramID, sessionID, orderDay)
	}
	if obSessionItem == nil || obSessionItem.IsPractice == library.Yes {
		return nil
	}
	obSessionItem.IsPractice = library.Yes
	obSessionItem.UID = uid
	if err := obSessionItem.Update(); err != nil {
		return err
	}
	if obProgram != nil && obProgram.ID > 0 {
		totalFinish := course.TbObProgramCourse.GetTotalFinish(uid, obProgramID)
		obProgram.FinishCount = int32(totalFinish)
		if err := obProgram.Update(); err != nil {
			return err
		}
	}
	return nil
}

type PracticeFeelReportParam struct {
	SID               string `json:"sid"`
	UID               int64  `json:"uid"`
	CourseID          int64  `json:"course_id"`
	OBProgramID       int64  `json:"ob_program_id"`
	PracticeStartTime int64  `json:"practice_start_time"`
	PracticeFeel      int64  `json:"practice_feel"`
}

// PracticeFeelReport 练习感觉上报
func (suser) PracticeFeelReport(params *PracticeFeelReportParam) error {
	PracticeFeelToKafka(params)
	return nil
}

func ProfileUserVipExpired() {
	rd := cache.GetCRedis()
	lockKey := libcache.LockProfileUserVipExpired
	if lock := rd.Lock(lockKey, cache.LockExpiration(time.Hour)); !lock {
		logger.Info("已被锁定")
		return
	}
	defer func() {
		if err := rd.Unlock(lockKey); err != nil {
			logger.Error(err)
		}
	}()
	accountArr := dbuser.TbAccount.UserVipExpired()
	if accountArr == nil {
		return
	}
	for _, v := range accountArr {
		sensor.UserProfileVipType(v.ID)
	}
}

func HandleChangeUserVip() {
	rd := cache.GetCRedis()
	lockKey := libcache.LockChangeUserVip
	if lock := rd.Lock(lockKey, cache.LockExpiration(time.Hour)); !lock {
		logger.Info("已被锁定")
		return
	}
	defer func() {
		if err := rd.Unlock(lockKey); err != nil {
			logger.Error(err)
		}
	}()
	ctx, cancel := context.WithCancel(context.Background())
	resCh, errCh := util.GetFromQueue(ctx, cache.GetYoga01Redis().GetClient(), libuser.ChangeUserVipKey)
	isStop := false
	for {
		select {
		case msg, ok := <-resCh:
			if !ok {
				isStop = true
				break
			}
			uid, err := strconv.ParseInt(msg, 10, 64)
			if err != nil {
				logger.Error(err.Error(), msg)
			}
			sensor.UserProfileVipType(uid)
		case <-errCh:
		}
		if isStop {
			cancel()
			break
		}
	}
}

func IsPermanentlyVip(account *dbuser.Account) bool {
	var permanentlyTime int64 = ********** // 大于2122年的会员判断为永久卡
	return account.EndTime >= permanentlyTime
}

func ValidGroup(uid int64, groupIDs []int64) *childrenusergroup.ValidGroupResponse {
	rpc := grpc.GetChildrenGroupClient()
	resp, err := rpc.ValidGroup(context.Background(), &childrenusergroup.ValidGroupRequest{UID: uid,
		GroupLabelIDs: groupIDs,
	})
	if err != nil {
		logger.Error(err)
		return nil
	}
	return resp
}

// IsVip 判断用户是不是vip
func IsVip(uid int64) bool {
	if uid < 1 {
		return false
	}
	account := dbuser.TbAccount.GetUserByID(uid)
	if account != nil && account.EndTime > time.Now().Unix() {
		return true
	}
	return false
}

func UnlockDevice(info *dbuser.DeviceLock) bool {
	rdc := cache.GetCRedis().GetClient()
	ctx := context.Background()
	_, err := rdc.SRem(ctx, libcache.UIDByDeviceIDLock, info.UID).Result()
	if err != nil {
		logger.Error("SRem 解锁失败 err ", err)
		return false
	}
	dataKey := fmt.Sprintf("%s%d", libcache.UserOnlineCacheKeyPrefix, info.UID)
	resultArr, err := rdc.LRange(ctx, dataKey, 0, libuser.UIDBindSidLen).Result()
	if err != nil {
		logger.Warn(err)
		return false
	}
	for _, v := range resultArr {
		cacheKey := fmt.Sprintf("%s%s", libcache.LoginSIDCacheKeyPrefix, v)
		if _, err := rdc.Del(context.Background(), cacheKey).Result(); err == nil {
			logger.Warn(err)
		}
	}
	rdc.Del(ctx, fmt.Sprintf("%s%d", libcache.DeviceLimitCacheKeyPrefix, info.UID))
	return true
}

// GetUserPartInformation 部分用户信息
func GetUserPartInformation(uid int64) *libuser.PartInformation {
	rdc := cache.GetCRedis().GetClient()
	ctx := context.Background()
	cKey := fmt.Sprintf("%s:%d", libcache.UserPartInformation, uid)
	cString, cErr := rdc.Get(ctx, cKey).Result()
	var userPartInformation = &libuser.PartInformation{}
	if cErr != nil || cErr == redis.Nil || cString == "" {
		partInformation, err := usersetting.TbUserSetting.GetItem(uid, libuser.UserPartInformation)
		if err != nil {
			logger.Errorf("用户userSetting查询数据失败, uid: %d, key: %s", uid, libuser.UserPartInformation)
			return nil
		}
		if partInformation == nil {
			return nil
		}
		if err := json.Unmarshal([]byte(partInformation.Value), userPartInformation); err != nil {
			logger.Errorf("用户userSetting解析失败, err: %s, uid: %d", err, uid)
			return nil
		}
		if notifyByte, err := json.Marshal(userPartInformation); err == nil {
			rdc.Set(ctx, cKey, string(notifyByte), 12*time.Hour) //nolint
		}
	} else {
		err := json.Unmarshal([]byte(cString), userPartInformation)
		if err == nil {
			return userPartInformation
		}
	}
	return userPartInformation
}

// UpdateUserPartInformation 更新用户信息
func UpdateUserPartInformation(uid int64, uPInformation *libuser.PartInformation) error {
	partInformation, err := usersetting.TbUserSetting.GetItem(uid, libuser.UserPartInformation)
	if err != nil {
		logger.Errorf("用户userSetting查询数据失败, uid: %d, key: %s", uid, libuser.UserPartInformation)
		return nil
	}
	rdc := cache.GetCRedis().GetClient()
	ctx := context.Background()
	cKey := fmt.Sprintf("%s:%d", libcache.UserPartInformation, uid)
	saveInformation := &libuser.PartInformation{}
	if partInformation != nil && partInformation.ID > 0 {
		if err := json.Unmarshal([]byte(partInformation.Value), saveInformation); err != nil {
			logger.Error(err)
			return nil
		}
	}
	if uPInformation.Age > 0 {
		saveInformation.Age = uPInformation.Age
	}
	if uPInformation.Height > 0 {
		saveInformation.Height = uPInformation.Height
	}
	if uPInformation.Weight > 0 {
		saveInformation.Weight = uPInformation.Weight
	}
	value, err := json.Marshal(saveInformation)
	if err != nil {
		logger.Warnf("设置用户部分属性, err: %s", err)
		return err
	}
	if partInformation != nil && partInformation.ID > 0 {
		partInformation.Value = string(value)
		if err := partInformation.Update(); err != nil {
			logger.Errorf("更新用户配置失败, uid:%d, err: %s, value: %s, ", uid, err, string(value))
			return err
		}
		rdc.Del(ctx, cKey)
	} else {
		data := &usersetting.UserSetting{
			UID:      uid,
			Key:      libuser.UserPartInformation,
			Value:    string(value),
			IsDelete: int32(library.No),
		}
		if err := data.Save(); err != nil {
			logger.Warnf("插入用户配置失败 %s", err)
			return err
		}
		rdc.Set(ctx, cKey, string(value), 24*time.Hour) //nolint
	}
	return nil
}

type Information struct {
	Gender      int
	NickName    string
	Avatar      string
	Weight      int
	GoalWeight  int
	Age         int
	Height      int
	IsRecommend int
}

// UpdateUserInformation 更新用户信息
// nolint
func UpdateUserInformation(uid int64, i *Information, isLimit bool) errorcode.ErrorCode {
	account := dbuser.TbAccount.GetUserByID(uid)
	if account == nil {
		return errorcode.Success
	}
	if i.Gender > 0 {
		account.Gender = i.Gender
	}
	if i.NickName != "" {
		if !SensitiveWordCheck([]string{i.NickName}) {
			return errorcode.HasSensitiveText
		}
		account.Nickname = i.NickName
	}
	if i.Avatar != "" {
		if !SensitiveImageCheck([]string{i.Avatar}) {
			return errorcode.HasSensitiveImage
		}
		account.Avatar = i.Avatar
	}
	if !LimitModifyRate(uid, i.Gender, i.NickName, i.Avatar) && isLimit {
		return errorcode.ModifyInfoLimit
	}
	if i.IsRecommend > 0 {
		account.IsRecommend = i.IsRecommend
	}

	if err := UpdateUserPartInformation(account.ID, &libuser.PartInformation{
		Age:    i.Age,
		Height: i.Height,
		Weight: i.Weight,
	}); err != nil {
		logger.Warnf("UpdateUserPartInformation uid:%d err:%s", account.ID, err.Error())
	}
	err := account.Update()
	if err != nil {
		return errorcode.DBError
	}
	return errorcode.Success
}

type CollectUserInfoRes struct {
	CollectList []*CollectUserInfoItem `json:"collect_list"`
}

type CollectUserInfoItem struct {
	Type         string   `json:"type"`
	UseGoal      string   `json:"use_goal"`
	UseScene     string   `json:"use_scene"`
	CollectTimes int32    `json:"collect_times"`
	List         []string `json:"list"`
	Content      string   `json:"content"`
}

type LinkItem struct {
	LinkType    int32  `json:"link_type"`
	LinkContent string `json:"link_content"`
}

func CollectBaseUserInfo(account *dbuser.Account) ([]*CollectUserInfoItem, error) {
	res := make([]*CollectUserInfoItem, 0)
	baseInfos := map[libuser.PrivacyPersonalInfoType]interface{}{
		libuser.PrivacyPersonalInfoEnum.Thumbnail: account.Avatar,
		libuser.PrivacyPersonalInfoEnum.NickName:  account.Nickname,
		libuser.PrivacyPersonalInfoEnum.Gender:    int64(account.Gender),
		libuser.PrivacyPersonalInfoEnum.Age:       0,
		libuser.PrivacyPersonalInfoEnum.Height:    0,
		libuser.PrivacyPersonalInfoEnum.Weight:    0,
	}
	partInformation := GetUserPartInformation(account.ID)
	if partInformation != nil {
		baseInfos[libuser.PrivacyPersonalInfoEnum.Age] = partInformation.Age
		baseInfos[libuser.PrivacyPersonalInfoEnum.Height] = partInformation.Height
		baseInfos[libuser.PrivacyPersonalInfoEnum.Weight] = partInformation.Weight
	}
	collectList := make([]*CollectUserInfoItem, 0)
	for k, v := range baseInfos {
		collectList = append(collectList, formatCollectBase(v, k))
	}
	funcs := map[libuser.PrivacyPersonalInfoType]func(account *dbuser.Account,
		tp libuser.PrivacyPersonalInfoType) *CollectUserInfoItem{
		libuser.PrivacyPersonalInfoEnum.Mobile: formatCollectPhone,
	}
	for k, v := range funcs {
		collectList = append(collectList, v(account, k))
	}
	// 排序
	for _, v := range libuser.PrivacyPersonalInfoSort {
		for k := range collectList {
			if collectList[k].Type == libuser.PrivacyPersonalInfoMap[v] {
				res = append(res, collectList[k])
			}
		}
	}
	return res, nil
}

// formatCollectBase 基础信息格式化
func formatCollectBase(info interface{}, tp libuser.PrivacyPersonalInfoType) *CollectUserInfoItem {
	res := &CollectUserInfoItem{
		Type:    libuser.PrivacyPersonalInfoMap[tp],
		UseGoal: "完善个人资料",
		List:    make([]string, 0),
		Content: fmt.Sprintf("可至【我的】-【设置】-【个人信息】- %s查看", libuser.PrivacyPersonalInfoMap[tp]),
	}
	switch tp {
	case libuser.PrivacyGender, libuser.PrivacyAge, libuser.PrivacyHeight, libuser.PrivacyWeight:
		res.UseScene = "完善个人相关资料、制定适合个人的运动健身计划"
	default:
		res.UseScene = "完善相关的个人资料"
	}
	if v, ok := info.(string); ok && v != "" && v != "0" {
		res.CollectTimes = 1
	}
	if v, ok := info.(int64); ok && v != 0 {
		res.CollectTimes = 1
	}
	if v, ok := info.(int); ok && v != 0 {
		res.CollectTimes = 1
	}
	if tp == libuser.PrivacyPersonalInfoEnum.Gender {
		res.CollectTimes = 1
	}
	return res
}

// formatCollectPhone 手机号信息格式化
func formatCollectPhone(account *dbuser.Account, tp libuser.PrivacyPersonalInfoType) *CollectUserInfoItem {
	res := &CollectUserInfoItem{
		Type:     libuser.PrivacyPersonalInfoMap[tp],
		UseGoal:  "创建账号",
		UseScene: "首次注册创建账号、使用一键登录功能、手机登录",
		List:     make([]string, 0),
		Content:  "可至【我的】-【设置】-【账号与安全】- 手机查看",
	}
	if account.Mobile != "" && account.Mobile != "0" {
		res.CollectTimes = 1
		res.List = append(res.List, hideStar(account.Mobile))
	}
	return res
}

// nolint
func hideStar(str string) string { // hideStar 脱敏处理
	r := []rune(str)
	if len(r) == 0 || str == "0" {
		return ""
	}
	if len(r) <= 5 {
		return "****"
	}
	m := "*"
	l := len(r) - 4
	if l > 10 {
		l = 10
	}
	for i := 0; i < l; i++ {
		m += "*"
	}
	return string(r[0:2]) + m + string(r[len(r)-3:])
}

func GetMobilePhone(t *http.Context, mobilePhone string) string {
	if mobilePhone == "" {
		return ""
	}
	cfg := config.Get()
	mobilePhoneByte := crypto.NewAes(cfg.Service.PassWdAesKey, cfg.Service.PassWdAesKey).Decrypt(mobilePhone)
	if len(mobilePhoneByte) == 0 {
		return mobilePhone
	}
	return string(mobilePhoneByte)
}
