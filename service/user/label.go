package user

import (
	"encoding/json"
	"sort"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases/course"
	"gitlab.dailyyoga.com.cn/server/children/databases/order"
	dbp "gitlab.dailyyoga.com.cn/server/children/databases/product"
	"gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/databases/usersetting"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libcourse "gitlab.dailyyoga.com.cn/server/children/library/course"
	"gitlab.dailyyoga.com.cn/server/children/library/pay"
	libuser "gitlab.dailyyoga.com.cn/server/children/library/user"
	"gitlab.dailyyoga.com.cn/server/children/service/audit"
	srvcourse "gitlab.dailyyoga.com.cn/server/children/service/course"
	"gitlab.dailyyoga.com.cn/server/children/service/equity"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type label struct {
}

var SrvLabel label

// VipLabel 获取vip类型
type VipLabel struct {
	VipType   int   `json:"vip_type"`
	StartTime int64 `json:"start_time"`
	EndTim    int64 `json:"end_tim"`
}

type LabelList struct {
	RegisterTime   int64                       `json:"register_time"`
	UserChannel    int                         `json:"user_channel"`
	Gender         int                         `json:"gender"`
	Age            int32                       `json:"age"`
	VipStatus      int32                       `json:"vip_status"`
	FitnessPurpose libcourse.FitnessPurposeInt `json:"fitness_purpose"`
	AdSwitch       int32                       `json:"ad_switch"`
	ProgramSwitch  int32                       `json:"program_switch"`
}

func (l *label) CalcUserLabelBefore(uid int64, appClient *library.AppClient, choice *srvcourse.ObUserChoice) {
	userLabelList := &LabelList{
		VipStatus: library.No, // 1-是 2-否
	}
	// 获取用户标签
	userLabelConfig, _ := usersetting.TbUserSetting.GetItem(uid, libuser.UserLabel)
	if userLabelConfig != nil {
		if err := json.Unmarshal([]byte(userLabelConfig.Value), &userLabelList); err != nil {
			logger.Warnf("解析用户标签列表失败, err: %s", err)
		}
	}
	userLabelList.Age = int32(choice.Age)
	userLabelList.Gender = choice.Gender
	userLabelList.FitnessPurpose = choice.SportPurpose

	accountInfo := user.TbAccount.GetUserByID(uid)
	if accountInfo != nil {
		userLabelList.RegisterTime = accountInfo.CreateTime
		userLabelList.VipStatus = library.No
		now := time.Now().Unix()
		if accountInfo.EndTime > now {
			userLabelList.VipStatus = library.Yes
		}
		userLabelList.Gender = accountInfo.Gender
	}
	// 用户渠道标签
	userLabelList.UserChannel = audit.GetChannel(appClient, uid)

	jsonValue, _ := json.Marshal(userLabelList)
	logger.Infof("CalcUserLabelBefore-userLabelList: %s", string(jsonValue))
	jsonValue, _ = json.Marshal(userLabelConfig)
	logger.Infof("CalcUserLabelBefore-userLabelConfig: %s", string(jsonValue))
	// 更新用户标签
	if err := l.updateUserLabel(uid, userLabelConfig, userLabelList); err != nil {
		logger.Warnf("更新用户标签失败 err: %s", err)
	}
}

// CalcUserLabel 计算用户标签
func (l *label) CalcUserLabel(uid int64, appClient *library.AppClient) {
	// 获取用户标签
	userLabelConfig, userLabelList, err := l.getUserLabelConfig(uid, appClient)
	if err != nil {
		return
	}
	// 更新用户标签
	if err := l.updateUserLabel(uid, userLabelConfig, userLabelList); err != nil {
		logger.Warnf("更新用户标签失败 err: %s", err)
	}
}

func UpdateCollectPlanNum(uid int64) int {
	// 获取用户标签
	userLabelConfig, err := usersetting.TbUserSetting.GetItem(uid, libuser.UserLabel)
	if err != nil || userLabelConfig == nil {
		return 0
	}
	userLabelList := &LabelList{}
	if err := json.Unmarshal([]byte(userLabelConfig.Value), &userLabelList); err != nil {
		logger.Error(err)
		return 0
	}
	value, err := json.Marshal(userLabelList)
	if err != nil {
		logger.Error(err)
		return 0
	}
	userLabelConfig.Value = string(value)
	if err := userLabelConfig.Update(); err != nil {
		logger.Error(err)
		return 0
	}
	return 0
}

// updateUserLabel 更新用户标签
func (l *label) updateUserLabel(uid int64, userLabelConfig *usersetting.UserSetting, userLabelList *LabelList) error {
	value, err := json.Marshal(userLabelList)
	if err != nil {
		logger.Warnf("用户标签格式化失败, err: %s", err)
		return err
	}

	if userLabelConfig != nil && userLabelConfig.ID > 0 {
		userLabelConfig.Value = string(value)
		if err := userLabelConfig.Update(); err != nil {
			logger.Errorf("更新用户配置失败, uid:%d, err: %s, value: %s, ", uid, err, string(value))
			return err
		}
	} else {
		userLabelConfig := &usersetting.UserSetting{
			UID:      uid,
			Key:      libuser.UserLabel,
			Value:    string(value),
			IsDelete: int32(library.No),
		}
		if err := userLabelConfig.Save(); err != nil {
			logger.Warnf("插入用户配置失败 %s", err)
			return err
		}
	}
	return nil
}

// getUserLabelConfig 获取用户标签
// nolint
func (l *label) getUserLabelConfig(uid int64,
	appClient *library.AppClient) (*usersetting.UserSetting, *LabelList, error) {
	userLabelList := &LabelList{
		VipStatus: library.No, // 1-是 2-否
	}
	// 获取用户标签
	userLabelConfig, err := usersetting.TbUserSetting.GetItem(uid, libuser.UserLabel)
	if err != nil {
		return nil, userLabelList, err
	}
	if userLabelConfig != nil {
		if err := json.Unmarshal([]byte(userLabelConfig.Value), &userLabelList); err != nil {
			logger.Warnf("解析用户标签列表失败, err: %s", err)
			return userLabelConfig, userLabelList, nil
		}
	}
	accountInfo := user.TbAccount.GetUserByID(uid)
	if accountInfo != nil {
		userLabelList.RegisterTime = accountInfo.CreateTime
		userLabelList.VipStatus = library.No
		now := time.Now().Unix()
		if accountInfo.EndTime > now {
			userLabelList.VipStatus = library.Yes
		}
		userLabelList.Gender = accountInfo.Gender
	}
	// 用户渠道标签
	userLabelList.UserChannel = audit.GetChannel(appClient, uid)
	// 获取vip类型
	obProgram := course.TbObProgram.GetInProcessProgram(uid)
	if obProgram != nil {
		obUserChoice := &srvcourse.ObUserChoice{}
		err := json.Unmarshal([]byte(obProgram.ChoiceData), obUserChoice)
		if err == nil && obUserChoice.SID != "" {
			userLabelList.FitnessPurpose = obUserChoice.SportPurpose
			userLabelList.Age = int32(obUserChoice.Age)
		}
	}
	partInformation := GetUserPartInformation(uid)
	if partInformation != nil && partInformation.Age != 0 {
		userLabelList.Age = int32(partInformation.Age)
	}
	return userLabelConfig, userLabelList, nil
}

const (
	PracticeTypeStart = iota + 1
	PracticeTypeExit
	PracticeTypeFinish
)

// GetVipLabel 获取vip类型
func GetVipLabel(uid int64) []*VipLabel {
	resp := make([]*VipLabel, 0)
	orderList := order.TbWebOrder.GetAllListByUID(uid)
	vipLabelAllMap := make(map[int][]*VipLabel)
	webProductMap := dbp.TbWebProduct.GetAllProductMap()
	if len(webProductMap) == 0 {
		return resp
	}
	for _, v := range orderList {
		if (v.PaymentOrderType == pay.PaymentOrderTypeVoice ||
			v.PaymentOrderType == pay.PaymentOrderTypeVoiceStrong) || v.OrderStatus == pay.OrderStatus.UnPay {
			continue
		}
		if len(order.TbRefund.GetRefundListByOrderIDStatus(v.OrderID, []int{int(pay.RefundStatusEnum.Success)})) > 0 {
			continue
		}
		product := webProductMap[v.ProductID]
		if product == nil {
			continue
		}
		if _, ok := vipLabelAllMap[product.ProductType]; !ok {
			vipLabelAllMap[product.ProductType] = make([]*VipLabel, 0)
		}
		vipLabelAllMap[product.ProductType] = append(vipLabelAllMap[product.ProductType], &VipLabel{
			VipType:   product.ProductType,
			StartTime: v.CreateTime,
			EndTim: util.FormatEndTime(
				equity.CalcVipTimeByStartTime(v.CreateTime, product.DurationType, product.DurationValue)),
		})
	}
	for _, v := range vipLabelAllMap {
		vipLabelArr := v
		switch len(vipLabelArr) {
		case 0:
			continue
		case 1:
			resp = append(resp, vipLabelArr[0])
		default:
			sort.Slice(vipLabelArr, func(i, j int) bool {
				return vipLabelArr[i].EndTim > vipLabelArr[j].EndTim
			})
			resp = append(resp, vipLabelArr[0])
		}
	}
	return resp
}

type AccountSwitchInfo struct {
	RecommendSwitch int32 `json:"recommend_switch"`
	AdSwitch        int32 `json:"ad_switch"`
	ProgramSwitch   int32 `json:"program_switch"`
}

func (l *label) GetUserSwitch(uid int64, appClient *library.AppClient) *AccountSwitchInfo {
	if uid == 0 {
		return &AccountSwitchInfo{
			RecommendSwitch: library.Yes,
			AdSwitch:        library.Yes,
			ProgramSwitch:   library.Yes,
		}
	}
	switchInfo := &AccountSwitchInfo{}
	_, labelItem, err := SrvLabel.getUserLabelConfig(uid, appClient)
	if err != nil {
		logger.Error(err)
		return switchInfo
	}
	switchInfo.AdSwitch = labelItem.AdSwitch
	switchInfo.ProgramSwitch = labelItem.ProgramSwitch
	if switchInfo.AdSwitch == 0 {
		switchInfo.AdSwitch = library.Yes
	}
	if switchInfo.ProgramSwitch == 0 {
		switchInfo.ProgramSwitch = library.Yes
	}
	switchInfo.RecommendSwitch = library.Yes
	account := user.TbAccount.GetUserByID(uid)
	if account != nil && account.IsRecommend > 0 {
		switchInfo.RecommendSwitch = int32(account.IsRecommend)
	}
	return switchInfo
}

func (l *label) ChangeUserSwitch(uid int64, appClient *library.AppClient, switchInfo *AccountSwitchInfo) {
	userLabelConfig, userLabelList, err := SrvLabel.getUserLabelConfig(uid, appClient)
	if err != nil {
		return
	}
	if switchInfo != nil {
		if switchInfo.AdSwitch > 0 {
			userLabelList.AdSwitch = switchInfo.AdSwitch
		}
		if switchInfo.ProgramSwitch > 0 {
			userLabelList.ProgramSwitch = switchInfo.ProgramSwitch
		}
	}
	account := user.TbAccount.GetUserByID(uid)
	if account != nil && switchInfo.RecommendSwitch > 0 {
		account.IsRecommend = int(switchInfo.RecommendSwitch)
		if err := account.Update(); err != nil {
			logger.Error(err)
		}
	}
	// 更新用户标签
	if err := l.updateUserLabel(uid, userLabelConfig, userLabelList); err != nil {
		logger.Warnf("更新用户标签失败 err: %s", err)
	}
}
