package user

import (
	"context"
	"encoding/json"
	"strconv"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/protogen/children-user-group-go/childrenusergroup"
	"gitlab.dailyyoga.com.cn/server/children/databases/client"
	"gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/grpc"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libc "gitlab.dailyyoga.com.cn/server/children/library/client"
	"gitlab.dailyyoga.com.cn/server/children/service/product"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type ResourceData struct {
	ID            int64              `json:"id"`
	Title         string             `json:"title"`
	PraiseLatform int32              `json:"praise_platform"`
	IsLottery     int64              `json:"is_lottery"`
	AwardType     int64              `json:"award_type"`
	AwardDuration int64              `json:"award_duration"`
	Img           *library.ImageInfo `json:"img,omitempty"`
}

type StorePraiseConfig struct {
	IsPopupPositive bool `json:"is_popup_positive"`
}

func GetPraiseInfoByID(uid, id int64, appClient *library.AppClient,
	obChannel *product.ObUserChannel) *ResourceData {
	logger.Info("GetPraiseInfoByID-0", uid, id)
	configItem := client.TbConfig.GetItemByKey(libc.ConfigKeyStoreParise)
	if configItem == nil {
		return nil
	}
	logger.Info("GetPraiseInfoByID-0-1", uid, id)
	configValue := &StorePraiseConfig{}
	err := json.Unmarshal([]byte(configItem.Value), configValue)
	if err != nil {
		logger.Error(err)
		return nil
	}
	logger.Info("GetPraiseInfoByID-1", uid, id)
	if obChannel.Compliance || !configValue.IsPopupPositive {
		return nil
	}
	if item := user.TbStorePraise.GetItemByUID(uid); item != nil {
		return nil
	}
	logger.Info("GetPraiseInfoByID-2", uid, id)
	if item := user.TbStorePraise.GetItemByDeviceID(appClient.DeviceID); item != nil {
		return nil
	}
	item := user.TbPraiseResource.GetItemByID(id)
	if item == nil {
		return nil
	}
	channel := strconv.Itoa(appClient.Channel)
	_, ok := libc.NameToChannelMap[channel]
	if !ok {
		channel = libc.ChannelOther
	}
	logger.Info("GetPraiseInfoByID-3", channel)
	// 判断在不渠道列表
	clientChannel := make([]string, 0)
	_ = json.Unmarshal([]byte(item.ClientChannel), &clientChannel)
	logger.Info("GetPraiseInfoByID-4", clientChannel)
	if !library.InArray(channel, clientChannel) {
		return nil
	}
	return &ResourceData{
		ID:            item.ID,
		Title:         item.Title,
		PraiseLatform: item.PraiseLatform,
		IsLottery:     item.IsLottery,
		AwardType:     item.AwardType,
		AwardDuration: item.AwardDuration,
		Img:           util.UnmarshalImageStr(item.Img),
	}
}

type PraiseFinishPop struct {
	ID            int64              `json:"id"`
	Title         string             `json:"title"`
	ButtonText    string             `json:"button_text"`
	ButtonTextSub string             `json:"button_text_sub"`
	DayNum        int                `json:"day_num"`
	PopNumDay     int                `json:"pop_num_day"`
	PopNumNum     int                `json:"pop_num_num"`
	Img           *library.ImageInfo `json:"img,omitempty"`
	PraiseInfo    *ResourceData      `json:"praise_info,omitempty"`
}

// nolint
func StorePraiseFinish(uid int64, appClient *library.AppClient,
	obChannel *product.ObUserChannel) *PraiseFinishPop {
	configItem := client.TbConfig.GetItemByKey(libc.ConfigKeyStoreParise)
	if configItem == nil {
		return nil
	}
	configValue := &StorePraiseConfig{}
	err := json.Unmarshal([]byte(configItem.Value), configValue)
	if err != nil {
		logger.Error(err)
		return nil
	}
	logger.Info("StorePraiseFinish-1", uid)
	if obChannel.Compliance || !configValue.IsPopupPositive {
		return nil
	}
	logger.Info("StorePraiseFinish-2", uid)
	if item := user.TbStorePraise.GetItemByUID(uid); item != nil {
		return nil
	}
	logger.Info("StorePraiseFinish-3", uid)
	if item := user.TbStorePraise.GetItemByDeviceID(appClient.DeviceID); item != nil {
		return nil
	}
	logger.Info("StorePraiseFinish-4", uid)
	finishPopList := user.TbPraiseResource.GetList()
	userGroupArr := make([]int64, 0)
	for _, v := range finishPopList {
		if v.UserGroupID == 0 {
			continue
		}
		userGroupArr = append(userGroupArr, v.UserGroupID)
	}
	groupClient := grpc.GetChildrenGroupClient()
	rsp, err := groupClient.ValidGroup(context.Background(), &childrenusergroup.ValidGroupRequest{
		UID:           uid,
		GroupLabelIDs: userGroupArr,
	})
	logger.Info("StorePraiseFinish-5", uid)
	if err != nil {
		logger.Error(err)
		return nil
	}
	var popID int64
	validGroupList := rsp.GetGroupLabelID()
	if len(validGroupList) == 0 {
		validGroupList = make([]int64, 0)
	}
	validGroupList = append(validGroupList, 0)
	logger.Info("StorePraiseFinish-6", uid, validGroupList)
	channel := strconv.Itoa(appClient.Channel)
	_, ok := libc.NameToChannelMap[channel]
	if !ok {
		channel = libc.ChannelOther
	}
	for _, v := range finishPopList {
		if library.InArray(v.UserGroupID, validGroupList) {
			clientChannel := make([]string, 0)
			_ = json.Unmarshal([]byte(v.ClientChannel), &clientChannel)
			if library.InArray(channel, clientChannel) {
				popID = v.ID
				break
			}
		}
	}
	logger.Info("StorePraiseFinish-6", uid, popID)
	item := user.TbPraiseResource.GetItemByID(popID)
	if item == nil {
		return nil
	}
	logger.Info("StorePraiseFinish-7", uid, popID)
	praiseInfo := GetPraiseInfoByID(uid, item.ID, appClient, obChannel)
	if praiseInfo == nil {
		return nil
	}
	logger.Info("StorePraiseFinish-8", uid, popID)
	return &PraiseFinishPop{
		ID:         item.ID,
		Title:      item.Title,
		Img:        util.UnmarshalImageStr(item.Img),
		PraiseInfo: praiseInfo,
	}
}
