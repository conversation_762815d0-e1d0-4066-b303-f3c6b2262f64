// nolint
package order

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"sort"
	"strconv"
	"time"

	"github.com/golang-jwt/jwt/v4"
	"gitlab.dailyyoga.com.cn/server/gopay/applev2/appstore"
	"gitlab.dailyyoga.com.cn/server/gopay/applev2/appstore/api"

	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/databases/iap"
	"gitlab.dailyyoga.com.cn/server/children/databases/order"
	dbuser "gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libc "gitlab.dailyyoga.com.cn/server/children/library/cache"
	"gitlab.dailyyoga.com.cn/server/children/library/pay"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

func ParseIosV2NotificationData(noticeData string) *appstore.SubscriptionNotificationV2SignedPayload {
	messageData := &appstore.SubscriptionNotificationV2SignedPayload{}
	if err := json.Unmarshal([]byte(noticeData), messageData); err != nil {
		logger.Error(err)
		return nil
	}
	return messageData
}

func ParseResponseBodyV2DecodedPayload(noticeData string) *appstore.SubscriptionNotificationV2DecodedPayload {
	messageData := &appstore.SubscriptionNotificationV2DecodedPayload{}
	if err := json.Unmarshal([]byte(noticeData), messageData); err != nil {
		logger.Error(err)
		return nil
	}
	return messageData
}

// DealV2IOSNotification 处理IOS服务端通知
func DealV2IOSNotification(nItem *iap.Notification) {
	lockKey := fmt.Sprintf("%s:%d", libc.LockDealIOSNotificationLog, nItem.ID)
	expireTime := 5
	lock := cache.GetCRedis().Lock(lockKey, cache.LockExpiration(time.Duration(expireTime)*time.Minute))
	if !lock {
		return
	}
	defer func() {
		err := cache.GetCRedis().Unlock(lockKey)
		if err != nil {
			logger.Warn(err)
		}
	}()
	logger.Info("IOS服务端通知处理", nItem.ID)
	notification := ParseResponseBodyV2DecodedPayload(nItem.NotificationContent)
	if notification == nil {
		return
	}
	defer func() {
		nItem.IsDeal = library.Yes
		notificationByte, _ := json.Marshal(notification)
		nItem.NotificationDetail = string(notificationByte)
		dataDecodedPayload := getNotificationsDataJwsDecoded(notification)
		dataDecodedPayloadByte, _ := json.Marshal(dataDecodedPayload)
		nItem.DataDecodedPayload = string(dataDecodedPayloadByte)
		if dataDecodedPayload != nil && dataDecodedPayload.Transaction != nil {
			nItem.OriginalTransactionID = dataDecodedPayload.Transaction.OriginalTransactionId
			nItem.TransactionID = dataDecodedPayload.Transaction.TransactionId
		}
		if err := nItem.Update(); err != nil {
			logger.Error("IOS服务端通知处理完成标记状态失败", err, nItem.ID)
		}
	}()
	var handleSubtypeFun = map[appstore.NotificationTypeV2]func(n *appstore.SubscriptionNotificationV2DecodedPayload,
		nItem *iap.Notification) bool{
		appstore.NotificationTypeV2Subscribed:             subscribed,
		appstore.NotificationTypeV2ConsumptionRequest:     consumptionRequest,
		appstore.NotificationTypeV2DidRenew:               didRenew,
		appstore.NotificationTypeV2OfferRedeemed:          offerRedeemed,
		appstore.NotificationTypeV2DidChangeRenewalPref:   didChangeRenewalPref,
		appstore.NotificationTypeV2DidChangeRenewalStatus: didChangeRenewalStatus,
		appstore.NotificationTypeV2DidFailToRenew:         didFailToRenew,
		appstore.NotificationTypeV2Expired:                Expired,
		appstore.NotificationTypeV2GracePeriodExpired:     gracePeriodExpired,
		appstore.NotificationTypeV2OneTimeCharge:          oneTimeCharge,
		appstore.NotificationTypeV2PriceIncrease:          priceIncrease,
		appstore.NotificationTypeV2Refund:                 refund,
		appstore.NotificationTypeV2RefundDeclined:         refundDeclined,
		appstore.NotificationTypeV2RefundReversed:         refundReversed,
		appstore.NotificationTypeV2RenewalExtended:        renewalExtended,
		appstore.NotificationTypeV2RenewalExtension:       renewalExtension,
		appstore.NotificationTypeV2Revoke:                 revoke,
	}

	if fn, ok := handleSubtypeFun[notification.NotificationType]; ok {
		fn(notification, nItem)
	} else {
		logger.Warn("未知的通知类型", notification.NotificationType)
		return
	}
}

// 如果subtype是INITIAL_BUY（首次购买），则用户首次通过“家人共享”购买或接收了对订阅的访问权限。
// 如果是RESUBSCRIBE（重新购买/重新购买同一个组内的plan），
// 则用户通过家庭共享重新订阅或接收了对同一订阅或同一订阅组内的另一个订阅的访问权限。
func subscribed(n *appstore.SubscriptionNotificationV2DecodedPayload, nItem *iap.Notification) bool {
	/*
		switch n.Subtype {
		// 客户首次订阅订阅组内的任何订阅。购买者首次订阅后 家庭成员可通过“家庭共享”获得订阅的访问权限。
		// 客户首次订阅时需兑换优惠码。
		case appstore.SubTypeV2InitialBuy:
		// 客户重新订阅与其已过期订阅相同的订阅组中的任何订阅  购买者重新订阅后，家庭成员可以通过“家庭共享”访问订阅。
		// 客户在订阅到期后兑换促销优惠、优惠代码或赢回优惠。
		case appstore.SubTypeV2Resubscribe:
		}
	*/
	return processingTransactionDat(n, nItem, nil)
}

// 一种通知类型，表明客户针对消费品应用内购买或自动续订订阅发起了退款请求，并且 App Store 要求您提供消费数据。
// Apple 会要求提供消费信息以用于客户发起的退款请求。
func consumptionRequest(n *appstore.SubscriptionNotificationV2DecodedPayload, nItem *iap.Notification) bool {
	if !validateNotification(n) {
		return false
	}
	dataDecodedPayload := getNotificationsDataJwsDecoded(n)
	if dataDecodedPayload != nil && dataDecodedPayload.Transaction != nil {
		ProcessRefundAsk(dataDecodedPayload.Transaction, pay.IosRefundStatusAlreadyApplied)
	}
	a := api.NewStoreClient(getStoreConfig(dataDecodedPayload.Transaction.Environment == appstore.Sandbox))
	ctx := context.Background()
	body := formatConsumptionBody(dataDecodedPayload.Transaction)
	if body == nil {
		logger.Error("AppStore consumptionRequest body empty")
		return false
	}
	code, err := a.SendConsumptionInfo(ctx, dataDecodedPayload.Transaction.OriginalTransactionId, *body)
	if err != nil {
		logger.Error("AppStore consumptionRequest err", err)
		return false
	}
	if code != 202 {
		logger.Errorf("AppStore consumptionRequest code:%d", code)
		return false
	}
	nItem.ProcessResult = strconv.Itoa(code)
	return true
}

func ProcessRefundAsk(tc *appstore.JWSTransactionDecodedPayload, refundStatus int) {
	re := iap.TbIOSOrder.GetItemByTran(tc.OriginalTransactionId, tc.TransactionId)
	if re == nil {
		re = iap.TbIOSOrder.GetItemByWebItemID(tc.OriginalTransactionId, tc.TransactionId)
	}
	if re == nil {
		return
	}
	if re.AnonymousID == "" {
		re.AnonymousID = GetAnonymousID(re.UID)
	}
	re.RefundStatus = refundStatus
	if err := re.Update(); err != nil {
		logger.Error("AppStore consumptionRequest err", err)
	}
}

// 与其一起subtype指示订阅已成功续订。如果subtype是BILLING_RECOVERY，则之前续订失败的过期订阅已成功续订。如果子状态为空（续订），
// 则活动订阅已成功自动续订新的交易周期。为客户提供对订阅内容或服务的访问权限
func didRenew(n *appstore.SubscriptionNotificationV2DecodedPayload, nItem *iap.Notification) bool {
	/*
		switch n.Subtype {
		// 计费重试成功恢复订阅。
		case appstore.SubTypeV2BillingRecovery:

		default:

		}
	*/
	return processingTransactionDat(n, nItem, nil)
}

// 一种通知类型，与其 一起subtype指示用户兑换了促销优惠或优惠代码。
// 如果subtype是INITIAL_BUY，则用户兑换了首次购买的优惠。
// 如果是RESUBSCRIBE，则用户兑换了重新订阅非活动订阅的优惠。
// 如果是UPGRADE，则用户兑换了升级其有效订阅的优惠，该优惠立即生效。
// 如果是DOWNGRADE，则用户兑换了降级其有效订阅的优惠，该优惠将在下一个续订日期生效。
func offerRedeemed(n *appstore.SubscriptionNotificationV2DecodedPayload, nItem *iap.Notification) bool {
	dataDecodedPayload := getNotificationsDataJwsDecoded(n)
	if dataDecodedPayload.Transaction == nil {
		logger.Error("AppStore subscribed dataDecodedPayload.Transaction nil")
		return false
	}
	if dataDecodedPayload.RenewalInfo == nil {
		logger.Error("AppStore subscribed dataDecodedPayload.RenewalInfo nil")
		return false
	}
	transaction := dataDecodedPayload.Transaction
	inAppOrder := getIAPTransactionItemHistory(transaction.OriginalTransactionId,
		transaction.Environment == appstore.Sandbox)
	if inAppOrder == nil {
		logger.Infof("AppStore formatIAPTransactionV2 nil data:%+v", inAppOrder)
		return true
	}
	switch n.Subtype {
	// 客户兑换促销优惠并降低其订阅级别。
	// case appstore.SubTypeV2Downgrade:
	// 客户兑换促销优惠或优惠代码来升级其订阅。
	case appstore.SubTypeV2Upgrade:
		// 处理退款订单
		dealNotificationCancelOrder(inAppOrder)
		// 升级的会员增加
		processingTransactionDat(n, nItem, inAppOrder)
		// 户兑换了首次购买的优惠。
	// case appstore.SubTypeV2InitialBuy:
	// 则用户兑换了重新订阅非活动订阅的优惠。
	// case appstore.SubTypeV2Resubscribe:
	// 客户兑换促销优惠或优惠代码以获得有效订阅。
	default:
		return processingTransactionDat(n, nItem, inAppOrder)
	}
	return true
}

// 一种通知类型，与其一起subtype指示用户对其订阅计划进行了更改。如果subtype是UPGRADE，则用户升级了他们的订阅。升级立即生效，
// 开始新的计费周期，用户将收到上一周期未使用部分的按比例退款。如果subtype是DOWNGRADE，则用户降级了他们的订阅。降级将在下一个续订日期生效，
// 并且不会影响当前有效的计划。如果subtype为空，则用户将其续订首选项更改回当前订阅，从而有效地取消降级。
func didChangeRenewalPref(n *appstore.SubscriptionNotificationV2DecodedPayload, nItem *iap.Notification) bool {
	dataDecodedPayload := getNotificationsDataJwsDecoded(n)
	if dataDecodedPayload.Transaction == nil {
		logger.Error("AppStore subscribed dataDecodedPayload.Transaction nil")
		return false
	}
	if dataDecodedPayload.RenewalInfo == nil {
		logger.Error("AppStore subscribed dataDecodedPayload.RenewalInfo nil")
		return false
	}
	transaction := dataDecodedPayload.Transaction
	inAppOrder := getIAPTransactionItemHistory(transaction.OriginalTransactionId,
		transaction.Environment == appstore.Sandbox)
	if inAppOrder == nil {
		logger.Infof("AppStore formatIAPTransactionV2 nil data:%+v", inAppOrder)
		return true
	}
	switch n.Subtype {
	// 客户降级同一订阅组内的订阅。
	// case appstore.SubTypeV2Downgrade:
	// 客户在同一订阅组内升级订阅。
	case appstore.SubTypeV2Upgrade:
		// 处理退款订单
		dealNotificationCancelOrder(inAppOrder)
		// 升级添加会员
		processingTransactionDat(n, nItem, inAppOrder)
		// 客户恢复到之前的订阅，从而有效地取消了降级。
	default:
		return processingTransactionDat(n, nItem, inAppOrder)
	}
	return true
}

// 与其一起subtype指示用户对订阅续订状态进行了更改。
// 如果 AUTO_RENEW_ENABLED，则用户重新启用订阅自动续订。
// 如果是AUTO_RENEW_DISABLED，则用户禁用了订阅自动续费，或者用户申请退款后App Store禁用了订阅自动续费。
func didChangeRenewalStatus(n *appstore.SubscriptionNotificationV2DecodedPayload, nItem *iap.Notification) bool {
	dataDecodedPayload := getNotificationsDataJwsDecoded(n)
	if dataDecodedPayload.Transaction == nil {
		logger.Error("AppStore subscribed dataDecodedPayload.Transaction nil")
		return false
	}
	if dataDecodedPayload.RenewalInfo == nil {
		logger.Error("AppStore subscribed dataDecodedPayload.RenewalInfo nil")
		return false
	}
	transaction := dataDecodedPayload.Transaction
	apiTransactionInfo := GetTransactionInfo(transaction.TransactionId, transaction.Environment == appstore.Sandbox)
	if apiTransactionInfo == nil {
		return false
	}
	fmtIap := &formatPayload{}
	inAppOrder := fmtIap.formatIAPTransactionV2(apiTransactionInfo)
	if inAppOrder == nil {
		logger.Infof("AppStore formatIAPTransactionV2 nil data:%+v", apiTransactionInfo)
		return true
	}
	switch n.Subtype {
	// 客户从 App Store 订阅设置页面取消订阅。
	// 由于客户使用退款请求 API 通过您的应用发起了退款，因此系统禁用了自动续订。
	case appstore.SubTypeV2AutoRenewDisabled:
		//autoRenewStatus := pay.ContractChangeTypeEnum.ContractChangeTypeAdd
		changeTransactionRenewStatus(inAppOrder.OriginalTransactionID,
			pay.ContractChangeTypeEnum.ContractChangeTypeDelete)
	// 客户取消订阅后再次订阅，可重新启用自动续订。
	case appstore.SubTypeV2AutoRenewEnabled:
		changeTransactionRenewStatus(inAppOrder.OriginalTransactionID,
			pay.ContractChangeTypeEnum.ContractChangeTypeAdd)
	}
	return true
}

// 与其一起subtype指示订阅由于计费问题而未能续订。
// 订阅进入计费重试期。如果subtype是GRACE_PERIOD，则在宽限期内继续提供服务。
// 如果为空，则说明订阅不在宽限期内，您可以停止提供订阅服务。
func didFailToRenew(n *appstore.SubscriptionNotificationV2DecodedPayload, nItem *iap.Notification) bool {
	//switch n.Subtype {
	//// 订阅续订失败并进入启用计费宽限期的计费重试期。
	//case appstore.SubTypeV2GracePeriod:
	//	// 订阅续订失败，进入计费重试期。
	//default:
	//
	//}
	logger.Infof("AppStore didFailToRenew %+v nItem: %+v", n, nItem)
	return true
}

// 如果subtype是VOLUNTARY，则订阅在用户禁用订阅续订后过期。
// 如果subtype是BILLING_RETRY，则订阅已过期，因为计费重试期已结束，
// 但没有成功的计费事务。如果是PRICE_INCREASE，
// 则订阅已过期，因为用户不同意需要用户同意的价格上涨。如果是PRODUCT_NOT_FOR_SALE，
// 则订阅已过期，因为在订阅尝试续订时该产品不可购买。
func Expired(n *appstore.SubscriptionNotificationV2DecodedPayload, nItem *iap.Notification) bool {
	//switch n.Subtype {
	//// 由于客户选择取消，订阅已过期。
	//case appstore.SubTypeV2Voluntary:
	//
	//// 由于开发者下架订阅，导致订阅过期，并且续订失败。
	//case appstore.SubTypeV2ProductNotForSale:
	//
	//	// 由于计费重试期结束且未恢复订阅，因此订阅已过期。
	//case appstore.SubTypeV2BillingRetry:
	//
	//}
	logger.Infof("AppStore Expired %+v nItem: %+v", n, nItem)
	return true
}

// 指示计费宽限期已结束而无需续订订阅，因此您可以关闭对服务或内容的访问。通知用户他们的账单信息可能存在问题。
// 订阅退出计费宽限期（并继续计费重试）。
func gracePeriodExpired(n *appstore.SubscriptionNotificationV2DecodedPayload, nItem *iap.Notification) bool {
	logger.Infof("AppStore gracePeriodExpired %+v nItem: %+v", n, nItem)
	return true
}

// ONE_TIME_CHARGE通知目前仅在沙盒环境中可用。
// 表示客户购买了消耗品、非消耗品或不续订的订阅。当客户通过“家人共享”获得非消费品的访问权时，App Store也会发送此通知。
// 有关自动续订订阅购买的通知，请参阅订阅通知类型。
func oneTimeCharge(n *appstore.SubscriptionNotificationV2DecodedPayload, nItem *iap.Notification) bool {
	return processingTransactionDat(n, nItem, nil)
}

// 一种通知类型，与其一起subtype表示系统已通知用户自动续订订阅价格上涨。
// 如果涨价需要用户同意，是subtype指PENDING用户没有对涨价做出回应，或者ACCEPTED用户已经同意涨价。
// 如果涨价不需要用户同意，那subtype就是ACCEPTED。
func priceIncrease(n *appstore.SubscriptionNotificationV2DecodedPayload, nItem *iap.Notification) bool {
	//switch n.Subtype {
	//// 客户同意自动续订订阅价格上涨（需征得同意）。
	//// 系统会通知客户自动续订订阅价格上涨，无需客户同意。
	//case appstore.SubTypeV2Accepted:
	//
	//	// 系统通知客户自动续订订阅价格上涨需要客户同意，但客户没有回应。
	//case appstore.SubTypeV2Pending:
	//
	//}
	logger.Infof("AppStore priceIncrease %+v nItem: %+v", n, nItem)

	return true
}

// 指示 AppStore已成功对消费品应用内购买、非消费品应用内购买、自动续订订阅或非续订订阅的交易进行退款。
func refund(n *appstore.SubscriptionNotificationV2DecodedPayload, nItem *iap.Notification) bool {
	dataDecodedPayload := getNotificationsDataJwsDecoded(n)
	if dataDecodedPayload.Transaction == nil {
		logger.Error("AppStore subscribed dataDecodedPayload.Transaction nil")
		return false
	}
	if dataDecodedPayload.RenewalInfo == nil {
		logger.Error("AppStore subscribed dataDecodedPayload.RenewalInfo nil")
		return false
	}
	transaction := dataDecodedPayload.Transaction
	apiTransactionInfo := GetTransactionInfo(transaction.TransactionId, transaction.Environment == appstore.Sandbox)
	if apiTransactionInfo == nil {
		return false
	}
	fmtIap := &formatPayload{}
	inAppOrder := fmtIap.formatIAPTransactionV2(apiTransactionInfo)
	if inAppOrder == nil {
		logger.Infof("AppStore formatIAPTransactionV2 nil data:%+v", apiTransactionInfo)
		return true
	}
	// 处理退款订单
	dealNotificationCancelOrder([]*IAPTransactionItem{inAppOrder})
	return true
}

// 指示 AppStore 拒绝了应用开发者使用以下任一方法发起的退款请求
// Apple 拒绝了客户使用请求退款 API 在应用程序中发起的退款。
func refundDeclined(n *appstore.SubscriptionNotificationV2DecodedPayload, nItem *iap.Notification) bool {
	logger.Infof("AppStore refundDeclined %+v nItem: %+v", n, nItem)
	dataDecodedPayload := getNotificationsDataJwsDecoded(n)
	if dataDecodedPayload != nil && dataDecodedPayload.Transaction != nil {
		ProcessRefundAsk(dataDecodedPayload.Transaction, pay.IosRefundStatusReject)
	}
	return true
}

// 表明 App Store 由于客户提出的争议而撤销了之前授予的退款。如果您的应用因相关退款而撤销了内容或服务，则需要恢复它们。
// 此通知类型可适用于任何应用内购买类型：消耗型、非消耗型、非续订订阅和自动续订订阅。对于自动续订订阅，当 App Store 撤销退款时，
// 续订日期保持不变。
func refundReversed(n *appstore.SubscriptionNotificationV2DecodedPayload, nItem *iap.Notification) bool {
	return processingTransactionDat(n, nItem, nil)
}

// 指示 App Store 延长了特定订阅的订阅续订日期。您可以通过调用App Store Server API中的延长订阅续订日期
// 或为所有活跃订阅者延长订阅续订日期来请求订阅续订日期延期
func renewalExtended(n *appstore.SubscriptionNotificationV2DecodedPayload, nItem *iap.Notification) bool {
	//switch n.Subtype {
	//// App Store 成功完成为所有符合条件的订阅者延长订阅续订日期。
	//case appstore.SubTypeV2Summary:
	//
	//	// App Store 未能为特定订阅者延长订阅续订日期。
	//case appstore.SubTypeV2Failure:
	//
	//	// App Store 成功延长特定订阅的订阅续订日期。
	//default:
	//
	//}
	logger.Infof("AppStore renewalExtended %+v nItem: %+v", n, nItem)
	return true
}

// subtype表示 AppStore 正在尝试通过调用为所有活跃订阅者延长订阅续订日期 来延长您请求的订阅续订日期。
// 如果subtype是SUMMARY，则 AppStore 已完成为所有符合条件的订阅者延长续订日期。
func renewalExtension(n *appstore.SubscriptionNotificationV2DecodedPayload, nItem *iap.Notification) bool {
	//switch n.Subtype {
	//// 要获取 summary 对象
	//case appstore.SubTypeV2Summary:
	//
	//}
	logger.Infof("AppStore renewalExtended %+v nItem: %+v", n, nItem)
	return true
}

// 用户有权通过“家人共享”进行应用内购买的通知类型不再可通过共享进行。当购买者对其购买禁用“家庭共享”、购买者（或家庭成员）
// 离开家庭群组或购买者收到退款时，AppStore 会发送此通知。您的应用程序也会收到呼叫。家庭共享适用于非消耗性应用内购买和自动续订订阅。
// 有关家庭共享的更多信息，请参阅在应用程序中支持家庭共享。
func revoke(n *appstore.SubscriptionNotificationV2DecodedPayload, nItem *iap.Notification) bool {
	dataDecodedPayload := getNotificationsDataJwsDecoded(n)
	if dataDecodedPayload.Transaction == nil {
		logger.Error("AppStore subscribed dataDecodedPayload.Transaction nil")
		return false
	}
	if dataDecodedPayload.RenewalInfo == nil {
		logger.Error("AppStore subscribed dataDecodedPayload.RenewalInfo nil")
		return false
	}
	transaction := dataDecodedPayload.Transaction
	apiTransactionInfo := GetTransactionInfo(transaction.TransactionId, transaction.Environment == appstore.Sandbox)
	if apiTransactionInfo == nil {
		return false
	}
	fmtIap := &formatPayload{}
	inAppOrder := fmtIap.formatIAPTransactionV2(apiTransactionInfo)
	if inAppOrder == nil {
		logger.Infof("AppStore formatIAPTransactionV2 nil data:%+v", apiTransactionInfo)
		return true
	}
	// 处理退款订单
	dealNotificationCancelOrder([]*IAPTransactionItem{inAppOrder})
	return true
}

type DataJwsDecodedPayload struct {
	RenewalInfo *appstore.JWSRenewalInfoDecodedPayload
	Transaction *appstore.JWSTransactionDecodedPayload
}

// getNotificationsDataJwsDecoded 获取回调关键信息
func getNotificationsDataJwsDecoded(n *appstore.SubscriptionNotificationV2DecodedPayload) *DataJwsDecodedPayload {
	resp := &DataJwsDecodedPayload{}
	renewalInfoDecodedPayload, err := getRenewalInfoDecodedPayload(n.Data.SignedRenewalInfo)
	if err == nil {
		resp.RenewalInfo = renewalInfoDecodedPayload
	}
	transactionDecodedPayload, err := getTransactionDecodedPayload(n.Data.SignedTransactionInfo)
	if err == nil {
		resp.Transaction = transactionDecodedPayload
	}
	return resp
}

func getRenewalInfoDecodedPayload(signedRenewalInfo appstore.JWSRenewalInfo) (*appstore.JWSRenewalInfoDecodedPayload,
	error) {
	claims, err := ParseNotificationData(string(signedRenewalInfo))
	if err != nil {
		return nil, err
	}
	notificationData, _ := json.Marshal(claims)
	var pResult appstore.JWSRenewalInfoDecodedPayload
	err = json.Unmarshal(notificationData, &pResult)
	if err != nil {
		return nil, err
	}
	return &pResult, nil
}

func getTransactionDecodedPayload(transaction appstore.JWSTransaction) (*appstore.JWSTransactionDecodedPayload, error) {
	claims, err := ParseNotificationData(string(transaction))
	if err != nil {
		return nil, err
	}
	notificationData, _ := json.Marshal(claims)
	var pResult appstore.JWSTransactionDecodedPayload
	err = json.Unmarshal(notificationData, &pResult)
	if err != nil {
		return nil, err
	}
	return &pResult, nil
}

// ParseNotificationData jwt解密
func ParseNotificationData(tokenStr string) (map[string]interface{}, error) {
	client := appstore.New()
	token := jwt.Token{}
	if err := client.ParseNotificationV2(tokenStr, &token); err != nil {
		return nil, err
	}
	claims, ok := token.Claims.(jwt.MapClaims)
	if !ok {
		return nil, errors.New("解密失败")
	}
	return claims, nil
}

// validateNotification 验证回调信息
func validateNotification(n *appstore.SubscriptionNotificationV2DecodedPayload) bool {
	dataDecodedPayload := getNotificationsDataJwsDecoded(n)
	if dataDecodedPayload.Transaction == nil {
		logger.Error("AppStore subscribed dataDecodedPayload.Transaction nil")
		return false
	}
	if dataDecodedPayload.RenewalInfo == nil {
		logger.Error("AppStore subscribed dataDecodedPayload.RenewalInfo nil")
		return false
	}
	transaction := dataDecodedPayload.Transaction
	apiTransactionInfo := GetTransactionInfo(transaction.TransactionId, transaction.Environment == appstore.Sandbox)
	if apiTransactionInfo == nil {
		return false
	}
	if transaction.OriginalTransactionId != apiTransactionInfo.OriginalTransactionId {
		logger.Fatalf("AppStore Notification:%+v api:%+v", *transaction, *apiTransactionInfo)
		return false
	}
	return true
}

// GetTransactionHistory 获取历史交易记录
func GetTransactionHistory(originalTransactionId string, isSandbox bool) []*api.JWSTransaction {
	a := api.NewStoreClient(getStoreConfig(isSandbox))
	ctx := context.Background()
	now := time.Now().UnixNano() / int64(time.Millisecond)
	values := &url.Values{}
	// 您请求的交易历史记录的时间跨度的可选开始日期。一年以外的订单不做处理，防止之前表内交易数据不完整，导致多加权益
	values.Add("startDate", strconv.FormatInt(util.PastTimeMilli(now, 460), 10))
	// 交易历史记录的可选排序顺序。ASCENDING, DESCENDING
	values.Add("sort", "DESCENDING")
	// 应用内所有权类型限制 FAMILY_SHARED 服务中受益的家庭成员 PURCHASED 该交易属于购买者。
	values.Add("inAppOwnershipType", "PURCHASED")
	response, err := a.GetTransactionHistory(ctx, originalTransactionId, values)
	if err != nil {
		logger.Warn("AppStore GetTransactionHistory err", err, originalTransactionId, isSandbox)
		return nil
	}
	resp := make([]*api.JWSTransaction, 0)
	for _, v := range response {
		transactions, tErr := a.ParseSignedTransactions(v.SignedTransactions)
		if tErr != nil {
			logger.Error("AppStore ParseSignedTransactions err", err)
			continue
		}
		resp = append(resp, transactions...)
	}
	sort.Slice(resp, func(i, j int) bool {
		return resp[i].PurchaseDate > resp[j].PurchaseDate
	})
	return resp
}

// GetTransactionInfo 获取交易信息
func GetTransactionInfo(transactionID string, isSandbox bool) *api.JWSTransaction {
	a := api.NewStoreClient(getStoreConfig(isSandbox))
	ctx := context.Background()
	for i := 0; i < 3; i++ {
		response, err := a.GetTransactionInfo(ctx, transactionID)
		if err != nil {
			logger.Warn("AppStore GetTransactionInfo err", err)
			time.Sleep(time.Second * 1)
			continue
		}
		tr, err := a.ParseSignedTransaction(response.SignedTransactionInfo)
		if err != nil {
			logger.Warn("AppStore GetTransactionInfo err", err)
			continue
		}
		return tr
	}
	return nil
}

func getStoreConfig(isSandbox bool) *api.StoreConfig {
	cfg := config.Get().AppStore
	return &api.StoreConfig{
		KeyContent: []byte(library.AppStoreAccountPrivateKey),
		KeyID:      cfg.KeyID,
		BundleID:   cfg.BundleID,
		Issuer:     cfg.Issuer,
		Sandbox:    isSandbox,
	}
}

type formatPayload struct {
	IosAuditSwitchInfo bool
}

func (f *formatPayload) formatIAPTransactionV2(tran *api.JWSTransaction) *IAPTransactionItem {
	mt := int64(1000)
	isSandBox := library.No
	if tran.Environment == api.Sandbox {
		isSandBox = library.Yes
	}

	var isInIntroOfferPeriod bool
	if tran.OfferType > 0 {
		isInIntroOfferPeriod = true
	}
	logger.Infof("formatIAPTransactionV2 %+v", tran)
	info := &IAPTransactionItem{
		ProductID:             tran.ProductID,
		WebItemID:             tran.WebOrderLineItemId,
		TransactionID:         tran.TransactionID,
		OriginalTransactionID: tran.OriginalTransactionId,
		PurchaseTime:          tran.PurchaseDate / mt,
		ExpireTime:            tran.ExpiresDate / mt,
		IsSandBox:             int32(isSandBox),
		IsUpgraded:            strconv.FormatBool(tran.IsUpgraded),
		IsInIntroOfferPeriod:  strconv.FormatBool(isInIntroOfferPeriod),
	}
	if tran.RevocationDate != 0 {
		info.CancelTime = tran.RevocationDate / mt
	}
	if config.Get().Service.Env == microservice.Product && f.IosAuditSwitchInfo {
		return info
	}
	// 10分钟的Sandbox 直接抛弃
	if tran.Environment == api.Sandbox && info.PurchaseTime < time.Now().Unix()-7200 {
		logger.Info("AppStore 10分钟的Sandbox 直接抛弃", info)
		return nil
	}
	return info
}

func getUidByTransactionV2(inAppOrderList []*IAPTransactionItem) int64 {
	var uid int64
	for _, v := range inAppOrderList {
		iOrder := iap.TbIOSOrder.GetItemByTran(v.OriginalTransactionID, v.TransactionID)
		if iOrder != nil && uid == 0 {
			webOrder := order.TbWebOrder.GetItemByOrderID(iOrder.OrderID)
			if webOrder != nil {
				uid = webOrder.UID
			}
		}
		if uid > 0 {
			break
		}
	}
	return uid
}

// processingTransactionDat 处理交易信息
func processingTransactionDat(n *appstore.SubscriptionNotificationV2DecodedPayload,
	nItem *iap.Notification, transactionItemHistory []*IAPTransactionItem) bool {
	if !validateNotification(n) {
		return false
	}
	inAppOrderList := make([]*IAPTransactionItem, 0)
	dataDecodedPayload := getNotificationsDataJwsDecoded(n)
	if len(transactionItemHistory) > 1 {
		inAppOrderList = transactionItemHistory
	} else {
		transactionHistoryList := GetTransactionHistory(dataDecodedPayload.Transaction.OriginalTransactionId,
			dataDecodedPayload.Transaction.Environment == appstore.Sandbox)
		if len(transactionHistoryList) < 1 {
			return false
		}
		fmtIap := &formatPayload{}
		for _, v := range transactionHistoryList {
			transactionV2 := fmtIap.formatIAPTransactionV2(v)
			if transactionV2 == nil {
				logger.Infof("AppStore formatIAPTransactionV2 nil data:%+v", v)
				continue
			}
			inAppOrderList = append(inAppOrderList, transactionV2)
		}
	}
	uid := getUidByTransactionV2(inAppOrderList)
	if uid == 0 {
		logger.Warn("AppStore 没有对应UID，不处理", dataDecodedPayload.Transaction.OriginalTransactionId)
		return false
	}
	iapReceiptInfo := &IAPClientPayReceipt{UID: uid}
	rsp := analysisIAPOrderList(inAppOrderList, iapReceiptInfo)
	rspByte, _ := json.Marshal(rsp)
	nItem.ProcessResult = string(rspByte)
	logger.Info("AppStore IOS服务端通知处理receipt", nItem.ID, uid, string(rspByte))
	return true
}

// 1 自动续订订阅已激活。
// 2 自动续订订阅已过期。
// 3 自动续订订阅处于计费重试期。
// 4 自动续订订阅处于计费宽限期。
// 5 自动续订订阅已撤销。App Store 已退还交易费用或已将其从家庭共享中撤销。
// RenewStatusCheckBySubscriptionStatuses 获取订阅状态更新状态
func RenewStatusCheckBySubscriptionStatuses(originalTransactionId string, isSandbox bool) {
	a := api.NewStoreClient(getStoreConfig(isSandbox))
	ctx := context.Background()
	for i := 0; i < 3; i++ {
		response, err := a.GetALLSubscriptionStatuses(ctx, originalTransactionId, nil)
		if err != nil {
			logger.Warn("AppStore RenewStatusCheckBySubscriptionStatuses err", err)
			time.Sleep(time.Second * 1)
			continue
		}
		for _, v := range response.Data {
			for _, s := range v.LastTransactions {
				if s.OriginalTransactionId == originalTransactionId {
					autoRenewStatus := pay.ContractChangeTypeEnum.ContractChangeTypeAdd
					if s.Status == 5 {
						autoRenewStatus = pay.ContractChangeTypeEnum.ContractChangeTypeDelete
					}
					changeTransactionRenewStatus(originalTransactionId, autoRenewStatus)
				}
			}
		}
	}
}

// formatConsumptionBody
func formatConsumptionBody(tc *appstore.JWSTransactionDecodedPayload) *api.ConsumptionRequestBody {
	transactionHistoryList := GetTransactionHistory(tc.OriginalTransactionId,
		tc.Environment == appstore.Sandbox)
	if len(transactionHistoryList) < 1 {
		return nil
	}
	inAppOrderList := make([]*IAPTransactionItem, 0)
	fmtIap := &formatPayload{}
	for _, v := range transactionHistoryList {
		transactionV2 := fmtIap.formatIAPTransactionV2(v)
		if transactionV2 == nil {
			logger.Infof("AppStore formatConsumptionBody nil data:%+v", v)
			continue
		}
		inAppOrderList = append(inAppOrderList, transactionV2)
	}
	uid := getUidByTransactionV2(inAppOrderList)
	if uid == 0 {
		logger.Warn("AppStore 没有对应UID，不处理", tc)
		return nil
	}
	account := dbuser.TbAccount.GetUserByID(uid)
	if account == nil {
		return nil
	}
	return &api.ConsumptionRequestBody{
		AccountTenure:            getConsumptionAccountTenure(account),
		AppAccountToken:          tc.AppAccountToken,
		CustomerConsented:        true,
		ConsumptionStatus:        getConsumptionStatus(),
		LifetimeDollarsPurchased: getLifetimeDollarsPurchased(account),
		Platform:                 1,
		RefundPreference:         2,
		UserStatus:               1,
	}
}

// 0 帐户年龄未声明。使用此值可避免提供此字段的信息。
// 1 账户年龄为0至3天之间。
// 2 账户年龄为 3 至 10 天。
// 3 账户年龄为 10 至 30 天。
// 4 账户年龄在 30 至 90 天之间。
// 5 账户年龄在90至180天之间。
// 6 账户年龄在180至365天之间。
// 7 帐户年龄已超过 365 天。
func getConsumptionAccountTenure(account *dbuser.Account) int32 {
	accountTenure := util.Days(account.CreateTime, time.Now().Unix(), false)
	switch {
	case 0 <= accountTenure && accountTenure < 3:
		return 1
	case 3 <= accountTenure && accountTenure < 10:
		return 2
	case 10 <= accountTenure && accountTenure < 30:
		return 3
	case 30 <= accountTenure && accountTenure < 90:
		return 4
	case 90 <= accountTenure && accountTenure < 180:
		return 5
	case 180 <= accountTenure && accountTenure < 365:
		return 6
	default:
		return 7
	}
}

// 0 消费状态未声明。使用此值可避免为该字段提供信息。
// 1 应用内购买不会被消耗。
// 2 应用内购买已部分消耗。
// 3 应用内购买已全部消耗。
func getConsumptionStatus() int32 {
	return 2
}

// 0 终身购买金额未声明。使用此值可避免提供此字段的信息。
// 1 终生购买金额为 0 美元。
// 2 终身购买金额在0.01至49.99美元之间。
// 3 终身购买金额在 50–99.99 美元之间。
// 4 终身购买金额在 100–499.99 美元之间。
// 5 终身购买金额在 500–999.99 美元之间。
// 6 终身购买金额在 1000–1999.99 美元之间。
// 7 终生购买金额超过 2000 美元。
func getLifetimeDollarsPurchased(account *dbuser.Account) int32 {
	logger.Infof("AppStore getLifetimeDollarsPurchased %+v", account)
	return 0
}

func LookUpOrderID(orderID string) *IAPOrderAnalysisRsp {
	a := api.NewStoreClient(getStoreConfig(false))
	ctx := context.Background()

	for i := 0; i < 3; i++ {
		response, err := a.LookupOrderID(ctx, orderID)
		if err != nil {
			logger.Error("AppStore LookUpOrderID err", err, orderID)
			time.Sleep(time.Second * 1)
			continue
		}
		transactionHistoryList, tErr := a.ParseSignedTransactions(response.SignedTransactions)
		if tErr != nil {
			logger.Error("AppStore ParseSignedTransactions err", err, orderID)
			continue
		}
		sort.Slice(transactionHistoryList, func(i, j int) bool {
			return transactionHistoryList[i].PurchaseDate > transactionHistoryList[j].PurchaseDate
		})
		fmtIap := &formatPayload{}
		inAppOrderList := make([]*IAPTransactionItem, 0)
		for _, v := range transactionHistoryList {
			transactionV2 := fmtIap.formatIAPTransactionV2(v)
			if transactionV2 == nil {
				logger.Infof("AppStore LookUpOrderID formatIAPTransactionV2 nil data:%+v orderid:%s", v, orderID)
				continue
			}
			inAppOrderList = append(inAppOrderList, transactionV2)
		}
		// 处理退款
		dealNotificationCancelOrder(inAppOrderList)
		uid := getUidByTransactionV2(inAppOrderList)
		if uid == 0 {
			logger.Warn("AppStore LookUpOrderID 没有对应UID，不处理", inAppOrderList)
			continue
		}
		iapReceiptInfo := &IAPClientPayReceipt{UID: uid}
		return analysisIAPOrderList(inAppOrderList, iapReceiptInfo)
	}
	return nil
}

func getIAPTransactionItemHistory(originalTransactionId string, isSandbox bool) []*IAPTransactionItem {
	transactionHistoryList := GetTransactionHistory(originalTransactionId,
		isSandbox)
	if len(transactionHistoryList) < 1 {
		return nil
	}
	inAppOrderList := make([]*IAPTransactionItem, 0)
	fmtIap := &formatPayload{}
	for _, v := range transactionHistoryList {
		transactionV2 := fmtIap.formatIAPTransactionV2(v)
		if transactionV2 == nil {
			logger.Infof("AppStore formatIAPTransactionV2 nil data:%+v", v)
			continue
		}
		inAppOrderList = append(inAppOrderList, transactionV2)
	}
	return inAppOrderList
}
