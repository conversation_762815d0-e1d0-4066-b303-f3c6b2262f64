package order

import (
	"context"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	yogacs "gitlab.dailyyoga.com.cn/protogen/yoga-cs-go/yoga-cs"
	dbProduct "gitlab.dailyyoga.com.cn/server/children/databases/product"
	"gitlab.dailyyoga.com.cn/server/children/grpc"
	"gitlab.dailyyoga.com.cn/server/children/library"
)

func LogCrackOrder(uid int64, orderID string, product *dbProduct.WebProduct) {
	req := &yogacs.LogCrackOrderReq{
		Project:         int32(library.ProjectTypeEnum.Children),
		OrderID:         orderID,
		UID:             uid,
		RealProductID:   product.IOSProductID,
		RealProductName: product.Name,
		RealMoneyPaid:   int64(product.OriginPrice),
	}

	resp, err := grpc.GetYoGaCsClientClient().LogCrackOrder(context.Background(), req)
	if err != nil {
		logger.Error("调用黑产订单记录rpc，失败", err)
		return
	}
	if resp.Msg != "" && resp.Msg != "SUCCESS" {
		logger.Error("调用黑产订单记录rpc，返回失败", resp.Msg)
	}
}
