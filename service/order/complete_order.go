package order

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/server/children/config"
	dbc "gitlab.dailyyoga.com.cn/server/children/databases/client"
	dbo "gitlab.dailyyoga.com.cn/server/children/databases/order"
	dbp "gitlab.dailyyoga.com.cn/server/children/databases/product"
	dbu "gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libcache "gitlab.dailyyoga.com.cn/server/children/library/cache"
	libc "gitlab.dailyyoga.com.cn/server/children/library/client"
	libp "gitlab.dailyyoga.com.cn/server/children/library/pay"
	libproduct "gitlab.dailyyoga.com.cn/server/children/library/product"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/equity"
	srvp "gitlab.dailyyoga.com.cn/server/children/service/pay"
	"gitlab.dailyyoga.com.cn/server/children/service/sensor"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
	"gitlab.dailyyoga.com.cn/server/gopay"
)

type Complete struct {
}

var SrvOrderComplete Complete

const (
	fenPerYuan = 100
)

type AlipaySignNotify struct {
	EAgreementNo string `json:"external_agreement_no"`
	AgreementNo  string `json:"agreement_no"`
	Status       string `json:"status"`
	SignTime     string `json:"sign_time"`
	NotifyTime   string `json:"notify_time"`
	AliUserID    string `json:"alipay_user_id"`
}

// AlipayContract 支付宝签约完成回调
// nolint
func (c *Complete) AlipayContract(notify gopay.BodyMap, subMode libp.SubscribeModeInt) bool {
	aliNotify := c.ParseSignNotify(notify)
	if aliNotify == nil {
		return false
	}
	contractInfo := dbo.TbAlipayContract.GetItemByContractCode(aliNotify.EAgreementNo)
	logger.Infof("d支付宝 contractInfo %+v", contractInfo)
	if contractInfo == nil {
		logger.Error("支付宝签约信息为空", notify)
		return false
	}
	changeType := libp.AliContractToChangeType[aliNotify.Status]
	// 多次回调或者解约比签约过来的晚的情况
	if contractInfo.ChangeType == libp.ContractChangeTypeEnum.ContractChangeTypeDelete ||
		contractInfo.ChangeType == changeType {
		return true
	}
	contractInfo.ContractID = aliNotify.AgreementNo
	if changeType == 0 {
		logger.Error("未知的签约类型", notify)
		return false
	}
	contractInfo.ChangeType = changeType
	if err := contractInfo.Update(); err != nil {
		logger.Error("签约信息保存失败", notify)
		return false
	}
	order := dbo.TbWebOrder.GetItemByOrderID(contractInfo.OrderID)
	if order == nil {
		logger.Error("不存在的订单", notify)
		return false
	}
	notifyTime, err := time.ParseInLocation("2006-01-02 15:04:05", aliNotify.NotifyTime, time.Local)
	if err != nil {
		logger.Error(err)
		return false
	}
	if subMode == libp.SubscribeModeEnum.PayAndSubscribe &&
		(contractInfo.OfferType == libproduct.ConstOfferTypeNo || contractInfo.OfferType == libproduct.ConstOfferTypeFirstBuy) {
		product := dbp.TbWebProduct.GetItemByID(order.ProductID)
		if product == nil {
			logger.Error("支付宝订阅回调未查到有效产品", notify)
			return false
		}
		execTimeInt := equity.CalcVipTime(notifyTime.Unix(), product.DurationType, product.DurationValue)
		notifyTime = time.Unix(execTimeInt, 0)
	} else if contractInfo.OfferType == libproduct.ConstOfferTypeTrial ||
		contractInfo.OfferType == libproduct.ConstOfferTypeTrialFirstBuy {
		execTimeInt := equity.CalcVipTime(notifyTime.Unix(), int(libproduct.DurationTypeEnum.Day),
			contractInfo.OfferTrialDay)
		notifyTime = time.Unix(execTimeInt, 0)
	}
	sub := &dbu.WebProductSubscribeUser{
		UID:                    order.UID,
		ProductID:              int(order.ProductID),
		OrderID:                order.OrderID,
		OriginalOrderID:        order.OrderID,
		ThirdPartTransactionID: aliNotify.EAgreementNo,
		OriginalTransactionID:  aliNotify.AgreementNo,
		Status:                 changeType,
		PayType:                libp.PayTypeAlipay,
		ExpiresTime:            notifyTime.Unix(),
		OfferType:              contractInfo.OfferType,
	}
	logger.Infof("d支付宝 WebProductSubscribeUser %+v", sub)
	if err := sub.FindOrCreate(); err != nil {
		logger.Error(err)
		return false
	}
	if changeType == libp.ContractChangeTypeEnum.ContractChangeTypeAdd {
		// 订阅上报
		sensor.ReportSubscribe(order, subMode, contractInfo.ContractCode)
		// 支付中签约不需要再扣款，先签约后扣款需要扣第一次款
		if subMode == libp.SubscribeModeEnum.PayAndSubscribe {
			// 2024-4-7 water 签约完成解约
			if err := c.UnSubLowProduct(order); err != nil {
				logger.Error("触发升降级逻辑报错", err)
			}
			return true
		}
		safelygo.GoSafelyByTraceID(func() {
			c.ChargeUserContract(sub, &ContractChargeParams{
				IsRenew: false,
				IsStep:  false,
			})
			if err := c.UnSubLowProduct(order); err != nil {
				logger.Error("触发升降级逻辑报错", err)
			}
		})
		return true
	}
	// 取消订阅上报
	sensor.ReportUnSubscribe(order, contractInfo.ContractCode, libp.UnsubscribeTypeEnum.User)
	return true
}

type UnSubProduct struct {
	OrderID   string
	ProductID int64
	PayType   int
	Duration  int64
}

func (c *Complete) UnSubLowProduct(order *dbo.WebOrder) error {
	subList := dbu.TbWPSubU.GetValidSubscribeUser(order.UID)
	sort.Slice(subList, func(i, j int) bool {
		return subList[i].CreateTime < subList[j].CreateTime
	})
	pMap := dbp.TbWebProduct.GetAllProductMap()
	subUserMapList := make(map[int][]*UnSubProduct)
	now := time.Now().Unix()
	for _, v := range subList {
		if v.PayType != libp.PayTypeAlipay {
			continue
		}
		if _, ok := subUserMapList[pMap[int64(v.ProductID)].VipType]; !ok {
			subUserMapList[pMap[int64(v.ProductID)].VipType] = make([]*UnSubProduct, 0)
		}
		subUserMapList[pMap[int64(v.ProductID)].VipType] = append(subUserMapList[pMap[int64(v.ProductID)].VipType],
			&UnSubProduct{
				OrderID:   v.OrderID,
				ProductID: int64(v.ProductID),
				PayType:   v.PayType,
				Duration: equity.CalcVipTime(now, pMap[int64(v.ProductID)].DurationType,
					pMap[int64(v.ProductID)].DurationValue),
			})
	}
	unsubList := make([]*UnSubProduct, 0)
	if len(subUserMapList[pMap[order.ProductID].VipType]) > 0 {
		unsubList = append(unsubList, subUserMapList[pMap[order.ProductID].VipType]...)
	}
	// 降序
	if len(unsubList) > 1 {
		sort.Slice(unsubList, func(i, j int) bool {
			return unsubList[i].Duration > unsubList[j].Duration
		})
		needHandled := unsubList[1:]
		for _, v := range needHandled {
			UnsubscribeByOrderID(v.OrderID, libp.UnsubscribeTypeEnum.Auto)
		}
	}
	return nil
}

// ParseSignNotify 解析签约通知
func (c *Complete) ParseSignNotify(notify gopay.BodyMap) *AlipaySignNotify {
	notifyByte, err := json.Marshal(notify)
	if err != nil {
		logger.Error(err)
		return nil
	}
	aliNotify := AlipaySignNotify{}
	err = json.Unmarshal(notifyByte, &aliNotify)
	if err != nil {
		logger.Error(err)
		return nil
	}
	asn := dbo.AlipayContractNotify{
		ContractCode: aliNotify.EAgreementNo,
		ContractID:   aliNotify.AgreementNo,
		ChangeType:   aliNotify.Status,
		SignTime:     aliNotify.SignTime,
		NotifyTime:   aliNotify.NotifyTime,
		AlipayUserID: aliNotify.AliUserID,
	}
	if err := asn.Save(); err != nil {
		return nil
	}
	return &aliNotify
}

// 支付宝签约后扣款或续订扣款
func (*Complete) AlipayContractTradePay(params *ContractChargeParams) *srvp.TradePayRes {
	product := dbp.TbWebProduct.GetItemByID(params.Contract.ProductID)
	if product == nil {
		logger.Error("自动续订扣款找不到商品")
		return nil
	}
	params.Product = product
	// 处理优惠产品价格
	needDeduction := FormatOffer(params.Contract, params.IsRenew, product)
	if !needDeduction {
		return &srvp.TradePayRes{
			Success: true,
		}
	}
	charge := dbo.AlipayContractCharge{
		OrderID:      params.Contract.OrderID,
		UID:          params.Contract.UID,
		ProductID:    params.Contract.ProductID,
		ContractCode: params.Contract.ContractCode,
		ContractID:   params.Contract.ContractID,
		OrderAmount:  product.Price,
		IsStepPrice:  library.No,
		ChargeDate:   time.Now().Format("2006-01-02"),
	}
	stepAmount := GetStepAmount(params.ChargeList, params.Contract, params.StepConfig)
	stepAmountFloat := float64(stepAmount)
	if stepAmount > 0 && params.StepConfig != nil && params.StepConfig.ProductType > 0 {
		lowLimit := libproduct.LowLimitByStepPType[libproduct.StepPTypeInt(params.StepConfig.ProductType)]
		if lowLimit > 0 && charge.OrderAmount > lowLimit {
			if charge.OrderAmount-stepAmountFloat >= lowLimit {
				charge.OrderAmount -= stepAmountFloat
			} else {
				stepAmountFloat = charge.OrderAmount - lowLimit
				charge.OrderAmount = lowLimit
			}
			charge.StepAmount = stepAmountFloat
			charge.IsStepPrice = library.Yes
		}
	}
	if params.IsJob {
		// 获取订单号，保持只有两个订单号 一个原价订单号，一个阶梯价订单号
		charge.OrderID = GetOrderIDByCharge(params.ChargeList, charge.OrderAmount, params.Product)
	}
	logger.Infof("支付宝扣款 AlipayContractTradePay请求记录 %+v", charge)
	if err := charge.Save(); err != nil {
		logger.Error(err)
		return nil
	}
	res := srvp.SrvAlipay.TradePay(charge.OrderID, charge.ContractID, params.Contract.AppID, charge.OrderAmount)
	if charge.IsStepPrice == library.Yes {
		res.IsStepPrice = true
		res.StepPrice = charge.StepAmount
	}
	return res
}

func GetStepAmount(chargeList []*dbo.AlipayContractCharge,
	contract *dbo.AlipayContract, stepConfig *ChargeStepSwitchConfig) int {
	if !stepConfig.IsSwitch || stepConfig.ChargeDays == 0 {
		return 0
	}
	tDay := time.Now().Day()
	// 1号 15号 20号 发薪日原价扣款
	if tDay == 1 || tDay == 15 || tDay == 20 {
		return 0
	}
	if time.Unix(contract.CreateTime, 0).Format("2006-01-02") == time.Now().Format("2006-01-02") {
		return 0
	}
	if len(stepConfig.StepConfig) == 0 {
		return 0
	}
	dateMap := make(map[string]int)
	dateMap[time.Now().Format("2006-01-02")] = 0
	todayStep := 1
	for _, v := range chargeList {
		dateMap[v.ChargeDate]++
		if v.ChargeDate == time.Now().Format("2006-01-02") {
			todayStep++
		}
	}
	for _, v := range stepConfig.StepConfig {
		if len(dateMap) > v.Min && len(dateMap) <= v.Max {
			if len(v.Step) == 0 {
				continue
			}
			if a, ok := v.Step[todayStep]; ok {
				return a
			}
		}
	}
	return 0
}

func GetOrderIDByCharge(chargeList []*dbo.AlipayContractCharge,
	orderAmount float64, productItem *dbp.WebProduct) string {
	sameAmountOrderID := ""
	sort.Slice(chargeList, func(i, j int) bool {
		return chargeList[i].CreateTime < chargeList[j].CreateTime
	})
	// 取最新的阶梯价订单号 或者原价订单号
	for _, v := range chargeList {
		if v.OrderAmount <= 0 {
			continue
		}
		if v.OrderAmount == orderAmount {
			sameAmountOrderID = v.OrderID
		}
	}
	logger.Info("相同价格的订单号", len(chargeList), sameAmountOrderID, orderAmount)
	if sameAmountOrderID == "" {
		return GenerateOrderID(productItem.ID)
	}
	firstTime := time.Now().Unix()
	for _, v := range chargeList {
		if v.OrderID == sameAmountOrderID && v.CreateTime < firstTime {
			firstTime = v.CreateTime
		}
	}
	logger.Info("相同价格的订单号第一次时间", sameAmountOrderID, firstTime)
	var maxOrderDay int64 = 15
	if (util.FormatStartTime(time.Now().Unix())-util.FormatStartTime(firstTime))/library.DayTime+1 <= maxOrderDay {
		return sameAmountOrderID
	}
	logger.Info("相同价格的订单号生成订单号", sameAmountOrderID, firstTime)
	return GenerateOrderID(productItem.ID)
}

// FormatOffer 处理优惠扣款 返回值 标记 要不要进行扣款
// nolint
func FormatOffer(contract *dbo.AlipayContract, isRenew bool, product *dbp.WebProduct) bool {
	if contract.OfferType > libproduct.ConstOfferTypeNo {
		switch contract.OfferType {
		case libproduct.ConstOfferTypeFirstBuy:
			list := dbo.TbAlipayCharge.GetValidChargeByCode(contract.ContractCode)
			if len(list) < int(contract.OfferFirstBuyCycle) {
				product.Price = contract.OfferFirstBuyPrice
			}
		case libproduct.ConstOfferTypeTrial:
			if !isRenew {
				if contract.OfferTrialPrice == 0 {
					if !CompleteOrder(&dbo.CompleteNotify{OrderID: contract.OrderID}, false) {
						logger.Error("0元试用未成功 ConstOfferTypeTrial", contract.OrderID)
					}
					return false
				}
				product.Price = contract.OfferTrialPrice
			}
		case libproduct.ConstOfferTypeTrialFirstBuy:
			if !isRenew {
				if contract.OfferTrialPrice == 0 {
					if !CompleteOrder(&dbo.CompleteNotify{OrderID: contract.OrderID}, false) {
						logger.Error("0元试用未成功 ConstOfferTypeTrialFirstBuy", contract.OrderID)
					}
					return false
				}
				product.Price = contract.OfferTrialPrice
			} else {
				list := dbo.TbAlipayCharge.GetValidChargeByCode(contract.ContractCode)
				if len(list) < int(contract.OfferFirstBuyCycle) {
					product.Price = contract.OfferFirstBuyPrice
				}
			}
		}
	}
	return true
}

type ChargeStepSwitchConfig struct {
	ProductType   int               `json:"product_type"`
	Title         string            `json:"title"`
	IsSwitch      bool              `json:"is_switch"`
	ChargeDays    int               `json:"charge_days"`
	ChargeDaysMax int               `json:"charge_days_max"`
	StepConfig    []*StepConfigItem `json:"step_config"`
}

type StepConfigItem struct {
	Min  int         `json:"min"`
	Max  int         `json:"max"`
	Step map[int]int `json:"step"`
}

type ContractChargeParams struct {
	IsRenew     bool
	IsStep      bool
	IsJob       bool
	Contract    *dbo.AlipayContract
	ChargeList  []*dbo.AlipayContractCharge
	Product     *dbp.WebProduct
	StepConfig  *ChargeStepSwitchConfig
	TContract   *dbo.TiktokContract
	TChargeList []*dbo.TiktokContractCharge
}

// ChargeUserContract 用户订阅当前周期扣款
// nolint
func (c *Complete) ChargeUserContract(sub *dbu.WebProductSubscribeUser, params *ContractChargeParams) {
	if sub == nil || params == nil {
		return
	}
	params.IsJob = params.IsRenew
	params.Contract = dbo.TbAlipayContract.GetItemByContractCode(sub.ThirdPartTransactionID)
	// 用户在签约完后解约，如果获取到的是用户已解约的数据，这里会报错
	if params.Contract == nil || params.Contract.ChangeType != libp.ContractChangeTypeEnum.ContractChangeTypeAdd {
		ChargePlanUpdate(sub.ID, 0, libp.AbnormalTypeUnsign, params.IsJob, params.IsStep, "")
		logger.Warn("支付宝自动续订找不到有效协议", *sub)
		return
	}
	now := time.Now()
	rd := cache.GetCRedis()
	lockKey := fmt.Sprintf("%s:%d", libcache.LockAlipaySubscribeCharge, sub.UID)
	if lock := rd.Lock(lockKey, cache.LockExpiration(time.Minute)); !lock {
		logger.Info("支付宝扣费锁失败", sub.UID)
		return
	}
	defer func() {
		if err := rd.Unlock(lockKey); err != nil {
			logger.Error(err)
		}
	}()
	// 已注销用户不再处理续订
	account := dbu.TbAccount.GetUserByID(sub.UID)
	if account == nil {
		ChargePlanUpdate(sub.ID, 0, libp.AbnormalTypeLogoff, params.IsJob, params.IsStep, "")
		logger.Warn("支付宝扣费,用户信息为空", sub.UID)
		return
	}
	params.Product = dbp.TbWebProduct.GetItemByID(params.Contract.ProductID)
	if params.Product == nil {
		logger.Error("自动续订扣款找不到商品")
		return
	}
	params.StepConfig = GetStepConfigByProduct(params.Product)
	if sub.BeginFailTime > 0 {
		// 获取从失败开始本期扣款次数
		params.ChargeList = dbo.TbAlipayCharge.GetChargeListByRange(util.FormatStartTime(sub.BeginFailTime),
			now.Unix(), sub.ThirdPartTransactionID)
	}
	todayCount := 0
	if len(params.ChargeList) > 0 {
		if !params.StepConfig.IsSwitch || params.StepConfig.ChargeDays == 0 {
			logger.Info("支付宝重试扣款：重试开关关闭周期推后", sub.ID)
			aliCyclePushback(params.Contract, sub)
			ChargePlanUpdate(sub.ID, 0, libp.AbnormalTypeMaxChargeDay, params.IsJob, params.IsStep, "")
			return
		}
		dateMap := make(map[string]int)
		dateMap[time.Now().Format("2006-01-02")] = 0
		for _, v := range params.ChargeList {
			// 已经有请求成功的，不再处理
			if v.Status == library.Yes {
				aliCyclePushback(params.Contract, sub)
				ChargePlanUpdate(sub.ID, 0, libp.AbnormalTypeMaxChargeDay, params.IsJob, params.IsStep, "")
				logger.Info("支付宝重试扣款：已有扣款记录:", sub.ID)
				return
			}
			dateMap[v.ChargeDate]++
			if v.ChargeDate == time.Now().Format("2006-01-02") {
				todayCount++
			}
		}
		if (len(dateMap) == params.StepConfig.ChargeDays && todayCount >= libp.LimitMaxChargeOneDay) || len(dateMap) > params.StepConfig.ChargeDays {
			logger.Info("支付宝重试扣款：达到重试上限周期推后", sub.ID)
			aliCyclePushback(params.Contract, sub)
			ChargePlanUpdate(sub.ID, 0, libp.AbnormalTypeMaxChargeDay, params.IsJob, params.IsStep, "")
			return
		}
		if todayCount >= libp.LimitMaxChargeOneDay {
			logger.Info("支付宝重试扣款：当天已达到最大扣款次数", sub.ID)
			return
		}
		if todayCount > 0 && !params.IsStep {
			logger.Info("支付宝重试扣款：当天已有扣款记录且非阶梯扣款", sub.ID)
			return
		}
	}
	// 2023/2/16 13:45 water 仅在脚本执行的时候会发生 修复1月在29-31号购买会员签约时间问题
	// 2023/2/22 09:53 water 特殊场景：用户在首次支付，创建签约订单为2023-02-21 23:59:57
	// 服务器收到回调扣款时间为2023-02-22 00:00:09 支付宝在扣款时报扣款日期不在签约时的允许范围之内 导致用户会员并未添加
	hTimeNode := time.Date(now.Year(), now.Month(), now.Day(), 00, 10, 0, 0, time.Local)
	if params.IsRenew || now.Unix() < hTimeNode.Unix() {
		if userAgreement := srvp.SrvAlipay.UserAgreementQuery(params.Contract.ContractCode,
			params.Contract.AppID); userAgreement != nil && userAgreement.Response.Code == "10000" &&
			userAgreement.Response.NextDeductTime != "" {
			logger.Infof("D签约信息 uid:%d 支付宝签约接口返回:%#v 用户实际签约:%#v", sub.UID, userAgreement.Response, *sub)
			if nextDeductTime, err := time.ParseInLocation("2006-01-02", userAgreement.Response.NextDeductTime,
				time.Local); err == nil {
				nextDeductTimeUnix := nextDeductTime.Unix()
				thirdNextDeductFormat := time.Date(now.Year(), now.Month(),
					now.Day(), 0, 0, 0, 0, time.Local)
				thirdNextDeductUnix := thirdNextDeductFormat.Unix()
				if thirdNextDeductUnix > nextDeductTimeUnix {
					_ = srvp.SrvAlipay.AgreementChargeDateChange(params.Contract.ContractID, params.Contract.AppID, now.Unix())
					return
				}
			}
		}
	}
	chargeSuccess := dbo.TbAlipayCharge.GetValidChargeByCode(params.Contract.ContractCode)
	originOrder := dbo.TbWebOrder.GetItemByOrderID(params.Contract.OrderID)
	// 续订重新生成订单ID
	if params.IsRenew && (originOrder == nil || originOrder.OrderStatus == libp.OrderStatus.Paid || len(chargeSuccess) > 0) {
		params.IsRenew = true
	} else {
		params.IsRenew = false
	}
	// 挑战赛首个周期不重试
	if !params.IsRenew && len(params.ChargeList) > 0 && originOrder.ChallengeID > 0 {
		ChargePlanUpdate(sub.ID, 0, libp.AbnormalTypeUnsign, params.IsJob, params.IsStep, "")
		return
	}
	logger.Info("支付宝自动续订，发起扣款", sub.ID, sub.UID)
	// 发起扣款
	res := c.AlipayContractTradePay(params)
	// 扣款成功，直接返回，上报在完成订单回调里面上报
	if res != nil && res.Success {
		ChargePlanUpdate(sub.ID, library.Yes, 0, params.IsJob, params.IsStep, res.ErrSubCode)
		logger.Info("支付宝续订扣款成功，直接返回", params.Contract.ContractCode)
		return
	}
	ChargePlanUpdate(sub.ID, library.No, 0, params.IsJob, params.IsStep, res.ErrSubCode)
	if err := changeNextChargeDate(sub, params.Contract); err != nil {
		logger.Warn("扣款失败后修改下次扣款时间错误", err)
	}
	// 扣款失败的任务根据阶梯规则加入延时队列
	safelygo.GoSafelyByTraceID(func() {
		//delayCharge(sub, params)
	})
	chargeResult := &sensor.ChargeResultItem{
		IsSuccess:          false,
		UID:                sub.UID,
		ProductID:          int64(sub.ProductID),
		ContractID:         params.Contract.ContractCode,
		PayType:            sub.PayType,
		IsRenew:            params.IsRenew,
		FirstSubscribeTime: params.Contract.CreateTime,
		OrderID:            res.OrderID,
		Price:              res.Price,
		FailReason:         res.ErrSubMsg,
		RetryTimes:         len(params.ChargeList), // 重试次数从第二次开始算第一次重试，因此不用计算本次
		RetryDays:          int32(calcRetryDays(params.ChargeList)),
		SubscribeTimes:     GetSubscribeTimesByContract(params.Contract, ""),
		RetryTimesOfTheDay: todayCount,
		IsCoupon:           res.IsStepPrice,
		APPID:              params.Contract.AppID,
		CouponMoney:        res.StepPrice,
		IsHistoryDiscard:   params.Contract.IsHistoryDiscard == library.Yes,
	}
	sensor.ReportChargeSuccessFail(chargeResult)
}

// 延迟处理阶梯扣款任务
// nolint
func delayCharge(sub *dbu.WebProductSubscribeUser, params *ContractChargeParams) {
	if params.StepConfig == nil || !params.StepConfig.IsSwitch || params.StepConfig.ChargeDays == 0 {
		return
	}
	// 签约当天扣款
	var chargeTime int64
	loc, _ := time.LoadLocation(library.TimeZoneBeijing)
	ct := time.Now()
	step1 := time.Date(ct.Year(), ct.Month(), ct.Day(), 9, 0, 0, 0, loc)
	step2 := time.Date(ct.Year(), ct.Month(), ct.Day(), 13, 0, 0, 0, loc)
	step3 := time.Date(ct.Year(), ct.Month(), ct.Day(), 17, 0, 0, 0, loc)
	if ct.Before(step1) || ct.After(step3) {
		return
	}
	stepLimit := (step3.Unix() - step2.Unix()) - 7200
	if ct.Before(step2) {
		if time.Now().Unix()-step1.Unix() > stepLimit {
			chargeTime = step2.Unix() + stepLimit
		} else {
			chargeTime = step2.Unix() + (time.Now().Unix() - step1.Unix())
		}
		if chargeTime < step2.Unix() || chargeTime > step3.Unix() {
			chargeTime = step2.Unix()
		}
	} else if ct.Before(step3) {
		if time.Now().Unix()-step2.Unix() > stepLimit {
			chargeTime = step3.Unix() + stepLimit
		} else {
			chargeTime = step3.Unix() + (time.Now().Unix() - step2.Unix())
		}
		if chargeTime < step3.Unix() {
			chargeTime = step3.Unix()
		}
	} else {
		logger.Info("支付宝重试扣款:重试时间不在允许范围", sub.ID)
		return
	}
	if chargeTime == 0 {
		return
	}
	rdc := cache.GetCRedis().GetClient()
	member := &redis.Z{
		Score:  float64(chargeTime),
		Member: strconv.FormatInt(sub.ID, 10),
	}
	logger.Info("支付宝重试扣款:添加阶梯扣款任务", sub.ID, chargeTime)
	ret, err := rdc.ZAdd(context.Background(), libcache.StepDelayCharge, member).Result()
	if err != nil {
		logger.Error(err)
		return
	}
	if ret == 0 {
		logger.Error("支付宝重试扣款:阶梯扣款写入延迟队列失败", sub.ID, ret)
	}
}

func aliCyclePushback(contract *dbo.AlipayContract, sub *dbu.WebProductSubscribeUser) {
	execTime := contract.CreateTime
	durationType := 0
	durationValue := 0
	switch contract.PeriodType {
	case "MONTH":
		durationType = int(libproduct.DurationTypeEnum.Month)
		durationValue = contract.Period
	case "DAY":
		durationType = int(libproduct.DurationTypeEnum.Day)
		durationValue = contract.Period
	default:
	}
	if durationType == 0 || durationValue == 0 {
		return
	}
	for execTime < time.Now().Unix() {
		execTime = equity.CalcDurationTime(execTime, durationType, durationValue)
	}
	if sub.ExpiresTime == execTime {
		return
	}
	logger.Info("用户支付宝签约周期推后", sub.ID, sub.ExpiresTime, execTime)
	sub.ExpiresTime = execTime
	sub.BeginFailTime = 0
	err := sub.Update()
	if err != nil {
		logger.Info("用户支付宝签约周期推后失败", sub.ID, sub.ExpiresTime, execTime)
	}
}

func GetStepConfigByProduct(product *dbp.WebProduct) *ChargeStepSwitchConfig {
	var stepProductType libproduct.StepPTypeInt
	res := &ChargeStepSwitchConfig{}
	switch product.DurationType {
	case int(libproduct.DurationTypeEnum.Day):
		if product.DurationValue < libproduct.DurationTypeToDays[libproduct.DurationTypeEnum.Month] {
			return res
		}
		stepProductType = libproduct.StepPTypeEnum.Month
	case int(libproduct.DurationTypeEnum.Month):
		stepProductType = libproduct.StepPTypeEnum.Month
		if product.DurationValue == libproduct.MonthThree {
			stepProductType = libproduct.StepPTypeEnum.Quarter
		}
		if product.DurationValue == libproduct.MonthSix {
			stepProductType = libproduct.StepPTypeEnum.HalfYear
		}
		if product.DurationValue == libproduct.MonthTwelve {
			stepProductType = libproduct.StepPTypeEnum.Year
		}
	case int(libproduct.DurationTypeEnum.Year):
		stepProductType = libproduct.StepPTypeEnum.Year
	default:
		return res
	}
	if stepProductType == 0 {
		return res
	}
	configInfo := dbc.TbConfig.GetItemByKey(libc.ChargeStepSwitch)
	if configInfo == nil {
		return res
	}
	stepChargeConfig := make([]*ChargeStepSwitchConfig, 0)
	if err := json.Unmarshal([]byte(configInfo.Value), &stepChargeConfig); err != nil {
		return res
	}
	for _, v := range stepChargeConfig {
		if int32(v.ProductType) == int32(stepProductType) {
			res = v
		}
	}
	return res
}

func calcRetryDays(chargeList []*dbo.AlipayContractCharge) int {
	if len(chargeList) == 0 {
		return 0
	}
	dayMap := make(map[string]int)
	for _, v := range chargeList {
		if time.Now().Format("2006-01-02") == v.ChargeDate {
			continue
		}
		dayMap[v.ChargeDate] = 1
	}
	return len(dayMap)
}

func ChargePlanUpdate(subID int64, isSuccess, abnormalType int, isJob, isStep bool, errSubCode string) {
	if !isJob || isStep {
		return
	}
	loc, _ := time.LoadLocation(library.TimeZoneBeijing)
	ts := time.Now()
	start := time.Date(ts.Year(), ts.Month(), ts.Day(), libp.AlipayChargeControlBegin, 0, 0, 0, loc)
	end := time.Date(ts.Year(), ts.Month(), ts.Day(), libp.AlipayChargeControlEnd, 1, 0, 0, loc)
	if ts.After(end) || ts.Before(start) {
		return
	}
	todayStr := time.Now().Format("20060102")
	planItem := dbo.TbDailyChargePlan.GetPlanItem(todayStr, subID)
	if planItem == nil {
		logger.Info("支付宝每日扣款计划更新:无此计划", subID, isSuccess, abnormalType)
		return
	}
	if planItem.IsSuccess == isSuccess && planItem.AbnormalType == abnormalType {
		return
	}
	logger.Info("支付宝每日扣款计划更新:更新", subID, isSuccess, abnormalType)
	planItem.IsSuccess = isSuccess
	planItem.AbnormalType = abnormalType
	planItem.ErrSubCode = errSubCode
	if err := planItem.Update(); err != nil {
		logger.Error(err)
	}
}

// 扣款失败后修改下次扣款事件
func changeNextChargeDate(sub *dbu.WebProductSubscribeUser, contract *dbo.AlipayContract) error {
	// 第一次失败时，记录扣款开始失败时间
	if sub.BeginFailTime == 0 {
		sub.BeginFailTime = util.FormatStartTime(time.Now().Unix())
	}
	var intervalDay int64 = 1
	// 因为脚本是提前一天扣款，因此 此时的ExpiresTime是次日。因此intervalDay加一
	sub.ExpiresTime = util.FormatStartTime(time.Now().Unix()) + (intervalDay+1)*86400
	// 优惠产品要求必须是订阅时间之后去扣款
	if sub.OfferType > libproduct.ConstOfferTypeNo {
		sub.ExpiresTime = util.FormatStartTime(time.Now().Unix()) + (intervalDay)*86400
	}
	logger.Info("支付宝自动续订，扣款失败，修改计划", contract.ContractID, sub.ExpiresTime, intervalDay)
	if err := sub.Update(); err != nil {
		return err
	}
	return nil
}

// 统计订阅成功扣费的次数
func GetSubscribeTimesByContract(contract *dbo.AlipayContract, successOrderID string) int {
	if contract == nil {
		return 0
	}
	chargeList := dbo.TbAlipayCharge.GetValidChargeByCode(contract.ContractCode)
	var subscribeTimes int
	isExist := false
	if len(chargeList) > 0 {
		for _, v := range chargeList {
			if v.OrderID == successOrderID {
				isExist = true
			}
			if v.OrderID != contract.OrderID {
				subscribeTimes++
			}
		}
	}
	webOrder := dbo.TbWebOrder.GetItemByOrderID(contract.OrderID)
	if webOrder != nil && webOrder.OrderStatus == libp.OrderStatus.Paid {
		if successOrderID == webOrder.OrderID {
			isExist = true
		}
		subscribeTimes++
	}
	// 当次完成订单的订单号，如果没在上面记录里，订阅次数+1
	if successOrderID != "" && !isExist {
		subscribeTimes++
	}
	return subscribeTimes
}

type AlipayTradeChargeNotify struct {
	OutTradeNo string `json:"out_trade_no"`
	NotifyTime string `json:"notify_time"`
	AppID      string `json:"app_id"`
}

// nolint
func (*Complete) AlipayTradeCharge(notify gopay.BodyMap, cn *dbo.CompleteNotify) bool {
	notifyByte, err := json.Marshal(notify)
	if err != nil {
		logger.Error(err)
		return false
	}
	aliNotify := AlipayTradeChargeNotify{}
	err = json.Unmarshal(notifyByte, &aliNotify)
	if err != nil {
		logger.Error(err)
		return false
	}
	charge := dbo.TbAlipayCharge.GetItemByOrderID(aliNotify.OutTradeNo)
	// 没有扣款请求或已付款
	if charge == nil {
		logger.Error("支付宝续订扣款回调未查到有效请求", notify)
		return false
	}
	if charge.Status == library.Yes {
		logger.Error("支付宝续订扣款收到扣款状态已完成的回调,请立即排查", notify)
		return true
	}
	product := dbp.TbWebProduct.GetItemByID(charge.ProductID)
	if product == nil {
		logger.Error("支付宝续订扣款回调未查到有效产品", notify)
		return false
	}
	contract := dbo.TbAlipayContract.GetItemByContractCode(charge.ContractCode)
	// 处理优惠产品价格
	FormatOffer(contract, true, product)
	subscribeUser := dbu.TbWPSubU.GetItemByContractID(charge.ContractID)
	subscribeUser.OrderID = aliNotify.OutTradeNo
	notifyTime, err := time.ParseInLocation("2006-01-02 15:04:05", aliNotify.NotifyTime, time.Local)
	if err != nil {
		logger.Error(err)
		return false
	}
	expireTime := notifyTime.Unix()
	if subscribeUser.ExpiresTime > expireTime {
		expireTime = subscribeUser.ExpiresTime
	}
	subscribeUser.ExpiresTime = equity.CalcVipTime(expireTime, product.DurationType, product.DurationValue)
	if err := subscribeUser.Update(); err != nil {
		logger.Error(err)
		return false
	}
	charge.Status = library.Yes
	if err := charge.Update(); err != nil {
		logger.Error(err)
		return false
	}
	webOrder := dbo.TbWebOrder.GetItemByOrderID(charge.OrderID)
	if webOrder == nil {
		webOrder = &dbo.WebOrder{
			UID:         charge.UID,
			OrderID:     charge.OrderID,
			ProductID:   charge.ProductID,
			OrderAmount: product.Price,
			IsRenew:     library.Yes,
			PayType:     libp.PayTypeAlipay,
			MchID:       aliNotify.AppID,
		}
		paymentOrderType := 0
		webOrder.PaymentOrderType = paymentOrderType
		if charge.OrderAmount > 0 {
			webOrder.OrderAmount = charge.OrderAmount
		}
		// 阶梯价订单或者龙南铭改价订单 根据订阅次数判断是否续订
		subTimes := GetSubscribeTimesByContract(contract, charge.OrderID)
		if subTimes <= 1 {
			webOrder.IsRenew = library.No
			conrtactOrder := dbo.TbWebOrder.GetItemByOrderID(contract.OrderID)
			if conrtactOrder != nil {
				webOrder.ProductSubID = conrtactOrder.ProductSubID
				webOrder.PaymentOrderType = conrtactOrder.PaymentOrderType
				webOrder.Source = conrtactOrder.Source
				webOrder.SourceID = conrtactOrder.SourceID
				webOrder.SourceRefer = conrtactOrder.SourceRefer
				webOrder.SourceReferID = conrtactOrder.SourceReferID
				webOrder.Channel = conrtactOrder.Channel
				webOrder.Version = conrtactOrder.Version
			}
		}
		if err := webOrder.Save(); err != nil {
			logger.Error(err)
			return false
		}
	}
	if CompleteOrder(cn, true) {
		return true
	}
	return false
}

// CheckOrderSubscribeStatus 检查订单是否签约
func (c *Complete) CheckOrderSubscribeStatus(orderItem *dbo.WebOrder) libp.OrderSubscribeStatus {
	product := dbp.TbWebProduct.GetItemByID(orderItem.ProductID)
	if product == nil || product.IsSubscribe != library.Yes {
		return libp.OrderSubscribeStatusEnum.Not
	}
	contract := dbo.TbAlipayContract.GetItemByOrderID(orderItem.OrderID)
	if contract == nil {
		return libp.OrderSubscribeStatusEnum.Not
	}
	if contract.ChangeType != libp.ContractChangeTypeEnum.ContractChangeTypeWait {
		return libp.OrderSubscribeStatusEnum.Success
	}
	cKey := fmt.Sprintf("%s:%s", libcache.DataCheckAlipayContractStatus, contract.OrderID)
	cst, err := cache.GetCRedis().GetClient().Get(context.Background(), cKey).Result()
	if err != nil && err.Error() != "redis: nil" {
		return libp.OrderSubscribeStatusEnum.Wait
	}
	csti, _ := strconv.Atoi(cst)
	if csti > int(libp.OrderSubscribeStatusEnum.Wait) {
		return libp.OrderSubscribeStatus(csti)
	}
	lockKey := fmt.Sprintf("%s:%s", libcache.LockCheckAlipayContractStatus, orderItem.OrderID)
	if lock := cache.GetCRedis().Lock(lockKey, cache.LockExpiration(time.Second)); !lock {
		return libp.OrderSubscribeStatusEnum.Wait
	}
	defer func() {
		err := cache.GetCRedis().Unlock(lockKey)
		if err != nil {
			logger.Warn(err)
		}
	}()
	safelygo.GoSafelyByTraceID(func() {
		oss := libp.OrderSubscribeStatusEnum.Not
		if srvp.SrvAlipay.AgreementQueryExist(contract.ContractCode, contract.AppID) {
			oss = libp.OrderSubscribeStatusEnum.Success
		}
		_, err := cache.GetCRedis().GetClient().Set(context.Background(), cKey, int(oss), time.Hour).Result()
		if err != nil {
			logger.Error(err)
		}
	})
	return libp.OrderSubscribeStatusEnum.Wait
}

// TiktokChargeUserContract 用户订阅当前周期扣款
// nolint
func (c *Complete) TiktokChargeUserContract(sub *dbu.WebProductSubscribeUser, params *ContractChargeParams, domainURL string) {
	if sub == nil || params == nil {
		return
	}
	params.IsJob = params.IsRenew
	if params.IsJob {
		currentItem := dbu.TbWPSubU.GetItemByID(sub.ID)
		if currentItem == nil || currentItem.Status != library.Yes {
			logger.Info("tiktok自动续订找不到有效协议", sub.ID, sub.UID)
			return
		}
	}
	logger.Infof("tiktok TiktokChargeUserContract %+v", sub)
	params.TContract = dbo.TbTiktokContract.GetItemByContractCode(sub.ThirdPartTransactionID)
	// 用户在签约完后解约，如果获取到的是用户已解约的数据，这里会报错
	if params.TContract == nil || params.TContract.ChangeType != libp.ContractChangeTypeEnum.ContractChangeTypeAdd {
		logger.Warn("tiktok自动续订找不到有效协议", *sub)
		return
	}
	logger.Infof("tiktok TiktokChargeUserContract TContract %+v", params.TContract)
	now := time.Now()
	rd := cache.GetCRedis()
	lockKey := fmt.Sprintf("%s:%d", libcache.LockTiktokPaySubscribeCharge, sub.UID)
	if lock := rd.Lock(lockKey, cache.LockExpiration(time.Minute)); !lock {
		logger.Info("tiktok扣费锁失败", sub.UID)
		return
	}
	defer func() {
		if err := rd.Unlock(lockKey); err != nil {
			logger.Error(err)
		}
	}()
	// 已注销用户不再处理续订
	account := dbu.TbAccount.GetUserByID(sub.UID)
	logger.Infof("tiktok TiktokChargeUserContract account %+v", account)
	if account == nil {
		logger.Warn("tiktok扣费,用户信息为空", sub.UID)
		return
	}
	params.Product = dbp.TbWebProduct.GetItemByID(params.TContract.ProductID)
	if params.Product == nil {
		logger.Error("tiktok 自动续订扣款找不到商品")
		return
	}
	logger.Infof("tiktok TiktokChargeUserContract Product %+v", params.TContract)
	if sub.BeginFailTime > 0 {
		// 获取从失败开始本期扣款次数
		params.TChargeList = dbo.TbTiktokCharge.GetChargeListByRange(util.FormatStartTime(sub.BeginFailTime),
			now.Unix(), sub.ThirdPartTransactionID)
	}
	params.StepConfig = GetStepConfigByProduct(params.Product)
	todayCount := 0
	if len(params.TChargeList) > 0 {
		dateMap := make(map[string]int)
		dateMap[time.Now().Format("2006-01-02")] = 0
		for _, v := range params.TChargeList {
			// 已经有请求成功的，不再处理
			if v.Status == library.Yes {
				logger.Info("tiktok 重试扣款：已有扣款记录:", sub.ID)
				return
			}
			dateMap[v.ChargeDate]++
			if v.ChargeDate == time.Now().Format("2006-01-02") {
				todayCount++
			}
		}
		if todayCount > 0 {
			logger.Info("tiktok 重试扣款：当天已有扣款记录", sub.ID)
			return
		}
	}

	originOrder := dbo.TbWebOrder.GetItemByOrderID(params.TContract.OrderID)
	chargeSuccess := dbo.TbTiktokCharge.GetValidChargeByCode(params.TContract.ContractCode)
	// 续订重新生成订单ID
	if params.IsRenew && (originOrder == nil || originOrder.OrderStatus == libp.OrderStatus.Paid || len(chargeSuccess) > 0) {
		params.IsRenew = true
	} else {
		params.IsRenew = false
	}
	logger.Info("TikTok自动续订，发起扣款", sub.ID, sub.UID)
	// 发起扣款
	res := c.TiktokContractTradePay(params, sub, domainURL)
	logger.Infof("tiktok TiktokChargeUserContract TiktokContractTradePay %+v", res)
	if res == nil {
		logger.Warn("TikTok续订扣款异常，直接返回", sub.ID, sub.UID)
		return
	}
	chargeResult := &sensor.ChargeResultItem{
		IsSuccess:          res.Success,
		UID:                sub.UID,
		ProductID:          int64(sub.ProductID),
		ContractID:         params.TContract.ContractCode,
		PayType:            sub.PayType,
		IsRenew:            params.IsRenew,
		FirstSubscribeTime: params.TContract.CreateTime,
		OrderID:            res.OrderID,
		Price:              res.Price,
		FailReason:         res.ErrSubMsg,
		RetryTimes:         len(params.ChargeList), // 重试次数从第二次开始算第一次重试，因此不用计算本次
		RetryDays:          int32(calcRetryDays(params.ChargeList)),
		RetryTimesOfTheDay: todayCount,
		OrderMoney:         params.Product.Price,
		APPID:              libp.TikTokMchIDToAppID[params.TContract.GetMerchantID()],
	}
	sensor.ReportChargeSuccessFail(chargeResult)
}

// tiktokChangeNextChargeDate tiktok自动续订 抖音机制的问题 请求成功不代表 扣款成功
func tiktokChangeNextChargeDate(sub *dbu.WebProductSubscribeUser) {
	if sub.BeginFailTime == 0 {
		sub.BeginFailTime = util.FormatStartTime(time.Now().Unix())
	}
	var intervalDay int64 = 1
	// 因为脚本是提前一天扣款，因此 此时的ExpiresTime是次日。因此intervalDay加一
	sub.ExpiresTime = util.FormatStartTime(time.Now().Unix()) + (intervalDay+1)*86400
	// 优惠产品要求必须是订阅时间之后去扣款
	if sub.OfferType > libproduct.ConstOfferTypeNo {
		sub.ExpiresTime = util.FormatStartTime(time.Now().Unix()) + (intervalDay)*86400
	}
	logger.Info("Tiktok自动续订，扣款失败，修改计划", sub.ThirdPartTransactionID, sub.ExpiresTime, intervalDay)
	if err := sub.Update(); err != nil {
		logger.Warn("Tiktok 扣款失败后修改下次扣款时间错误", err)
	}
}

// TiktokContractTradePay tiktok签约后扣款或续订扣款
func (*Complete) TiktokContractTradePay(params *ContractChargeParams,
	sub *dbu.WebProductSubscribeUser, domainURL string) *srvp.TradePayRes {
	product := dbp.TbWebProduct.GetItemByID(params.TContract.ProductID)
	if product == nil {
		logger.Error("自动续订扣款找不到商品")
		return nil
	}
	logger.Infof("tiktok TiktokContractTradePay %+v", product)
	params.Product = product
	// 处理优惠扣款
	needDeduction := FormatTiktokOffer(params.TContract, product, params.IsRenew)
	if !needDeduction {
		return &srvp.TradePayRes{
			Success: true,
		}
	}
	charge := &dbo.TiktokContractCharge{
		UID:          params.TContract.UID,
		ProductID:    params.TContract.ProductID,
		ContractCode: params.TContract.ContractCode,
		ContractID:   params.TContract.ContractID,
		OrderAmount:  product.Price,
		ChargeDate:   time.Now().Format("2006-01-02"),
		OrderID:      params.TContract.OrderID,
	}
	if params.IsJob {
		// 获取订单号，保持只有两个订单号 一个原价订单号，一个阶梯价订单号
		charge.OrderID = GetOrderIDByCharge(params.ChargeList, charge.OrderAmount, params.Product)
	}
	logger.Infof("Tiktok扣款 TiktokContractTradePay  %+v", charge)
	if err := charge.Save(); err != nil {
		logger.Error(err)
		return &srvp.TradePayRes{
			Success: false,
			OrderID: charge.OrderID,
		}
	}
	resp := srvp.SrvDouYin.TiktokCycleCreate(charge.OrderID, params.TContract, charge.OrderAmount, 0, domainURL)
	if resp == nil || resp.ErrNo != 0 {
		return &srvp.TradePayRes{
			Success: false,
			OrderID: charge.OrderID,
		}
	}
	// 请求成功不代表扣款成功
	tiktokChangeNextChargeDate(sub)
	return &srvp.TradePayRes{
		Success: true,
		OrderID: charge.OrderID,
		Price:   product.Price,
	}
}

func FormatTiktokOffer(contract *dbo.TiktokContract, product *dbp.WebProduct, isRenew bool) bool {
	if contract.OfferType > libproduct.ConstOfferTypeNo {
		switch contract.OfferType {
		case libproduct.ConstOfferTypeFirstBuy:
			list := dbo.TbTiktokCharge.GetValidChargeByCode(contract.ContractCode)
			if len(list) < int(contract.OfferFirstBuyCycle) {
				product.Price = contract.OfferFirstBuyPrice
			}
		case libproduct.ConstOfferTypeTrial:
			if !isRenew {
				if contract.OfferTrialPrice == 0 {
					if !CompleteOrder(&dbo.CompleteNotify{OrderID: contract.OrderID}, false) {
						logger.Error("0元试用未成功 ConstOfferTypeTrial", contract.OrderID)
					}
					return false
				}
				product.Price = contract.OfferTrialPrice
			}
		default:
			logger.Warn("抖音小程序不支持的类型", contract, product)
		}
	}
	return true
}

type SignPayCallback struct {
	AppID         string `json:"app_id"`
	Status        string `json:"status"`
	AuthOrderID   string `json:"auth_order_id"`
	PayOrderID    string `json:"pay_order_id"`
	OutPayOrderNo string `json:"out_pay_order_no"`
	TotalAmount   int    `json:"total_amount"`
	PayChannel    int    `json:"pay_channel"`
	ChannelPayID  string `json:"channel_pay_id"`
	MerchantUID   string `json:"merchant_uid"`
	EventTime     int64  `json:"event_time"`
}

// nolint
func (*Complete) TiktokTradeCharge(notify SignPayCallback, cn *dbo.CompleteNotify) bool {
	charge := dbo.TbTiktokCharge.GetItemByOrderID(notify.OutPayOrderNo)
	// 没有扣款请求或已付款
	if charge == nil {
		logger.Error("Tiktok续订扣款回调未查到有效请求", notify)
		return false
	}
	if charge.Status == library.Yes {
		logger.Error("Tiktok续订扣款收到扣款状态已完成的回调,请立即排查", notify)
		return true
	}
	product := dbp.TbWebProduct.GetItemByID(charge.ProductID)
	if product == nil {
		logger.Error("Tiktok续订扣款回调未查到有效产品", notify)
		return false
	}
	contract := dbo.TbTiktokContract.GetItemByContractCode(charge.ContractCode)
	// 处理优惠产品价格
	FormatTiktokOffer(contract, product, true)
	subscribeUser := dbu.TbWPSubU.GetItemByContractID(charge.ContractID)
	subscribeUser.OrderID = notify.OutPayOrderNo
	millisTimestamp := notify.EventTime
	// 转换为秒
	secondsTimestamp := millisTimestamp / 1000
	// 使用time.Unix转换为time.Time
	notifyTime := time.Unix(secondsTimestamp, 0)
	expireTime := notifyTime.Unix()
	if subscribeUser.ExpiresTime > expireTime && subscribeUser.BeginFailTime == 0 {
		expireTime = subscribeUser.ExpiresTime
	}
	subscribeUser.ExpiresTime = equity.CalcVipTime(expireTime, product.DurationType, product.DurationValue)
	subscribeUser.BeginFailTime = 0
	if err := subscribeUser.Update(); err != nil {
		logger.Error(err)
		return false
	}
	charge.OrderAmount = float64(notify.TotalAmount / fenPerYuan)
	charge.Status = library.Yes
	// 退款的时候需要的是这个订单号
	charge.PayOrderID = notify.PayOrderID
	if err := charge.Update(); err != nil {
		logger.Error(err)
		return false
	}
	webOrder := dbo.TbWebOrder.GetItemByOrderID(charge.OrderID)
	if webOrder == nil {
		webOrder = &dbo.WebOrder{
			UID:         charge.UID,
			OrderID:     charge.OrderID,
			ProductID:   charge.ProductID,
			OrderAmount: product.Price,
			IsRenew:     library.Yes,
			PayType:     libp.PayTypeTikTok,
			MchID:       notify.MerchantUID,
		}
		if charge.OrderAmount > 0 {
			webOrder.OrderAmount = charge.OrderAmount
		}
		subTimes := GetSubscribeTimesByTkContract(contract, charge.OrderID)
		if subTimes <= 1 {
			webOrder.IsRenew = library.No
			tOrder := dbo.TbWebOrder.GetItemByOrderID(contract.OrderID)
			if tOrder != nil {
				webOrder.ProductSubID = tOrder.ProductSubID
				webOrder.PaymentOrderType = tOrder.PaymentOrderType
				webOrder.Source = tOrder.Source
				webOrder.SourceID = tOrder.SourceID
				webOrder.SourceRefer = tOrder.SourceRefer
				webOrder.SourceReferID = tOrder.SourceReferID
				webOrder.Channel = tOrder.Channel
				webOrder.Version = tOrder.Version
			}
		}
		if err := webOrder.Save(); err != nil {
			logger.Error(err)
			return false
		}
	} else {
		if charge.OrderAmount > 0 && webOrder.OrderAmount != charge.OrderAmount {
			webOrder.OrderAmount = charge.OrderAmount
			webOrder.MchID = notify.MerchantUID
			if err := webOrder.Update(); err != nil {
				logger.Error(err)
				return false
			}
		}
	}
	if CompleteOrder(cn, true) {
		return true
	}
	return false
}

func AgreementPaymentCharge(notify *dbo.CompleteNotify, tiktok *SignPayCallback) bool {
	charge := dbo.TbTiktokCharge.GetItemByOrderID(tiktok.OutPayOrderNo)
	if charge != nil && charge.Status == library.Yes {
		logger.Error("Tiktok续订扣款收到扣款状态已完成的回调,请立即排查", notify, charge)
		return true
	}
	order := dbo.TbWebOrder.GetItemByOrderID(notify.OrderID)
	if order == nil {
		logger.Error("完成订单,订单记录为空", notify.OrderID)
		return false
	}
	charge = &dbo.TiktokContractCharge{
		UID:          order.UID,
		ProductID:    order.ProductID,
		ContractCode: tiktok.OutPayOrderNo + string(config.Get().Service.Env),
		ContractID:   tiktok.AuthOrderID,
		OrderAmount:  float64(tiktok.TotalAmount / fenPerYuan),
		ChargeDate:   time.Now().Format("2006-01-02"),
		OrderID:      tiktok.OutPayOrderNo,
		PayOrderID:   tiktok.PayOrderID,
		Status:       library.Yes,
	}

	if charge.OrderAmount > 0 && order.OrderAmount != charge.OrderAmount {
		order.OrderAmount = charge.OrderAmount
		if err := order.Update(); err != nil {
			logger.Error(err)
			return false
		}
	}
	logger.Infof("Tiktok扣款 AgreementPaymentCharge  %+v", charge)
	if err := charge.Save(); err != nil {
		logger.Error(err)
	}
	return CompleteOrder(notify, true)
}

func GetSubscribeTimesByTkContract(contract *dbo.TiktokContract, successOrderID string) int {
	if contract == nil {
		return 0
	}
	chargeList := dbo.TbTiktokCharge.GetValidChargeByCode(contract.ContractCode)
	var subscribeTimes int
	isExist := false
	if len(chargeList) > 0 {
		for _, v := range chargeList {
			if v.OrderID == successOrderID {
				isExist = true
			}
			if v.OrderID != contract.OrderID {
				subscribeTimes++
			}
		}
	}
	webOrder := dbo.TbWebOrder.GetItemByOrderID(contract.OrderID)
	if webOrder != nil && webOrder.OrderStatus == libp.OrderStatus.Paid {
		if successOrderID == webOrder.OrderID {
			isExist = true
		}
		subscribeTimes++
	}
	// 当次完成订单的订单号，如果没在上面记录里，订阅次数+1
	if successOrderID != "" && !isExist {
		subscribeTimes++
	}
	return subscribeTimes
}

type CallBackAgreement struct {
	AppID          string `json:"app_id"`
	Status         string `json:"status"`
	AuthOrderID    string `json:"auth_order_id"`
	OutAuthOrderNo string `json:"out_auth_order_no"`
	CancelSource   int    `json:"cancel_source"` // 解约来源，仅解约结果通知有该字段 1-用户解约
	EventTime      int64  `json:"event_time"`
}

// TikTokContract 抖音签约回调
func (c *Complete) TikTokContract(notify *CallBackAgreement, subMode libp.SubscribeModeInt, domainURL string) bool {
	if notify == nil {
		return false
	}
	millisTimestamp := notify.EventTime
	// 转换为秒
	secondsTimestamp := millisTimestamp / 1000
	// 使用time.Unix转换为time.Time
	notifyTime := time.Unix(secondsTimestamp, 0)
	contractNotify := dbo.TiktokContractNotify{
		AppID:        notify.AppID,
		ContractCode: notify.OutAuthOrderNo,
		ContractID:   notify.AuthOrderID,
		ChangeType:   notify.Status,
		SignTime:     notifyTime.Format("2006-01-02 15:04:05"),
		NotifyTime:   time.Now().Format("2006-01-02 15:04:05"),
	}
	if err := contractNotify.Save(); err != nil {
		logger.Error("TikTok签约保存数据", err)
		return false
	}

	contractInfo := dbo.TbTiktokContract.GetItemByContractCode(notify.OutAuthOrderNo)
	logger.Infof("TikTok contractInfo %+v", contractInfo)
	if contractInfo == nil {
		logger.Error("TikTok签约信息为空", notify)
		return false
	}
	changeType := libp.TiktokContractToChangeType[notify.Status]
	// 多次回调或者解约比签约过来的晚的情况
	if contractInfo.ChangeType == libp.ContractChangeTypeEnum.ContractChangeTypeDelete ||
		contractInfo.ChangeType == changeType {
		return true
	}
	contractInfo.ContractID = notify.AuthOrderID
	if changeType == 0 {
		logger.Error("未知的签约类型", notify)
		return false
	}
	contractInfo.ChangeType = changeType
	if err := contractInfo.Update(); err != nil {
		logger.Error("签约信息保存失败", notify)
		return false
	}
	order := dbo.TbWebOrder.GetItemByOrderID(contractInfo.OrderID)
	if order == nil {
		logger.Error("不存在的订单", notify)
		return false
	}
	if subMode == libp.SubscribeModeEnum.PayAndSubscribe {
		product := dbp.TbWebProduct.GetItemByID(order.ProductID)
		if product == nil {
			logger.Error("支付宝订阅回调未查到有效产品", notify)
			return false
		}
		execTimeInt := equity.CalcVipTime(notifyTime.Unix(), product.DurationType, product.DurationValue)
		notifyTime = time.Unix(execTimeInt, 0)
	} else if contractInfo.OfferType == libproduct.ConstOfferTypeTrial {
		execTimeInt := equity.CalcVipTime(notifyTime.Unix(), int(libproduct.DurationTypeEnum.Day),
			int(contractInfo.OfferTrialDay))
		notifyTime = time.Unix(execTimeInt, 0)
	}
	sub := &dbu.WebProductSubscribeUser{
		UID:                    order.UID,
		ProductID:              int(order.ProductID),
		OrderID:                order.OrderID,
		OriginalOrderID:        order.OrderID,
		ThirdPartTransactionID: notify.OutAuthOrderNo,
		OriginalTransactionID:  notify.AuthOrderID,
		Status:                 changeType,
		PayType:                libp.PayTypeTikTok,
		ExpiresTime:            notifyTime.Unix(),
		OfferType:              int(contractInfo.OfferType),
	}
	if err := sub.FindOrCreate(); err != nil {
		logger.Error(err)
		return false
	}
	logger.Infof("tiktok WebProductSubscribeUser changeType %+v", changeType)
	if changeType == libp.ContractChangeTypeEnum.ContractChangeTypeAdd {
		// 订阅上报
		sensor.ReportSubscribe(order, subMode, notify.OutAuthOrderNo)
		// 支付中签约不需要再扣款，先签约后扣款需要扣第一次款
		logger.Infof("tiktok WebProductSubscribeUser subMode %+v", subMode)
		if subMode == libp.SubscribeModeEnum.PayAndSubscribe {
			return true
		}
		safelygo.GoSafelyByTraceID(func() {
			c.TiktokChargeUserContract(sub, &ContractChargeParams{
				IsRenew: false,
				IsStep:  false,
			}, domainURL)
		})
		return true
	}
	// 取消订阅上报
	sensor.ReportUnSubscribe(order, contractInfo.ContractCode,
		libp.UnsubscribeTypeEnum.User)
	return true
}
