package order

import (
	"context"
	"errors"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases/order"
	dbuser "gitlab.dailyyoga.com.cn/server/children/databases/user"
	libc "gitlab.dailyyoga.com.cn/server/children/library/cache"
	"gitlab.dailyyoga.com.cn/server/children/library/pay"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	srvsensor "gitlab.dailyyoga.com.cn/server/children/service/sensor"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

func UnsubscribeByOrderID(orderID string, unsubscribeType pay.UnsubscribeTypeInt) bool {
	orderItem := order.TbWebOrder.GetItemByOrderID(orderID)
	if orderItem == nil {
		return false
	}
	contract := order.TbAlipayContract.GetItemByOrderID(orderID)
	charge := order.TbAlipayCharge.GetItemByOrderID(orderID)
	if charge == nil && contract == nil {
		return false
	}
	if contract == nil {
		contract = order.TbAlipayContract.GetItemByContractCode(charge.ContractCode)
	}
	if contract == nil {
		return false
	}
	subItem := dbuser.TbWPSubU.GetItemByContractID(contract.ContractID)
	if subItem == nil {
		return false
	}
	if subItem.Status != pay.ContractChangeTypeEnum.ContractChangeTypeAdd {
		return true
	}
	subItem.Status = pay.ContractChangeTypeEnum.ContractChangeTypeDelete
	subItem.ExpiresTime = time.Now().Unix()
	err := subItem.Update()
	if err != nil {
		return false
	}
	srvsensor.ReportUnSubscribe(orderItem, contract.ContractCode, unsubscribeType)
	// 限额自增
	AddUnsubscribeQuota()
	return true
}

func UnsubscribeByID(uid, id int64) (bool, error) {
	subItem := dbuser.TbWPSubU.GetItem(id)
	if subItem == nil {
		return false, errors.New("数据错误，订阅不存在")
	}
	if subItem.UID != uid {
		return false, errors.New("数据错误，用户异常")
	}
	if subItem.PayType == pay.PayTypeApple {
		return false, errors.New("iOS订阅用户请使用苹果设备在【设置】中进行续订管理")
	}
	if subItem.PayType != pay.PayTypeAlipay {
		return false, errors.New("暂不支持该类型的订阅取消")
	}
	if subItem.Status != pay.ContractChangeTypeEnum.ContractChangeTypeAdd {
		return true, errors.New("订阅已解约或者等待签约")
	}
	orderItem := order.TbWebOrder.GetItemByOrderID(subItem.OrderID)
	if orderItem == nil {
		return false, errors.New("数据错误，订单不存在")
	}
	contract := order.TbAlipayContract.GetItemByOrderID(subItem.OrderID)
	charge := order.TbAlipayCharge.GetItemByOrderID(subItem.OrderID)
	if charge == nil && contract == nil {
		return false, errors.New("数据错误")
	}
	if contract == nil {
		contract = order.TbAlipayContract.GetItemByContractCode(charge.ContractCode)
	}
	if contract == nil {
		return false, errors.New("数据错误")
	}
	subItem.Status = pay.ContractChangeTypeEnum.ContractChangeTypeDelete
	subItem.ExpiresTime = time.Now().Unix()
	err := subItem.Update()
	if err != nil {
		return false, errors.New("数据更新错误")
	}
	srvsensor.ReportUnSubscribe(orderItem, contract.ContractCode, pay.UnsubscribeTypeEnum.Admin)
	// 限额自增
	AddUnsubscribeQuota()
	return true, nil
}

func AddUnsubscribeQuota() int {
	quota, err := cache.GetYoga01Redis().GetClient().Incr(context.Background(),
		libc.DataRefundUnsubscribeQuota+time.Now().Format("20060102")).Result()
	if err != nil {
		return 0
	}
	ok, err := cache.GetYoga01Redis().GetClient().ExpireAt(context.Background(),
		libc.DataRefundUnsubscribeQuota+time.Now().Format("20060102"),
		time.Unix(util.FormatEndTime(time.Now().Unix()), 0)).Result()
	if err != nil || !ok {
		logger.Warn("设置订阅限额过期时间错误")
	}
	return int(quota)
}
