package order

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/awa/go-iap/appstore"
	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	dbclient "gitlab.dailyyoga.com.cn/server/children/databases/client"
	"gitlab.dailyyoga.com.cn/server/children/databases/iap"
	"gitlab.dailyyoga.com.cn/server/children/databases/order"
	"gitlab.dailyyoga.com.cn/server/children/databases/product"
	"gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libc "gitlab.dailyyoga.com.cn/server/children/library/cache"
	"gitlab.dailyyoga.com.cn/server/children/library/pay"
	libp "gitlab.dailyyoga.com.cn/server/children/library/product"
	"gitlab.dailyyoga.com.cn/server/children/service/audit"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/equity"
	"gitlab.dailyyoga.com.cn/server/children/service/sensor"
)

// IAPClientPayReceipt 客户端下单交易凭证
type IAPClientPayReceipt struct {
	SID            string `json:"sid"`
	OrderID        string `json:"order_id"`
	OriginalTranID string `json:"original_tran_id"`
	TranID         string `json:"tran_id"`
	UID            int64  `json:"uid"`
	IOSProductID   string `json:"ios_product_id"`
	Receipt        string `json:"receipt"`
	Source         string `json:"source"`
	SourceID       string `json:"source_id"`
	SourceRefer    string `json:"source_refer"`
	SourceReferID  string `json:"source_refer_id"`
	Version        string `json:"df_version"`
	Channel        int    `json:"df_channel"`
}

// DealIAPReceiptLog 处理客户端上报receipt
// nolint
func DealIAPReceiptLog(iosReceipt *iap.IOSReceiptLog, appClient *library.AppClient) {
	logger.Info("IOS客户端订单完成处理", iosReceipt.ID)
	lockKey := fmt.Sprintf("%s:%d", libc.LockDealIOSReceiptLog, iosReceipt.ID)
	expireTime := 5
	lock := cache.GetCRedis().Lock(lockKey, cache.LockExpiration(time.Duration(expireTime)*time.Minute))
	if !lock {
		logger.Warn("IOS客户端订单完成处理加锁失败", iosReceipt.ID)
		return
	}
	defer func() {
		err := cache.GetCRedis().Unlock(lockKey)
		if err != nil {
			logger.Warn(err)
		}
	}()
	receiptInfo := &IAPClientPayReceipt{}
	err := json.Unmarshal([]byte(iosReceipt.Request), receiptInfo)
	if err != nil {
		logger.Error(err, iosReceipt.ID)
		return
	}
	var isSandbox bool
	if config.Get().Service.Env == microservice.Dev || config.Get().Service.Env == microservice.Mirror {
		isSandbox = true
	}
	if receiptInfo.OriginalTranID == "" && receiptInfo.TranID != "" {
		transactionInfo := GetTransactionInfo(receiptInfo.TranID, isSandbox)
		if transactionInfo == nil {
			isSandbox = true
			transactionInfo = GetTransactionInfo(receiptInfo.TranID, isSandbox)
			if transactionInfo == nil {
				logger.Warn("AppStore 无法获取到 TranID AND OriginalTranID", iosReceipt.ID)
				return
			}
		}
		receiptInfo.OriginalTranID = transactionInfo.OriginalTransactionId
	}
	if receiptInfo.OriginalTranID == "" {
		logger.Error("AppStore 无法获取到 OriginalTranID", iosReceipt.ID)
		return
	}
	transactionHistoryList := GetTransactionHistory(receiptInfo.OriginalTranID,
		isSandbox)
	if len(transactionHistoryList) < 1 {
		isSandbox = true
		transactionHistoryList = GetTransactionHistory(receiptInfo.OriginalTranID, isSandbox)
		if len(transactionHistoryList) < 1 {
			logger.Warn("AppStore IOS客户端订单完成处理,请求苹果账单失败", err, iosReceipt.ID)
			return
		}
	}
	fmtIap := &formatPayload{}
	if appClient != nil {
		fmtIap.IosAuditSwitchInfo = audit.GetAuditInfo(appClient).IsInAudit
	}
	inAppOrderList := make([]*IAPTransactionItem, 0)
	for _, v := range transactionHistoryList {
		transactionV2 := fmtIap.formatIAPTransactionV2(v)
		if transactionV2 == nil {
			logger.Infof("AppStore formatIAPTransactionV2 nil data:%+v", v)
			continue
		}
		inAppOrderList = append(inAppOrderList, transactionV2)
	}

	presult := analysisIAPOrderList(inAppOrderList, receiptInfo)
	iapResByte, err := json.Marshal(transactionHistoryList)
	if err != nil {
		logger.Error(err, transactionHistoryList)
	}
	pResultByte, err := json.Marshal(presult)
	if err != nil {
		logger.Error(err, presult)
	}
	if !library.InArray(presult.CompleteOrderList, receiptInfo.OrderID) {
		//RenewStatusCheckByReceipt(iapRes)
		RenewStatusCheckBySubscriptionStatuses(receiptInfo.OriginalTranID, isSandbox)
	}
	logger.Info("IOS客户端订单完成处理", iosReceipt.ID, string(iapResByte), string(pResultByte))
	iosReceipt.ReceiptDetail = string(iapResByte)
	iosReceipt.IsDeal = library.Yes
	iosReceipt.ProcessResult = string(pResultByte)
	iosReceipt.OriginalTransactionID = receiptInfo.OriginalTranID
	if err := iosReceipt.Update(); err != nil {
		logger.Error(err, iosReceipt.ID)
		return
	}
}

func RenewStatusCheckByReceipt(iapRes *appstore.IAPResponse) {
	if len(iapRes.PendingRenewalInfo) == 0 {
		return
	}
	for k := range iapRes.PendingRenewalInfo {
		if iapRes.PendingRenewalInfo[k].OriginalTransactionID != "" &&
			iapRes.PendingRenewalInfo[k].SubscriptionAutoRenewStatus != "" {
			logger.Info("IOS自动订阅状态检查", iapRes.PendingRenewalInfo[k].OriginalTransactionID,
				iapRes.PendingRenewalInfo[k].SubscriptionAutoRenewStatus)
			autoRenewStatus := pay.ContractChangeTypeEnum.ContractChangeTypeAdd
			if iapRes.PendingRenewalInfo[k].SubscriptionAutoRenewStatus != "1" {
				autoRenewStatus = pay.ContractChangeTypeEnum.ContractChangeTypeDelete
			}
			changeTransactionRenewStatus(iapRes.PendingRenewalInfo[k].OriginalTransactionID, autoRenewStatus)
		}
	}
}

// RenewByLastTransaction 续订
func RenewByLastTransaction(iapOrder *iap.IOSOrder) {
	lockKey := fmt.Sprintf("%s:%d", libc.LockRenewIOSTransaction, iapOrder.ID)
	expireTime := 5
	lock := cache.GetCRedis().Lock(lockKey, cache.LockExpiration(time.Duration(expireTime)*time.Minute))
	if !lock {
		return
	}
	defer func() {
		err := cache.GetCRedis().Unlock(lockKey)
		if err != nil {
			logger.Warn(err)
		}
	}()
	logger.Info("IOS检查续订", iapOrder.OriginalTranID, iapOrder.UID)
	receiptInfo := &IAPClientPayReceipt{
		UID:            iapOrder.UID,
		OriginalTranID: iapOrder.OriginalTranID,
		Receipt:        iapOrder.Receipt,
	}
	var isSandbox bool
	if config.Get().Service.Env == microservice.Dev || config.Get().Service.Env == microservice.Mirror {
		isSandbox = true
	}
	transactionHistoryList := GetTransactionHistory(receiptInfo.OriginalTranID, isSandbox)
	if len(transactionHistoryList) < 1 {
		isSandbox = true
		transactionHistoryList = GetTransactionHistory(receiptInfo.OriginalTranID, isSandbox)
		if len(transactionHistoryList) < 1 {
			logger.Warn("AppStore RenewByLastTransaction,请求苹果账单失败", receiptInfo.OriginalTranID)
			return
		}
	}
	if isSandbox && config.Get().Service.Env == microservice.Product {
		return
	}
	fmtIap := &formatPayload{}
	inAppOrderList := make([]*IAPTransactionItem, 0)
	for _, v := range transactionHistoryList {
		transactionV2 := fmtIap.formatIAPTransactionV2(v)
		if transactionV2 == nil {
			logger.Infof("AppStore formatIAPTransactionV2 nil data:%+v", v)
			continue
		}
		inAppOrderList = append(inAppOrderList, transactionV2)
	}

	rsp := analysisIAPOrderList(inAppOrderList, receiptInfo)
	rspByte, _ := json.Marshal(rsp)
	logger.Info("IOS检查续订结果", iapOrder.OriginalTranID, iapOrder.UID, string(rspByte))
}

type IAPOrderAnalysisRsp struct {
	CompleteOrderList []string            `json:"complete_order_list"`
	CurrentOrder      *IAPTransactionItem `json:"current_order"`
	ErrMsg            []string            `json:"err_msg"`
}

// analysisIAPOrderList 分析receipt的订单列表
func analysisIAPOrderList(inAppOrderList []*IAPTransactionItem, recepitInfo *IAPClientPayReceipt) *IAPOrderAnalysisRsp {
	res := &IAPOrderAnalysisRsp{}
	if len(inAppOrderList) == 0 {
		res.ErrMsg = append(res.ErrMsg, "苹果交易列表为空")
		return res
	}
	for _, v := range inAppOrderList {
		re := iap.TbIOSOrder.GetItemByTran(v.OriginalTransactionID, v.TransactionID)
		if re == nil {
			re = iap.TbIOSOrder.GetItemByWebItemID(v.OriginalTransactionID, v.WebItemID)
		}
		if re != nil {
			if re.TranID == recepitInfo.TranID {
				res.CompleteOrderList = append(res.CompleteOrderList, re.OrderID)
			}
			continue
		}
		logger.Info("IOS生成订单", v.OriginalTransactionID, v.TransactionID, recepitInfo.UID)
		orderItem, err := generateAppleOrder(v, recepitInfo)
		if err != nil {
			res.ErrMsg = append(res.ErrMsg, err.Error())
		}
		if orderItem != nil {
			res.CompleteOrderList = append(res.CompleteOrderList, orderItem.OrderID)
		}
	}
	// 获取苹果交易列表中是否存在当前请求的交易
	if recepitInfo.TranID != "" {
		for _, v := range inAppOrderList {
			if v.TransactionID == recepitInfo.TranID {
				res.CurrentOrder = v
				break
			}
			if v.OriginalTransactionID == recepitInfo.OriginalTranID && v.ExpireTime > 0 {
				res.CurrentOrder = v
			}
		}
	}
	return res
}

// nolint
// generateRenewOrder 生成苹果订单
func generateAppleOrder(receiptOrder *IAPTransactionItem, receiptInfo *IAPClientPayReceipt) (*order.WebOrder, error) {
	lockKey := fmt.Sprintf("%s:%s:%s", libc.LockGenerateIOSTransaction, receiptOrder.OriginalTransactionID, receiptOrder.TransactionID)
	lock := cache.GetCRedis().Lock(lockKey, cache.LockExpiration(time.Minute))
	if !lock {
		return nil, errors.New("锁失败" + lockKey)
	}
	defer func() {
		err := cache.GetCRedis().Unlock(lockKey)
		if err != nil {
			logger.Warn(err)
		}
	}()
	session := databases.GetEngineMaster().NewSession()
	defer session.Close()
	if err := session.Begin(); err != nil {
		logger.Error(err)
		return nil, err
	}
	var err error
	defer func() {
		if err != nil {
			logger.Error(err)
			if err = session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()
	productItem := product.TbWebProduct.GetItemByIOSID(receiptOrder.ProductID)
	if productItem == nil {
		err = errors.New("产品ID为空" + receiptOrder.ProductID)
		return nil, err
	}
	if receiptInfo == nil {
		err = errors.New(fmt.Sprintf("receiptInfo is nil OriginalTransactionID %+v", receiptOrder))
		return nil, err
	}
	var webOrder *order.WebOrder
	if receiptInfo.OrderID != "" && (receiptInfo.TranID == receiptOrder.TransactionID ||
		(receiptOrder.IsSandBox == library.Yes && receiptInfo.TranID[:len(receiptInfo.TranID)-2] ==
			receiptOrder.TransactionID[:len(receiptOrder.TransactionID)-2])) {
		webOrder = order.TbWebOrder.GetItemByOrderID(receiptInfo.OrderID)
		if webOrder == nil || webOrder.OrderStatus == pay.OrderStatus.Paid {
			err = errors.New("订单无效" + receiptInfo.OrderID)
			return nil, err
		}
		if receiptOrder.OriginalTransactionID == receiptOrder.TransactionID {
			webOrder.IsRenew = library.No
		}
		if productItem.ID != webOrder.ProductID {
			err = errors.New("webOrder 订单无效" + receiptInfo.OrderID)
			logger.Warnf("ios 疑似黑产订单 uid:%d orderID:%s", receiptInfo.UID, receiptInfo.OrderID)
			LogCrackOrder(receiptInfo.UID, receiptInfo.OrderID, productItem)
			return nil, err
		}
	} else {
		webOrder = &order.WebOrder{
			UID:           receiptInfo.UID,
			OrderID:       GenerateOrderID(productItem.ID),
			ProductID:     productItem.ID,
			OrderAmount:   productItem.Price,
			PayType:       pay.PayTypeApple,
			IsRenew:       library.Yes,
			Source:        receiptInfo.Source,
			SourceID:      receiptInfo.SourceID,
			SourceRefer:   receiptInfo.SourceRefer,
			SourceReferID: receiptInfo.SourceReferID,
			Channel:       receiptInfo.Channel,
			Version:       receiptInfo.Version,
		}
		if receiptOrder.OriginalTransactionID == receiptOrder.TransactionID {
			webOrder.IsRenew = library.No
		}
		if err = webOrder.SaveByTran(session); err != nil {
			return nil, err
		}
	}
	isTrialPeriod := 0
	if receiptOrder.IsTrialPeriod == library.IsTrue {
		isTrialPeriod = library.Yes
	}
	isInIntroOfferPeriod := 0
	if receiptOrder.IsInIntroOfferPeriod == library.IsTrue {
		isInIntroOfferPeriod = library.Yes
	}
	iOrder := &iap.IOSOrder{
		OriginalTranID:       receiptOrder.OriginalTransactionID,
		TranID:               receiptOrder.TransactionID,
		WebItemID:            receiptOrder.WebItemID,
		ProductID:            receiptOrder.ProductID,
		UID:                  webOrder.UID,
		OrderID:              webOrder.OrderID,
		PurchaseTime:         receiptOrder.PurchaseTime,
		CancelTime:           receiptOrder.CancelTime,
		ExpireTime:           receiptOrder.ExpireTime,
		Receipt:              receiptInfo.Receipt,
		IsTrialPeriod:        isTrialPeriod,
		IsInIntroOfferPeriod: isInIntroOfferPeriod,
	}
	if err = iOrder.SaveByTran(session); err != nil {
		return nil, err
	}
	if err = writeAppleSubscribe(receiptOrder, receiptInfo, webOrder, productItem, session); err != nil {
		return nil, err
	}
	if err = CompleteWebOrderBySess(webOrder, productItem, iOrder, session); err != nil {
		return nil, err
	}
	if err = session.Commit(); err != nil {
		return nil, err
	}
	sensor.ReportPurchaseOrder(webOrder, productItem, &library.AppClient{
		Channel: webOrder.Channel,
		Version: webOrder.Version,
	})
	logger.Info("IOS生成订单成功", receiptOrder.TransactionID, webOrder.OrderID)
	return webOrder, nil
}

// writeAppleSubscribe 记录用户订阅信息
func writeAppleSubscribe(receiptOrder *IAPTransactionItem, receiptInfo *IAPClientPayReceipt,
	webOrder *order.WebOrder, productItem *product.WebProduct, session *xorm.Session) error {
	if receiptOrder.ExpireTime == 0 {
		return nil
	}
	subInfo := user.TbWPSubU.GetItemByThirdOriginalTranID(receiptInfo.UID, receiptOrder.OriginalTransactionID)
	if subInfo != nil {
		subInfo.ExpiresTime = receiptOrder.ExpireTime
		subInfo.ThirdPartTransactionID = receiptOrder.TransactionID
		subInfo.Status = pay.ContractChangeTypeEnum.ContractChangeTypeAdd
		subInfo.OrderID = webOrder.OrderID
		subInfo.PayType = pay.PayTypeApple
		subInfo.ProductID = int(webOrder.ProductID)
		if err := subInfo.UpdateByTran(session); err != nil {
			return err
		}
		return nil
	}
	subInfo = &user.WebProductSubscribeUser{
		UID:                    webOrder.UID,
		ProductID:              int(webOrder.ProductID),
		OrderID:                webOrder.OrderID,
		OriginalOrderID:        webOrder.OrderID,
		PayType:                pay.PayTypeApple,
		ThirdPartTransactionID: receiptOrder.TransactionID,
		OriginalTransactionID:  receiptOrder.OriginalTransactionID,
		Status:                 pay.ContractChangeTypeEnum.ContractChangeTypeAdd,
		ExpiresTime:            receiptOrder.ExpireTime,
		OfferType:              productItem.OfferType,
	}
	if err := subInfo.SaveByTran(session); err != nil {
		return err
	}
	return nil
}

type IAPTransactionItem struct {
	ProductID             string `json:"product_id"`
	TransactionID         string `json:"transaction_id"`
	OriginalTransactionID string `json:"original_transaction_id"`
	PurchaseTime          int64  `json:"purchase_time"`
	ExpireTime            int64  `json:"expire_time"`
	CancelTime            int64  `json:"cancel_time"`
	IsSandBox             int32  `json:"is_sandbox"`
	WebItemID             string `json:"web_order_line_item_id"`
	IsUpgraded            string `json:"is_upgraded"`
	IsTrialPeriod         string `json:"is_trial_period"`
	IsInIntroOfferPeriod  string `json:"is_in_intro_offer_period,omitempty"`
}

// verifyIAPReceipt 调用苹果接口验证receipt
func verifyIAPReceipt(receiptStr string) (*appstore.IAPResponse, error) {
	// 校验receipt获取订单详细数据
	client := appstore.New()
	req := appstore.IAPRequest{
		ReceiptData: receiptStr,
		Password:    config.Get().Service.ApplePasswd,
	}
	var err error
	var resp *appstore.IAPResponse
	for i := 0; i < 3; i++ {
		resp = &appstore.IAPResponse{}
		if err = client.Verify(context.Background(), req, resp); err != nil {
			logger.Warn(err)
			continue
		}
		break
	}
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func getIAPOrderList(resp *appstore.IAPResponse) []*IAPTransactionItem {
	result := make([]*IAPTransactionItem, 0)
	appendF := func(list []*IAPTransactionItem, item *IAPTransactionItem) {
		if item.IsSandBox == library.Yes && item.PurchaseTime < time.Now().Unix()-600 {
			return
		}
		exist := false
		for _, v := range list {
			if v.OriginalTransactionID == item.OriginalTransactionID &&
				v.TransactionID == item.TransactionID {
				exist = true
				break
			}
		}
		if !exist {
			result = append(result, item)
		}
	}
	for k := range resp.LatestReceiptInfo {
		info := formatIAPTransaction(&resp.LatestReceiptInfo[k])
		if info == nil {
			continue
		}
		if resp.Environment == appstore.Sandbox {
			info.IsSandBox = library.Yes
		}
		appendF(result, info)
	}
	for k := range resp.Receipt.InApp {
		info := formatIAPTransaction(&resp.Receipt.InApp[k])
		if info == nil {
			continue
		}
		if resp.Environment == appstore.Sandbox {
			info.IsSandBox = library.Yes
		}
		appendF(result, info)
	}
	if len(result) == 0 {
		return result
	}
	sort.Slice(result, func(i, j int) bool {
		return result[i].PurchaseTime < result[j].PurchaseTime
	})
	return result
}

func formatIAPTransaction(tran *appstore.InApp) *IAPTransactionItem {
	mt := 1000
	purchaseDate, _ := strconv.Atoi(tran.PurchaseDateMS)
	expireDate, _ := strconv.Atoi(tran.ExpiresDateMS)
	info := &IAPTransactionItem{
		ProductID:             tran.ProductID,
		WebItemID:             tran.WebOrderLineItemID,
		TransactionID:         tran.TransactionID,
		OriginalTransactionID: tran.OriginalTransactionID,
		PurchaseTime:          int64(purchaseDate / mt),
		ExpireTime:            int64(expireDate / mt),
		IsSandBox:             library.No,
		IsUpgraded:            tran.IsUpgraded,
		IsTrialPeriod:         tran.IsTrialPeriod,
		IsInIntroOfferPeriod:  tran.IsInIntroOfferPeriod,
	}

	if tran.CancellationDateMS != "" {
		cancelData, _ := strconv.Atoi(tran.CancellationDateMS)
		info.CancelTime = int64(cancelData / mt)
	}
	// 非续期订阅
	if info.ExpireTime == 0 {
		return info
	}
	return info
}

// ParseIosNotificationData 解析Ios支付回调数据
func ParseIosNotificationData(noticeData string) *appstore.SubscriptionNotification {
	messageData := &appstore.SubscriptionNotification{}
	if err := json.Unmarshal([]byte(noticeData), messageData); err != nil {
		logger.Error(err)
		return nil
	}
	return messageData
}

// DealIOSNotification 处理IOS服务端通知
func DealIOSNotification(nItem *iap.Notification) {
	lockKey := fmt.Sprintf("%s:%d", libc.LockDealIOSNotificationLog, nItem.ID)
	expireTime := 5
	lock := cache.GetCRedis().Lock(lockKey, cache.LockExpiration(time.Duration(expireTime)*time.Minute))
	if !lock {
		return
	}
	defer func() {
		err := cache.GetCRedis().Unlock(lockKey)
		if err != nil {
			logger.Warn(err)
		}
	}()
	logger.Info("IOS服务端通知处理", nItem.ID)
	notification := ParseIosNotificationData(nItem.NotificationContent)
	if notification == nil {
		return
	}
	defer func() {
		nItem.IsDeal = library.Yes
		if err := nItem.Update(); err != nil {
			logger.Error("IOS服务端通知处理完成标记状态失败", err, nItem.ID)
		}
	}()
	notificationType := notification.NotificationType
	receipt := notification.UnifiedReceipt.LatestReceipt
	originalTransactionID := string(notification.OriginalTransactionID)
	// 续订状态变更
	if notificationType == appstore.NotificationTypeDidChangeRenewalStatus {
		autoRenewStatus := pay.ContractChangeTypeEnum.ContractChangeTypeAdd
		if notification.AutoRenewStatus == "false" {
			autoRenewStatus = pay.ContractChangeTypeEnum.ContractChangeTypeDelete
		}
		logger.Info("IOS服务端通知更新订阅状态", originalTransactionID, autoRenewStatus)
		changeTransactionRenewStatus(originalTransactionID, autoRenewStatus)
		return
	}
	iapRes, err := verifyIAPReceipt(receipt)
	if err != nil {
		logger.Error(err)
		return
	}
	inAppOrderList := getIAPOrderList(iapRes)
	// 处理退款订单
	dealNotificationCancelOrder(inAppOrderList)
	var uid int64
	for _, v := range inAppOrderList {
		iOrder := iap.TbIOSOrder.GetItemByTran(v.OriginalTransactionID, v.TransactionID)
		if iOrder != nil && uid == 0 {
			webOrder := order.TbWebOrder.GetItemByOrderID(iOrder.OrderID)
			if webOrder != nil {
				uid = webOrder.UID
			}
		}
	}
	if uid == 0 {
		logger.Warn("没有对应UID，不处理", nItem.ID)
		return
	}
	iapReceiptInfo := &IAPClientPayReceipt{UID: uid, Receipt: receipt}
	rsp := analysisIAPOrderList(inAppOrderList, iapReceiptInfo)
	rspByte, _ := json.Marshal(rsp)
	nItem.ProcessResult = string(rspByte)
	logger.Info("IOS服务端通知处理receipt", nItem.ID, uid, string(rspByte))
}

// dealNotificationCancelOrder 处理退款订单
func dealNotificationCancelOrder(inAppOrderList []*IAPTransactionItem) {
	inAppCancelOrderList := make([]*IAPTransactionItem, 0) // 已退款订单列表
	for _, v := range inAppOrderList {
		if v.CancelTime > 0 || v.IsUpgraded == library.IsTrue {
			inAppCancelOrderList = append(inAppCancelOrderList, v)
		}
	}
	if len(inAppCancelOrderList) == 0 {
		return
	}
	for _, v := range inAppCancelOrderList {
		re := iap.TbIOSOrder.GetItemByTran(v.OriginalTransactionID, v.TransactionID)
		if re == nil {
			re = iap.TbIOSOrder.GetItemByWebItemID(v.OriginalTransactionID, v.WebItemID)
		}
		if re == nil || re.OrderID == "" {
			logger.Error("退款订单没有找到对应原始订单", v.OriginalTransactionID, v.TransactionID, v.WebItemID)
			continue
		}
		if re.CancelTime > 0 {
			continue
		}
		webOrder := order.TbWebOrder.GetItemByOrderID(re.OrderID)
		if webOrder == nil {
			logger.Error("退款订单对应原始订单为空", re.OrderID, v.OriginalTransactionID, v.TransactionID, v.WebItemID)
			continue
		}
		if v.IsUpgraded == library.IsTrue {
			re.IsUpgraded = library.Yes
		}
		cancelIAPTransaction(webOrder, re)
		// 数据上报
		sensor.ReportIOSRefundOrder(webOrder)
	}
}

func MockIosRefund(orderID string, refundStatus int) string {
	webOrder := order.TbWebOrder.GetItemByOrderID(orderID)
	if webOrder == nil {
		return "订单信息错误"
	}
	re := iap.TbIOSOrder.GetItemByOrderID(orderID)
	if re == nil {
		return "退款订单没有找到iso订单"
	}
	if re.CancelTime > 0 {
		return "该订单已退款"
	}
	re.AnonymousID = GetAnonymousID(re.UID)
	switch refundStatus {
	case pay.IosRefundStatusAlreadyApplied:
		re.RefundStatus = pay.IosRefundStatusAlreadyApplied
		if err := re.Update(); err != nil {
			logger.Error("AppStore consumptionRequest err", err)
		}
	case pay.IosRefundStatusReject:
		re.RefundStatus = pay.IosRefundStatusReject
		if err := re.Update(); err != nil {
			logger.Error("AppStore consumptionRequest err", err)
		}
	case pay.IosRefundStatusSuccess:
		cancelIAPTransaction(webOrder, re)
	default:
		return "模拟ios退款类型错误"
	}

	return "模拟ios退款成功"
}

// cancelIAPTransaction 退款订单权益处理
// nolint
func cancelIAPTransaction(webOrder *order.WebOrder, iOrder *iap.IOSOrder) {
	logger.Info("IOS订单退款处理", webOrder.OrderID)
	productItem := product.TbWebProduct.GetItemByID(webOrder.ProductID)
	if productItem == nil {
		return
	}
	userItem := user.TbAccount.GetDBUserByID(webOrder.UID)
	if userItem == nil {
		return
	}
	session := databases.GetEngineMaster().NewSession()
	defer session.Close()
	if err := session.Begin(); err != nil {
		logger.Error(err)
		return
	}
	var err error
	defer func() {
		if err != nil {
			logger.Error("IOS订单退款处理失败", err)
			if err = session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()
	iOrder.CancelTime = time.Now().Unix()
	if iOrder.AnonymousID == "" {
		iOrder.AnonymousID = GetAnonymousID(iOrder.UID)
	}
	iOrder.RefundStatus = pay.IosRefundStatusSuccess
	durationType := productItem.DurationType
	durationValue := productItem.DurationValue
	if (iOrder.IsTrialPeriod == library.Yes || iOrder.IsInIntroOfferPeriod == library.Yes ||
		iOrder.IsUpgraded == library.Yes) && iOrder.ExpireTime != 0 {
		durationType = int(libp.DurationTypeEnum.Day)
		durationValue = int((iOrder.ExpireTime - time.Now().Unix()) / library.DayTime)
	}
	if userItem.IsLogoff != library.Yes && durationValue > 0 {
		if err = equity.SubEquityProcess(session, webOrder, productItem, durationType, durationValue); err != nil {
			logger.Error(err)
			return
		}
	}
	if err = iOrder.UpdateByTran(session); err != nil {
		return
	}
	if err = session.Commit(); err != nil {
		return
	}
	logger.Info("IOS订单退款处理成功", webOrder.OrderID)
}

func GetAnonymousID(uid int64) string {
	adUIDChannel := dbclient.TbAdUIDChannel.GetItemByUID(uid)
	if adUIDChannel == nil {
		return ""
	}
	adSensorsData := dbclient.TbAdSensorsData.GetItemByDeviceID(adUIDChannel.DeviceID)
	if adSensorsData == nil {
		return ""
	}
	return adSensorsData.AnonymousID
}

// changeTransactionRenewStatus 订阅续订状态修改
func changeTransactionRenewStatus(originalTransactionID string, autoRenewStatus pay.ContractChangeType) {
	subInfoList := user.TbWPSubU.GetListByThirdOriginalTranID(originalTransactionID)
	if len(subInfoList) == 0 {
		logger.Info("IOS 续订状态修改,没有对应记录", originalTransactionID, autoRenewStatus)
		return
	}
	changed := false
	for _, v := range subInfoList {
		if v.Status != autoRenewStatus {
			v.Status = autoRenewStatus
			changed = true
			if err := v.Update(); err != nil {
				logger.Error(err)
			}
		}
	}
	// 取消订阅上报记录
	if changed && autoRenewStatus == pay.ContractChangeTypeEnum.ContractChangeTypeDelete {
		orderList := iap.TbIOSOrder.GetListByOriginal(originalTransactionID)
		if len(orderList) == 0 {
			return
		}
		webOrder := order.TbWebOrder.GetItemByOrderID(orderList[0].OrderID)
		if webOrder == nil {
			return
		}
		sensor.ReportIOSUnSubscribe(webOrder, len(orderList))
	}
}

func GetIsIosOfferCondition(iosProductName string, uid int64) bool {
	if iosProductName == "" {
		return false
	}
	skuList := ""
	listIosGroup := product.TbIosOfferGroup.GetList()
	for _, v := range listIosGroup {
		if v == nil {
			continue
		}
		for _, sku := range strings.Split(v.SkuList, ",") {
			if sku == iosProductName {
				skuList = v.SkuList
				break
			}
		}
		if skuList != "" {
			break
		}
	}
	if skuList == "" {
		return false
	}
	iosOrderIDfvList := iap.TbIOSOrder.GetIOSOrderByProductID(uid, strings.Split(skuList, ","))
	orderArr := make([]string, 0)
	for _, v := range iosOrderIDfvList {
		orderArr = append(orderArr, v.OrderID)
	}
	if len(orderArr) == 0 {
		return true
	}
	if len(order.TbWebOrder.GetOrderListByOrder(orderArr)) > 0 {
		return false
	}
	return true
}
