package order

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	sc "gitlab.dailyyoga.com.cn/gokit/sensorsdata"
	"gitlab.dailyyoga.com.cn/protogen/pay-bridge-go/paybridge"
	"gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/databases/client"
	"gitlab.dailyyoga.com.cn/server/children/databases/conf"
	"gitlab.dailyyoga.com.cn/server/children/databases/iap"
	dbo "gitlab.dailyyoga.com.cn/server/children/databases/order"
	dbp "gitlab.dailyyoga.com.cn/server/children/databases/product"
	dbu "gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/grpc"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libc "gitlab.dailyyoga.com.cn/server/children/library/client"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	"gitlab.dailyyoga.com.cn/server/children/library/pay"
	libproduct "gitlab.dailyyoga.com.cn/server/children/library/product"
	libuser "gitlab.dailyyoga.com.cn/server/children/library/user"
	"gitlab.dailyyoga.com.cn/server/children/service/audit"
	"gitlab.dailyyoga.com.cn/server/children/service/equity"
	srvpay "gitlab.dailyyoga.com.cn/server/children/service/pay"
	"gitlab.dailyyoga.com.cn/server/children/service/sensor"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type PreOrderRequest struct {
	SID              string `json:"sid"`
	ProductID        int64  `json:"product_id"`
	PayType          int    `json:"pay_type"`
	UID              int64  `json:"uid"`
	Source           string `json:"source_type"`
	SourceID         string `json:"source_id"`
	SourceRefer      string `json:"source_refer"`
	SourceReferID    string `json:"source_refer_id"`
	PaymentOrderType int    `json:"payment_order_type"`
	ProductSubID     int64  `json:"product_sub_id"`
	ChallengeID      int64  `json:"challenge_id"`
	PaymentPageID    int64  `json:"page_id"`
	DealUpdateTimes  int64  `json:"deal_update_times"`
	PayScenes        int    `json:"pay_scenes"`
}

type PayOrderParam struct {
	OrderID string
	Amount  float64
	// 传给支付第三方的描述信息
	Body         string
	Subject      string
	IsSubscribe  int
	MchID        string // 第三方商户号
	ContractInfo *ContractInfo
	OrgAmount    float64
	IsH5Pay      bool
	ProductID    int64
	UID          int64
}
type ContractInfo struct {
	OrderID       string
	UID           int64
	ProductID     int64
	ContractCode  string
	PeriodType    string
	Period        int
	Price         float64
	ExecuteTime   string
	ServiceDes    string
	SubscribeMode pay.SubscribeModeInt
	ServiceTitle  string
	OpenID        string
	TemplateID    string // 签约的模版ID
}

// nolint
func PreOrder(req *PreOrderRequest, product *dbp.WebProduct, client *library.AppClient) *PayOrderParam {
	beforePrice := product.Price
	if err := CalculateOffer(req, product, client, false); err != nil {
		logger.Error("下单无效商品 优惠产品", *req, err.Error())
		return nil
	}
	order := &dbo.WebOrder{
		UID:              req.UID,
		ProductID:        req.ProductID,
		PayType:          req.PayType,
		MchID:            GetCMchIDByPayType(req.PayType, req.ProductID, client),
		OrderID:          GenerateOrderID(product.ID),
		OrderAmount:      product.Price,
		OrderStatus:      pay.OrderStatus.UnPay,
		IsRenew:          library.No,
		Source:           req.Source,
		SourceID:         req.SourceID,
		SourceRefer:      req.SourceRefer,
		SourceReferID:    req.SourceReferID,
		Channel:          client.Channel,
		Version:          client.Version,
		PaymentOrderType: req.PaymentOrderType,
		ProductSubID:     req.ProductSubID,
		ChallengeID:      req.ChallengeID,
	}
	if product.HasGift == library.Yes && order.IsRenew == library.No {
		order.HasGift = library.Yes
	}
	if err := order.Save(); err != nil {
		logger.Error(err)
		return nil
	}
	res := &PayOrderParam{
		OrderID:     order.OrderID,
		Amount:      order.OrderAmount,
		IsSubscribe: product.IsSubscribe,
		Body:        "开通小树苗运动会员",
		Subject:     "开通小树苗运动会员",
		MchID:       order.MchID,
		OrgAmount:   beforePrice,
		UID:         order.UID,
	}
	if req.PayType == pay.PayTypeAlipay && product.IsSubscribe == library.Yes {
		contract := GenerateContract(req.UID, order.OrderID, order.MchID, product, client, order)
		if contract == nil {
			return nil
		}
		res.ContractInfo = contract
	}
	if req.PayType == pay.PayTypeTikTok && product.IsSubscribe == library.Yes {
		contract := GenerateTiTokContract(req.UID, order.OrderID, order.MchID, product)
		if contract == nil {
			return nil
		}
		res.ContractInfo = contract
	}
	// 如果是微信或者支付宝的单次，走h5支付
	if client.OsType == int(libc.DeviceTypeEnum.IOS) &&
		product.IsSubscribe != library.Yes && req.PayType != pay.PayTypeApple {
		res.IsH5Pay = true
	}
	srvpay.SetPaymentPageIDCache(order.OrderID, req.PaymentPageID)
	srvpay.SetDealUpdateTimesCache(order.OrderID, req.DealUpdateTimes)
	srvpay.SetUserVipStatusCache(order.UID)
	srvpay.SetUserOldBuyNumCache(order.UID)
	// 神策上报下单事件
	sensor.ReportSubmitOrder(order, product, client)
	return res
}

func GenerateTiTokContract(uid int64, orderID, mchID string, product *dbp.WebProduct) *ContractInfo {
	// 获取用户的openID
	accountThird := dbu.TbAccountThirdAuth.GetBindUID(uid, libuser.LoginTypeTikTok)
	if accountThird == nil || accountThird.ThirdOpenID == "" {
		return nil
	}
	var templateID string
	appID := pay.TikTokMchIDToAppID[mchID]
	if product.TiktokSubID != "" && appID == product.TiktokAppID {
		templateID = product.TiktokSubID
	} else {
		var ok bool
		templateID, ok = pay.TikTokMchIDToDeduceTemplate[mchID]
		if !ok {
			logger.Errorf("商户号无对应的 扣款模版ID 商户ID：%s", mchID)
			return nil
		}
	}
	logger.Infof("Tiktok product:%+v", product)
	currentMode, execDate := getTiktokExecuteTime(product)
	res := ContractInfo{
		OrderID:       orderID,
		UID:           uid,
		ProductID:     product.ID,
		ExecuteTime:   execDate,
		ContractCode:  orderID + string(config.Get().Service.Env),
		Price:         product.Price,
		SubscribeMode: currentMode,
		OpenID:        accountThird.ThirdOpenID,
		TemplateID:    templateID, // 抖音的模版ID
	}
	aliCon := &dbo.TiktokContract{
		UID:                uid,
		OrderID:            orderID,
		ProductID:          res.ProductID,
		OpenID:             res.OpenID,
		ContractCode:       res.ContractCode,
		ExecuteTime:        res.ExecuteTime,
		MerchantID:         mchID,
		OfferType:          int32(product.OfferType),
		OfferFirstBuyCycle: int32(product.OfferFirstBuyCycle),
		OfferFirstBuyPrice: product.OfferFirstBuyPrice,
		OfferTrialDay:      int32(product.OfferTrialDay),
		OfferTrialPrice:    product.OfferTrialPrice,
		ServiceID:          res.TemplateID,
	}
	if err := aliCon.Save(); err != nil {
		logger.Error(err)
		return nil
	}
	return &res
}

// nolint
func getTiktokExecuteTime(product *dbp.WebProduct) (currentMode pay.SubscribeModeInt, execDate string) {
	date := time.Now()
	year := date.Year()
	month := date.Month()
	day := date.Day()
	execTime := time.Date(year, month, day, 0, 0, 0, 0, time.Local)
	currentMode = pay.SubscribeModeEnum.OnlySubscribe
	if product.OfferType > libproduct.ConstOfferTypeNo {
		switch product.OfferType {
		case libproduct.ConstOfferTypeFirstBuy:
			currentMode = pay.SubscribeModeEnum.PayAndSubscribe
			execTimeInt := equity.CalcVipTime(execTime.Unix(), product.DurationType, product.DurationValue)
			execTime = time.Unix(execTimeInt, 0)
		case libproduct.ConstOfferTypeTrial:
			currentMode = pay.SubscribeModeEnum.PayAndSubscribe
			if product.OfferTrialPrice == 0 {
				currentMode = pay.SubscribeModeEnum.OnlySubscribe
			}
			execTimeInt := equity.CalcVipTime(date.Unix(), int(libproduct.DurationTypeEnum.Day),
				int(product.OfferTrialDay))
			execTime = time.Unix(execTimeInt, 0)
		default:
			logger.Warn("未支持支付方式", product)
		}
	}
	execDate = execTime.Format("2006-01-02")
	return currentMode, execDate
}

// CalculateOffer 计算优惠（预下单+强付费的时候apple不替换价格,只有预下单的时候isReplace:false）
// nolint
func CalculateOffer(req *PreOrderRequest, product *dbp.WebProduct, appClient *library.AppClient, isReplace bool) error {
	logger.Infof("会员优惠产品 计算优惠 product：%+v req:%+v", product, req)
	// 非自然量的时候才能进入
	isOffer := false
	if library.IntInArray(req.PayType, []int{pay.PayTypeAlipay}) && product.IsSubscribe == library.Yes &&
		product.OfferType > libproduct.ConstOfferTypeNo && CheckOfferVersion(product, appClient) {
		orderList := dbo.TbWebOrder.GetListByUID(req.UID)
		logger.Info("会员优惠产品 orderList", orderList)
		isOffer = true
		for _, o := range orderList {
			if o.ProductID == product.ID {
				isOffer = false
				break
			}
		}
	}
	if isOffer {
		switch product.OfferType {
		case libproduct.ConstOfferTypeFirstBuy:
			// 针对首购价格大于原价，特殊处理
			if product.OfferFirstBuyPrice > product.OriginPrice {
				product.OriginPrice = product.OfferFirstBuyPrice
			}
			product.Price = product.OfferFirstBuyPrice
		case libproduct.ConstOfferTypeTrial, libproduct.ConstOfferTypeTrialFirstBuy:
			product.Price = product.OfferTrialPrice
		default:
			logger.Error("错误的优惠类型，请联系客服", *req)
			return errors.New("错误的优惠类型")
		}
		return nil
	}
	product.OfferType = libproduct.ConstOfferTypeNo
	product.OfferFirstBuyPrice = 0
	product.OfferFirstBuyCycle = 0
	product.OfferTrialPrice = 0
	product.OfferTrialDay = 0
	return nil
}

func CheckOfferVersion(product *dbp.WebProduct, appClient *library.AppClient) bool {
	cv := util.UVersion.Format(appClient.Version)
	if !cv.Success || cv.Version < 10200 && library.IntInArray(product.OfferType,
		[]int{libproduct.ConstOfferTypeTrial, libproduct.ConstOfferTypeTrialFirstBuy}) && product.OfferTrialPrice > 0 {
		return false
	}
	return true
}

// CheckSubscribeLimit 检查用户订阅状态，有订阅分两种，有订阅且上次扣款成功和有订阅且扣款失败
// nolint
func CheckSubscribeLimit(uid int64, product *dbp.WebProduct, req *PreOrderRequest) errorcode.ErrorCode {
	// 支付宝方放开限制
	if product.IsSubscribe != library.Yes {
		return errorcode.Success
	}

	subList := dbu.TbWPSubU.GetValidSubscribeUser(uid)
	if len(subList) == 0 {
		return errorcode.Success
	}
	pMap := dbp.TbWebProduct.GetAllProductMap()
	if req.PayType == pay.PayTypeAlipay && product.IsSubscribe == library.Yes {
		return errorcode.Success
	}
	// 用户会员订阅，不能买会员、打包购订阅，可以买语音包订阅;
	// 用户打包购订阅，不能买会员、打包购订阅，可以买语音包订阅
	// 用户语音包订阅，不能买语音包订阅，可以买会员、打包购订阅
	for _, v := range subList {
		if pMap[int64(v.ProductID)].VipType == product.VipType {
			return userHasSubErr(v, product)
		} else if (product.VipType == libproduct.ProductVipTypeVIP) &&
			(pMap[int64(v.ProductID)].VipType == libproduct.ProductVipTypeVIP) {
			return userHasSubErr(v, product)
		}
	}
	return errorcode.Success
}

func userHasSubErr(v *dbu.WebProductSubscribeUser, product *dbp.WebProduct) errorcode.ErrorCode {
	if v.PayType != pay.PayTypeAlipay {
		return formatErrorCode(errorcode.HasSubscribeSuccess, product.VipType)
	}
	charge := dbo.TbAlipayCharge.GetLastChargeByCode(v.ThirdPartTransactionID)
	// 如果扣款记录为空，那应该是支付中签约的订阅记录
	if charge == nil {
		return formatErrorCode(errorcode.HasSubscribeSuccess, product.VipType)
	}
	if charge.Status == library.Yes {
		return formatErrorCode(errorcode.HasSubscribeSuccess, product.VipType)
	}
	return formatErrorCode(errorcode.SubscribeButChargeFail, product.VipType)
}

func formatErrorCode(code errorcode.ErrorCode, vipType int) errorcode.ErrorCode {
	logger.Info("formatErrorCode", code, vipType)
	return code
}

// GetCMchIDByPayType 根据payType获取当前线上的商户号
func GetCMchIDByPayType(payType int, productID int64, appClient *library.AppClient) string {
	cfg := config.Get()
	switch payType {
	case pay.PayTypeAlipay:
		return GetAliAppID(productID)
	case pay.PayTypeWechat:
		c := util.GetWxMiNiAppAccessByAppClient(appClient)
		if c.MchID != "" {
			return c.MchID
		}
		return cfg.WechatPay.MchID
	case pay.PayTypeTikTok:
		return util.GetTikTokAccessByAppClient(appClient).MchID
	default:
	}
	return ""
}

func GetAliAppID(productID int64) string {
	validItem := conf.TbMchAccountConf.GetValid(int32(libproduct.FTProductTypeEnum.MemberProduct), productID)
	if validItem == nil {
		validItem = conf.TbMchAccountConf.GetDefault()
	}
	if validItem == nil || validItem.MchID == "" {
		logger.Error("无法读取支付宝商户号ID，请立即排查！！！！！！")
		return ""
	}
	return validItem.MchID
}

// GenerateContract 生成签约参数
func GenerateContract(uid int64, orderID, appID string,
	product *dbp.WebProduct, appClient *library.AppClient, order *dbo.WebOrder) *ContractInfo {
	currentMode, execDate := getALiExecuteTime(product)
	res := ContractInfo{
		OrderID:       orderID,
		UID:           uid,
		ProductID:     product.ID,
		ExecuteTime:   execDate,
		ContractCode:  orderID + string(config.Get().Service.Env),
		Price:         product.Price,
		SubscribeMode: currentMode,
	}
	res.ServiceDes, res.ServiceTitle = GetAliPaySignWriting(product, uid, appClient, order)
	res.PeriodType, res.Period = GetAlipayPeriod(product.DurationType, product.DurationValue)
	alicon := &dbo.AlipayContract{
		UID:                uid,
		OrderID:            orderID,
		ProductID:          res.ProductID,
		ContractCode:       res.ContractCode,
		PeriodType:         res.PeriodType,
		Period:             res.Period,
		ExecuteTime:        execDate,
		AppID:              appID,
		SubscribeMode:      int(currentMode),
		OfferType:          product.OfferType,
		OfferFirstBuyCycle: product.OfferFirstBuyCycle,
		OfferFirstBuyPrice: product.OfferFirstBuyPrice,
		OfferTrialDay:      product.OfferTrialDay,
		OfferTrialPrice:    product.OfferTrialPrice,
	}
	if err := alicon.Save(); err != nil {
		logger.Error(err)
		return nil
	}
	return &res
}

const (
	ABTestAliPaySignWriting       = 10003 // π项目OB付费页
	AliPaySignWritingTrackAbtType = 60001
)

// nolint
func GetAliPaySignWriting(product *dbp.WebProduct, uid int64, clientParam *library.AppClient, order *dbo.WebOrder) (w, wt string) {
	defaultWriting := "开通成功后，可解锁全部VIP课程，到期自动进行续费，可随时取消，取消后不再自动续费"
	defaultWritingTitle := "小树苗运动自动续费"
	key := libproduct.ConstOfferTypeConfKey[product.OfferType]
	var result AliPaySigningCopyWriting
	info := client.TbConfig.GetItemByKey(key)
	if info != nil {
		if err := json.Unmarshal([]byte(info.Value), &result); err != nil {
			return defaultWriting, defaultWritingTitle
		}
	}
	rateList := make([]*util.Rate, 0)
	for i, cf := range result.Conf {
		if cf.Flow == 0 {
			continue
		}
		rateList = append(rateList, &util.Rate{
			ID:      int64(i + 1),
			Percent: uint32(cf.Flow),
		})
	}
	matchID, ok := util.RateMatch(ABTestAliPaySignWriting, strconv.FormatInt(uid, 10), rateList).(int64)
	if !ok || matchID == 0 {
		return defaultWriting, defaultWritingTitle
	}
	matchID--
	if matchID >= int64(len(result.Conf)) {
		return defaultWriting, defaultWritingTitle
	}
	obProgramTrack := &AliPaySignWritingTrack{
		AbtType:    AliPaySignWritingTrackAbtType,
		AbtVersion: fmt.Sprintf("文案%d", matchID+1),
		CommData:   sensor.InitCommDataByClient(clientParam),
		AbtScene:   libproduct.OfferTypeDescTrackAbt[product.OfferType],
	}
	obProgramTrack.Track(strconv.FormatInt(uid, 10))
	resultWriting := ""
	CopyWriting := result.Conf[matchID].CopyWriting
	resultWritingTitle := result.Conf[matchID].CopyWritingTitle
	if order.ChallengeID > 0 {
		CopyWriting = result.Conf[matchID].ChallengeCopyWriting
		resultWritingTitle = result.Conf[matchID].ChallengeCopyWritingTitle
	}
	// 合规
	if audit.GetCompliance(uid, clientParam) {
		CopyWriting = result.CheckCopyWriting
		resultWritingTitle = result.CheckCopyWritingTitle
	}
	switch product.OfferType {
	case libproduct.ConstOfferTypeNo:
		resultWriting = ConstOfferTypeNo(product, CopyWriting)
	case libproduct.ConstOfferTypeFirstBuy:
		resultWriting = ConstOfferTypeFirstBuy(product, CopyWriting)
	case libproduct.ConstOfferTypeTrial:
		resultWriting = ConstOfferTypeTrial(product, CopyWriting)
	case libproduct.ConstOfferTypeTrialFirstBuy:
		resultWriting = ConstOfferTypeTrialFirstBuy(product, CopyWriting)
	default:
		return defaultWriting, defaultWritingTitle
	}
	if resultWriting == "" || resultWritingTitle == "" {
		return defaultWriting, defaultWritingTitle
	}
	return resultWriting, resultWritingTitle
}

func ConstOfferTypeNo(product *dbp.WebProduct, copyWriting string) string {
	if copyWriting == "" {
		return ""
	}
	copyWriting = strings.Replace(copyWriting, "{n1}",
		GetProductTypeVal(product.DurationType, product.DurationValue, 1, true), -1)
	copyWriting = strings.Replace(copyWriting, "{n2}",
		strconv.FormatFloat(product.Price, 'f', 2, 64)+"元", -1)
	return copyWriting
}

func ConstOfferTypeFirstBuy(product *dbp.WebProduct, copyWriting string) string {
	if copyWriting == "" {
		return ""
	}
	productDef := dbp.TbWebProduct.GetItemByID(product.ID)
	copyWriting = strings.Replace(copyWriting, "{n1}",
		GetProductTypeVal(product.DurationType, product.DurationValue, 1, true), -1)
	copyWriting = strings.Replace(copyWriting, "{n2}",
		GetProductTypeVal(product.DurationType, product.DurationValue, int32(product.OfferFirstBuyCycle), false), -1)
	copyWriting = strings.Replace(copyWriting, "{n3}",
		strconv.FormatFloat(productDef.Price, 'f', 2, 64)+"元", -1)
	copyWriting = strings.Replace(copyWriting, "{n4}",
		strconv.FormatFloat(product.OfferFirstBuyPrice, 'f', 2, 64)+"元", -1)
	return copyWriting
}

func ConstOfferTypeTrial(product *dbp.WebProduct, copyWriting string) string {
	if copyWriting == "" {
		return ""
	}
	productDef := dbp.TbWebProduct.GetItemByID(product.ID)
	copyWriting = strings.Replace(copyWriting, "{n1}",
		GetProductTypeVal(product.DurationType, product.DurationValue, 1, true), -1)
	copyWriting = strings.Replace(copyWriting, "{n2}", fmt.Sprintf("%d", product.OfferTrialDay)+"天", -1)
	copyWriting = strings.Replace(copyWriting, "{n3}",
		strconv.FormatFloat(productDef.Price, 'f', 2, 64)+"元", -1)
	copyWriting = strings.Replace(copyWriting, "{n4}",
		strconv.FormatFloat(product.OfferTrialPrice, 'f', 2, 64)+"元", -1)
	return copyWriting
}

func ConstOfferTypeTrialFirstBuy(product *dbp.WebProduct, copyWriting string) string {
	if copyWriting == "" {
		return ""
	}
	productDef := dbp.TbWebProduct.GetItemByID(product.ID)
	copyWriting = strings.Replace(copyWriting, "{n1}",
		GetProductTypeVal(product.DurationType, product.DurationValue, 1, true), -1)
	copyWriting = strings.Replace(copyWriting, "{n2}", fmt.Sprintf("%d", product.OfferTrialDay)+"天", -1)
	copyWriting = strings.Replace(copyWriting, "{n3}",
		strconv.FormatFloat(productDef.Price, 'f', 2, 64)+"元", -1)
	copyWriting = strings.Replace(copyWriting, "{n4}",
		strconv.FormatFloat(product.OfferTrialPrice, 'f', 2, 64)+"元", -1)
	copyWriting = strings.Replace(copyWriting, "{n5}",
		GetProductTypeVal(product.DurationType, product.DurationValue, int32(product.OfferFirstBuyCycle), false), -1)
	copyWriting = strings.Replace(copyWriting, "{n6}",
		strconv.FormatFloat(product.OfferFirstBuyPrice, 'f', 2, 64)+"元", -1)
	return copyWriting
}

func GetProductTypeVal(durationType, durationValue int, cycle int32, isSimplify bool) string {
	if cycle == 0 {
		cycle = 1
	}
	value := int32(0)
	switch libproduct.DurationType(durationType) {
	case libproduct.DurationTypeEnum.Month:
		value = int32(durationValue) * cycle
	case libproduct.DurationTypeEnum.Year:
		value = int32(durationValue) * libproduct.YearToMonth * cycle
	case libproduct.DurationTypeEnum.Day:
		value = int32(durationValue) * cycle
	}
	if libproduct.DurationType(durationType) == libproduct.DurationTypeEnum.Day {
		if isSimplify && value == 1 {
			return "天"
		}
		return fmt.Sprintf("%d天", value)
	}
	if value%libproduct.YearToMonth == 0 {
		if isSimplify && value/libproduct.YearToMonth == 1 {
			return "年"
		}
		return fmt.Sprintf("%d年", value/libproduct.YearToMonth)
	}
	if isSimplify && value == 1 {
		return "月"
	}
	return fmt.Sprintf("%d月", value)
}

type CopyWritingRop struct {
	CopyWriting               string `json:"copywriting"`
	Flow                      int    `json:"flow"`
	CopyWritingTitle          string `json:"copywriting_title"`
	ChallengeCopyWriting      string `json:"challenge_copywriting"`
	ChallengeCopyWritingTitle string `json:"challenge_copywriting_title"`
}

type AliPaySigningCopyWriting struct {
	OfferType             int              `json:"offer_type"`
	Conf                  []CopyWritingRop `json:"conf"`
	CheckCopyWriting      string           `json:"check_copywriting"`
	CheckCopyWritingTitle string           `json:"check_copywriting_title"`
}

type AliPaySignWritingTrack struct {
	sensor.CommData `json:"common_data"`
	AbtType         int    `json:"abt_type"`
	AbtVersion      string `json:"abt_version"`
	AbtScene        string `json:"abt_scene"`
}

// EventName 订阅产品取消订阅上报
func (AliPaySignWritingTrack) EventName() string {
	if config.Get().Service.Env != microservice.Product && config.Get().Service.Env != microservice.Mirror {
		return "h2_cs_abt_user_data"
	}
	return "cs_abt_user_data"
}
func (AliPaySignWritingTrack) Prefix() string {
	return ""
}

func (u *AliPaySignWritingTrack) Track(uid string) {
	go func() {
		if uid == "" || uid == "0" {
			return
		}
		err := sc.Track(uid, *u, true)
		if err != nil {
			logger.Error("神策过期事件上报失败", err)
		}
	}()
}

func getALiExecuteTime(product *dbp.WebProduct) (currentMode pay.SubscribeModeInt, execDate string) {
	date := time.Now()
	todayLast := time.Date(date.Year(), date.Month(), date.Day(), 23, 50, 0, 0, time.Local)
	// 如果是靠近凌晨，将扣款时间改为明天，防止回调过来时已经是第二天了
	if date.Unix() > todayLast.Unix() {
		date = date.AddDate(0, 0, 1)
	}
	year := date.Year()
	month := date.Month()
	day := date.Day()
	execTime := time.Date(year, month, day, 0, 0, 0, 0, time.Local)
	// 支付宝不允许下次扣款时间在一个月的28日之后，避免有些月份没有这些日期
	maxDay := 28
	if date.Day() > maxDay {
		execTime = time.Date(year, month, 1, 0, 0, 0, 0, time.Local)
		execTime = execTime.AddDate(0, 1, 0)
	}
	currentMode = pay.SubscribeModeEnum.OnlySubscribe
	if currentMode == pay.SubscribeModeEnum.PayAndSubscribe {
		execTimeInt := equity.CalcVipTime(execTime.Unix(), product.DurationType, product.DurationValue)
		execTime = time.Unix(execTimeInt, 0)
	}
	originItem := dbp.TbWebProduct.GetItemByID(product.ID)
	var maxMoney float64 = 300
	if originItem != nil {
		maxMoney = originItem.Price
	}
	if product.OfferType > libproduct.ConstOfferTypeNo {
		switch product.OfferType {
		case libproduct.ConstOfferTypeFirstBuy:
			currentMode = pay.SubscribeModeEnum.OnlySubscribe
			if product.OfferFirstBuyPrice > maxMoney {
				currentMode = pay.SubscribeModeEnum.PayAndSubscribe
			}
		case libproduct.ConstOfferTypeTrial, libproduct.ConstOfferTypeTrialFirstBuy:
			if product.OfferTrialPrice == 0 {
				currentMode = pay.SubscribeModeEnum.OnlySubscribe
				if product.OfferFirstBuyPrice > maxMoney {
					currentMode = pay.SubscribeModeEnum.PayAndSubscribe
				}
			} else {
				currentMode = pay.SubscribeModeEnum.PayAndSubscribe
			}
			execTimeInt := equity.CalcVipTime(date.Unix(), int(libproduct.DurationTypeEnum.Day),
				product.OfferTrialDay)
			execTime = time.Unix(execTimeInt, 0)
			if execTime.Day() > maxDay {
				execTime = time.Date(execTime.Year(), execTime.Month(), 1, 0, 0, 0, 0, time.Local)
				execTime = execTime.AddDate(0, 1, 0)
			}
		}
	}
	execDate = execTime.Format("2006-01-02")
	return currentMode, execDate
}

// GetAlipayPeriod 获取阿里的period参数
// nolint
func GetAlipayPeriod(durationType, durationValue int) (ptype string, p int) {
	const (
		AliPeriodTypeMonth = "MONTH"
		AliPeriodTypeDay   = "DAY"
	)
	switch libproduct.DurationType(durationType) {
	case libproduct.DurationTypeEnum.Day:
		return AliPeriodTypeDay, durationValue
	case libproduct.DurationTypeEnum.Month:
		return AliPeriodTypeMonth, durationValue
	case libproduct.DurationTypeEnum.Year:
		return AliPeriodTypeMonth, durationValue * 12
	default:
		return "", 0
	}
}

// GenerateOrderID 生成订单号
func GenerateOrderID(productID int64) string {
	mid := ""
	str := strconv.FormatInt(time.Now().UnixNano(), 10)
	return "C" + mid + str
}

// CompleteOrder 完成订单逻辑
func CompleteOrder(notify *dbo.CompleteNotify, hasNotify bool) bool {
	order := dbo.TbWebOrder.GetItemByOrderID(notify.OrderID)
	if order == nil {
		logger.Error("完成订单,订单记录为空", notify.OrderID)
		return false
	}
	product := dbp.TbWebProduct.GetItemByID(order.ProductID)
	if product == nil {
		logger.Error("完成订单,商品为空", order.ProductID)
		return false
	}
	session := databases.GetEngineMaster().NewSession()
	defer session.Close()
	if err := session.Begin(); err != nil {
		logger.Error(err)
		return false
	}
	var err error
	defer func() {
		if err != nil {
			logger.Error(err)
			if err = session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()
	if err = CompleteWebOrderBySess(order, product, nil, session); err != nil {
		return false
	}
	safelygo.GoSafelyByTraceID(func() {
		CompleteInfoNotify(notify)
	})
	if hasNotify {
		notify.IsProcessed = library.Yes
		if err = notify.UpdateByTran(session); err != nil {
			return false
		}
	}
	if err = session.Commit(); err != nil {
		return false
	}
	logger.Infof("BigGiftPackageID uid:%d 产品：%#v", order.UID, product)

	// 支付中签约模式支付宝付款页面有个续订开关，如果关了，不会有签约回调，在这里同时上报签约和解约
	safelygo.GoSafelyByTraceID(func() {
		ReportSingleSubscribe(order, product)
	})
	sensor.ReportPurchaseOrder(order, product, &library.AppClient{
		Channel: order.Channel,
		Version: order.Version,
	})
	// 上报扣款成功
	safelygo.GoSafelyByTraceID(func() {
		ReportChargeSuccess(order, product)
	})
	return true
}

func CompleteInfoNotify(notify *dbo.CompleteNotify) {
	if notify.PayType != pay.PayTypeAlipay {
		return
	}
	res, err := grpc.GetPayBridgeClient().CompleteInfo(context.Background(), &paybridge.CompleteInfoRequest{
		OrderID:     notify.OrderID,
		Data:        notify.Notify,
		ProjectType: int32(library.ProjectTypeEnum.Children),
	})
	if err != nil {
		logger.Error("完成订单上报pay-bridge失败", err, notify.ID)
		return
	}
	if res.ErrorCode != 0 {
		logger.Error("完成订单上报pay-bridge失败", res.ErrorCode, res.Msg, notify.ID)
	}
}

func ReportChargeSuccess(order *dbo.WebOrder, product *dbp.WebProduct) {
	logger.Info("支付宝扣款上报神策开始", order.OrderID, product.IsSubscribe)
	if product.IsSubscribe != library.Yes {
		return
	}
	charge := dbo.TbAlipayCharge.GetItemByOrderID(order.OrderID)
	contract := dbo.TbAlipayContract.GetItemByOrderID(order.OrderID)
	chargeResult := &sensor.ChargeResultItem{
		IsSuccess: true,
		UID:       order.UID,
		OrderID:   order.OrderID,
		Price:     order.OrderAmount,
		PayType:   order.PayType,
	}
	if order.IsRenew == library.Yes {
		chargeResult.IsRenew = true
	}
	// 既无签约扣款记录，也无签约记录，非订阅 直接返回
	if charge == nil && contract == nil {
		logger.Info("支付宝扣款上报神策返回:无扣款记录,无签约记录", order.OrderID, product.IsSubscribe)
		return
	}
	// charge为空且contract 非空 为支付中签约的首次扣款
	if charge == nil && contract != nil {
		chargeResult.FirstSubscribeTime = contract.CreateTime
		chargeResult.ProductID = contract.ProductID
		chargeResult.ContractID = contract.ContractCode
		chargeResult.IsRenew = false
		chargeResult.SubscribeTimes = GetSubscribeTimesByContract(contract, order.OrderID)
		chargeResult.IsHistoryDiscard = contract.IsHistoryDiscard == library.Yes
		logger.Info("订阅首次扣款上报", *chargeResult)
		sensor.ReportChargeSuccessFail(chargeResult)
		return
	}
	// 脚本扣款
	if charge != nil {
		if contract == nil {
			contract = dbo.TbAlipayContract.GetItemByContractCode(charge.ContractCode)
		}
		if contract == nil {
			logger.Info("支付宝扣款上报神策返回:无签约记录", order.OrderID, charge.ContractCode)
			return
		}
		chargeResult.ChargeDate = charge.ChargeDate
		if err := CountRetryTimes(contract, chargeResult); err != nil {
			logger.Error("上报扣款完成事件失败", err)
			return
		}
		if charge.IsStepPrice == library.Yes {
			chargeResult.IsCoupon = true
		}
		chargeResult.ProductID = contract.ProductID
		chargeResult.FirstSubscribeTime = contract.CreateTime
		chargeResult.ContractID = contract.ContractCode
		chargeResult.SubscribeTimes = GetSubscribeTimesByContract(contract, order.OrderID)
		chargeResult.APPID = contract.AppID
		chargeResult.CouponMoney = charge.StepAmount
		chargeResult.IsHistoryDiscard = contract.IsHistoryDiscard == library.Yes
		logger.Info("订阅脚本扣款上报", *chargeResult)
		sensor.ReportChargeSuccessFail(chargeResult)
	}
}

// 统计重试次数和天数
func CountRetryTimes(contract *dbo.AlipayContract, chargeResult *sensor.ChargeResultItem) error {
	sub := dbu.TbWPSubU.GetItemByContractID(contract.ContractID)
	if sub == nil {
		return errors.New("签约记录为空:" + contract.ContractID)
	}
	// 如果没有失败记录，则为一次性成功
	if sub.BeginFailTime == 0 {
		return nil
	}
	chargeList := dbo.TbAlipayCharge.GetChargeListByRange(sub.BeginFailTime, time.Now().Unix(), contract.ContractCode)
	sub.BeginFailTime = 0
	if err := sub.Update(); err != nil {
		return err
	}
	if len(chargeList) == 0 {
		return nil
	}
	var currentDateRetryTimes int64
	dayMap := make(map[string]int)
	for _, v := range chargeList {
		if chargeResult.ChargeDate == v.ChargeDate {
			currentDateRetryTimes++
			continue
		}
		dayMap[v.ChargeDate] = 1
	}
	// 重试天数为失败天数 重试次数为
	chargeResult.RetryDays = int32(len(dayMap))
	chargeResult.RetryTimes = len(chargeList) - 1
	chargeResult.RetryTimesOfTheDay = int(currentDateRetryTimes) - 1
	return nil
}

// CompleteWebOrderBySess 完成订单逻辑
func CompleteWebOrderBySess(order *dbo.WebOrder, product *dbp.WebProduct,
	iOrder *iap.IOSOrder, session *xorm.Session) error {
	if order.PayType == pay.PayTypeAlipay {
		alipayContract := dbo.TbAlipayContract.GetItemByContractCode(order.OrderID + string(config.Get().Service.Env))
		if alipayContract != nil && (alipayContract.OfferType == libproduct.ConstOfferTypeTrial ||
			alipayContract.OfferType == libproduct.ConstOfferTypeTrialFirstBuy) {
			product.DurationType = int(libproduct.DurationTypeEnum.Day)
			product.DurationValue = alipayContract.OfferTrialDay
		}
	}
	if iOrder != nil && iOrder.ExpireTime != 0 &&
		(iOrder.IsTrialPeriod == library.Yes || iOrder.IsInIntroOfferPeriod == library.Yes) {
		product.DurationType = int(libproduct.DurationTypeEnum.Day)
		product.DurationValue = int((iOrder.ExpireTime-time.Now().Unix())/library.DayTime) + 1
		if product.OfferType == libproduct.ConstOfferTypeFirstBuy {
			order.OrderAmount = product.OfferFirstBuyPrice
		}
		if product.OfferType == libproduct.ConstOfferTypeTrial {
			order.OrderAmount = product.OfferTrialPrice
		}
	}
	if err := equity.AddEquityProcess(session, order, product); err != nil {
		return err
	}
	order.OrderStatus = pay.OrderStatus.Paid
	order.PayTime = time.Now().Unix()
	if err := order.UpdateByTran(session); err != nil {
		return err
	}
	return nil
}

// ReportSingleSubscribe 支付中签约模式 中只单次付款上报签约和解约
func ReportSingleSubscribe(order *dbo.WebOrder, product *dbp.WebProduct) {
	if product.IsSubscribe != library.Yes {
		return
	}
	time.Sleep(time.Minute)
	contract := dbo.TbAlipayContract.GetItemByOrderID(order.OrderID)
	// 不是签约订单或者 已经有签约
	if contract == nil || contract.ChangeType != 0 {
		logger.Info("签约协议不存在或者签约协议已签约")
		return
	}
	// 查询支付宝签约是否存在 存在直接返回
	if srvpay.SrvAlipay.AgreementQueryExist(contract.ContractCode, contract.AppID) {
		logger.Info("签约协议已存在", contract.ContractCode, contract.AppID)
		return
	}
	// 订阅上报
	sensor.ReportSubscribe(order, pay.SubscribeModeEnum.PayAndSubscribe, contract.ContractCode)
	// 星月反馈这里不能连在一起，需要等五秒
	var five time.Duration = 5
	time.Sleep(five * time.Second)
	// 订阅取消上报
	sensor.ReportUnSubscribe(order, contract.ContractCode, pay.UnsubscribeTypeEnum.User)
}

type AlipayCompleteRes struct {
	AppID string `json:"app_id"`
}
type WechatCompleteRes struct {
	MchID string `json:"mch_id"`
}

func GetMchIDByOrder(order *dbo.WebOrder) string {
	if order.MchID != "" {
		return order.MchID
	}
	completeOrder := dbo.TbCompleteNofify.GetItemByOrderID(order.OrderID)
	if completeOrder == nil {
		return ""
	}
	switch completeOrder.PayType {
	case pay.PayTypeAlipay:
		alires := &AlipayCompleteRes{}
		err := json.Unmarshal([]byte(completeOrder.Notify), alires)
		if err == nil {
			return alires.AppID
		}
	case pay.PayTypeWechat:
		wechatres := &WechatCompleteRes{}
		err := json.Unmarshal([]byte(completeOrder.Notify), wechatres)
		if err == nil {
			return wechatres.MchID
		}
	}
	return ""
}

type MemberDuration struct {
	StartTime      int64 `json:"start_time"`
	EndTime        int64 `json:"end_time"`
	IsValid        bool  `json:"is_valid"`
	PermanentlyVip int   `json:"permanently_vip"`
}

type MinOrderInfo struct {
	OrderID          string         `json:"order_id"`
	OrderStatus      int            `json:"order_status"`
	OrderTotalAmount string         `json:"order_total_amount"`
	OriginalPrice    string         `json:"original_price"`
	Price            string         `json:"price"`
	ProductName      string         `json:"product_name"`
	MemberDuration   MemberDuration `json:"member_duration"`
}

func GetOrderInfo(orderID string) *MinOrderInfo {
	order := dbo.TbWebOrder.GetItemByOrderID(orderID)
	if order == nil {
		return nil
	}
	product := dbp.TbWebProduct.GetItemByID(order.ProductID)
	if product == nil {
		return nil
	}
	account := dbu.TbAccount.GetDBUserByID(order.UID)
	if account == nil {
		return nil
	}
	return &MinOrderInfo{
		OrderID:          order.OrderID,
		OrderStatus:      int(order.OrderStatus),
		OrderTotalAmount: strconv.FormatFloat(order.OrderAmount, 'f', -1, 64),
		OriginalPrice:    strconv.FormatFloat(product.OriginPrice, 'f', -1, 64),
		Price:            strconv.FormatFloat(order.OrderAmount, 'f', -1, 64),
		ProductName:      product.Name,
		MemberDuration: MemberDuration{
			StartTime: account.StartTime,
			EndTime:   account.EndTime,
			IsValid:   account.EndTime > time.Now().Unix(),
		},
	}
}
