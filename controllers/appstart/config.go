package appstart

import (
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/crypto"
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/databases/client"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libc "gitlab.dailyyoga.com.cn/server/children/library/client"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	"gitlab.dailyyoga.com.cn/server/children/service/app"
	"gitlab.dailyyoga.com.cn/server/children/service/audit"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

// AppActivation app激活 数据回传
func AppActivation(t *http.Context) {
	oaID := t.GetRequestStringD("oaid", "")
	anonymousID := t.GetRequestStringD("anonymous_id", "")
	pkgName := t.GetRequestStringD("pkg_name", "")
	if oaID == "" || anonymousID == "" || pkgName == "" {
		t.Result(errorcode.InvalidParams)
		return
	}
	appClient := util.GetClientInfo(t)
	if strconv.Itoa(appClient.Channel) != libc.ChannelXiaomi &&
		strconv.Itoa(appClient.Channel) != libc.ChannelOPPO {
		t.Result(library.EmptyResponse{})
		return
	}
	act := &app.Activation{
		OuID:        oaID,
		AnonymousID: anonymousID,
		PkgName:     pkgName,
		ChannelStr:  strconv.Itoa(appClient.Channel),
		ActionType:  int64(libc.OppoOcpxTypeEnum.AppActivation),
	}
	safelygo.GoSafelyByTraceID(func() {
		_, _ = act.AppActivation()
	})
	t.Result(library.EmptyResponse{})
}

type AppConfigRsp struct {
	HasTrainCountdown      bool   `json:"has_train_countdown"`       // 开训倒计时开关
	IsPopupAndroidPositive bool   `json:"is_popup_android_positive"` // 是否弹出安卓好评弹窗
	IsOpenQiyu             bool   `json:"is_open_qiyu"`              // 在线客服入口开关
	SkipButtonNum          int    `json:"skip_button_num"`           // OB流程跳过配置
	StartTimeDay           int    `json:"start_time_day"`            // OB流程跳过配置
	CertA                  string `json:"cert_a"`
	IsReportNative         bool   `json:"is_report_native"`
}

type AndroidPositiveConfig struct {
	IsPopupPositive bool `json:"is_popup_positive"`
}
type CustomerServiceConfig struct {
	IsOpenQiyu bool `json:"is_open_qiyu"`
}

func AppConfig(t *http.Context) {
	appConfig := &AppConfigRsp{
		HasTrainCountdown: true,
		IsOpenQiyu:        true,
		IsReportNative:    true,
	}
	appClient := util.GetClientInfo(t)
	appConfig.CertA = AuditEncrypt(t, appClient)
	// 安卓返回是否弹出应用商店好评弹窗
	if appClient.OsType == int(libc.DeviceTypeEnum.Android) || appClient.OsType == int(libc.DeviceTypeEnum.Harmony) {
		configItem := client.TbConfig.GetItemByKey(libc.AndroidPositiveConfig)
		if configItem != nil {
			configValue := &AndroidPositiveConfig{}
			err := json.Unmarshal([]byte(configItem.Value), configValue)
			if err == nil {
				appConfig.IsPopupAndroidPositive = configValue.IsPopupPositive
			}
		}
	}
	configItem := client.TbConfig.GetItemByKey(libc.ConfigCustomerService)
	if configItem != nil {
		configValue := &CustomerServiceConfig{}
		err := json.Unmarshal([]byte(configItem.Value), configValue)
		if err == nil {
			appConfig.IsOpenQiyu = configValue.IsOpenQiyu
		}
	}
	cv := util.UVersion.Format(appClient.Version)
	if (appClient.OsType == int(libc.DeviceTypeEnum.Android) ||
		appClient.OsType == int(libc.DeviceTypeEnum.Harmony)) && cv.Version < 10804 {
		appConfig.IsOpenQiyu = false
	}
	obSkipResp := ObSkipNumInfo()
	appConfig.SkipButtonNum = obSkipResp.SkipButtonNum
	appConfig.StartTimeDay = obSkipResp.StartTimeDay
	t.Result(appConfig)
}

type EncrypSwitch struct {
	ButtonTextA            string `json:"button_text_a"`
	ButtonTextS            string `json:"button_text_s"`
	ButtonTextR            string `json:"button_text_r"`
	ButtonTextJL           string `json:"button_text_jl"`
	ButtonTextJLVIP        string `json:"button_text_jl_vip"`
	ButtonTextJLChallenge  string `json:"button_text_jl_challenge"`
	ButtonTextJLNewPage    string `json:"button_text_jl_new_page"`
	ButtonTextJLNormalPage string `json:"button_text_jl_normal_page"`
	ButtonTextJLDialog     string `json:"button_text_jl_dialog"`
	ButtonTextJLSelect     string `json:"button_text_jl_select"`
}

func AuditEncrypt(t *http.Context, appClient *library.AppClient) string {
	var wg sync.WaitGroup
	switchInfo := &EncrypSwitch{}
	wg.Add(1)
	safelygo.GoSafelyByTraceID(func() {
		defer wg.Done()
		if audit.GetAuditInfo(appClient).IsInAudit {
			switchInfo.ButtonTextA = "下一步"
		}
	})
	uid := t.GetRequestInt64D("uid", 0)
	wg.Add(1)
	safelygo.GoSafelyByTraceID(func() {
		defer wg.Done()
		if audit.GetCompliance(uid, appClient) {
			switchInfo.ButtonTextS = "上一步"
		}
	})
	wg.Add(1)
	safelygo.GoSafelyByTraceID(func() {
		defer wg.Done()
		auditFunc := audit.GetFuncSwitchByKey(appClient, libc.OnlineConfObJump, 0)
		if auditFunc.Audit || auditFunc.OnlineSwitch {
			switchInfo.ButtonTextR = "开始"
		}
	})
	buttonTextJL := "确认"
	wg.Add(1)
	safelygo.GoSafelyByTraceID(func() {
		defer wg.Done()
		jlSwitch := audit.GetJuLiangSwitch(appClient)
		if appClient.OsType == int(libc.DeviceTypeEnum.IOS) && jlSwitch.Ios ||
			(appClient.OsType == int(libc.DeviceTypeEnum.Android) ||
				appClient.OsType == int(libc.DeviceTypeEnum.Harmony)) && jlSwitch.Android {
			switchInfo.ButtonTextJL = buttonTextJL
		}
	})
	wg.Wait()
	eData, err := json.Marshal(switchInfo)
	if err != nil {
		logger.Error(err)
		t.Result(errorcode.SystemError)
		return ""
	}
	cfg := config.Get()
	encodeAes, err := crypto.NewAes(cfg.Service.PassWdAesKey, cfg.Service.PassWdAesKey).Encrypt(eData)
	if err != nil {
		logger.Error(err)
		t.Result(errorcode.SystemError)
		return ""
	}
	return encodeAes
}

func GetServiceTime(t *http.Context) {
	t.Result(map[string]interface{}{
		"local_time": time.Now().Unix(),
	})
}

type ObSkipNumConf struct {
	SkipButtonNum int `json:"skip_button_num"`
	StartTimeDay  int `json:"start_time_day"`
}

func ObSkipNumInfo() ObSkipNumConf {
	resp := ObSkipNumConf{}
	info := client.TbConfig.GetItemByKey(libc.ObSkipNumConfig)
	if info != nil {
		if err := json.Unmarshal([]byte(info.Value), &resp); err != nil {
			logger.Error(err.Error())
			return resp
		}
	}
	return resp
}

const (
	CsEnterAPP = iota + 1
	CsEnterH5
)

type EnterAbtRes struct {
	EnterType int32  `json:"enter_type"`
	EnterURL  string `json:"enter_url"`
}

func CustomerServiceEnter(t *http.Context) {
	cfg := config.Get()
	envStr := ""
	if cfg.Service.Env != microservice.Product {
		envStr = "qa"
	}
	res := EnterAbtRes{
		EnterType: CsEnterH5,
		EnterURL: fmt.Sprintf("https://h5%s.dailyworkout.cn/qyCustomerServiceClient/index.html?need_bar=1",
			envStr),
	}
	t.Result(res)
}
