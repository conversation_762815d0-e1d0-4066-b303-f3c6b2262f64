package operate

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libi "gitlab.dailyyoga.com.cn/server/children/library/index"
	srvindex "gitlab.dailyyoga.com.cn/server/children/service/index"
	"gitlab.dailyyoga.com.cn/server/children/service/product"
	"gitlab.dailyyoga.com.cn/server/children/service/user"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type index struct {
}

var FitIndex index

type ContainerListRsp struct {
	List          []*srvindex.ContainerItem `json:"list"`
	UserGroupID   int64                     `json:"user_group_id"`
	FreeCourseIDs []int64                   `json:"free_course_ids"`
}

type ResourceItem struct {
	ContainerType int   `json:"container_type"`
	ContainerID   int64 `json:"container_id"`
}

// nolint
func (i *index) ContainerList(t *http.Context) {
	sid := t.GetRequestStringD("sid", "")
	resourceTab := t.GetRequestInt64D("resource_tab", library.Yes)
	uid := user.SrvLogin.SIDToUID(sid)
	res := &ContainerListRsp{
		List:          make([]*srvindex.ContainerItem, 0),
		FreeCourseIDs: make([]int64, 0),
	}
	appClient := util.GetClientInfo(t)
	userGroupID := int64(0)
	containerList, userGroupID := srvindex.SrvContainer.GetUserGroupContainer(uid, resourceTab)
	res.UserGroupID = userGroupID
	if len(containerList) == 0 {
		t.Result(res)
		return
	}
	obChannel := product.GetObUserChannel(appClient, uid, true)
	formatContainerListResp := srvindex.SrvContainer.FormatContainerList(containerList, uid, appClient, obChannel, resourceTab)
	res.List = formatContainerListResp.List
	res.FreeCourseIDs = formatContainerListResp.FreeCourseIDs

	if resourceTab == libi.ResourceTypePractice &&
		util.UVersion.GtVersion(appClient, 10300, 10300) {
		// 金刚区
		item := &srvindex.ContainerItem{
			ContainerType: int64(libi.ContainerTypeEnum.CourseClassification),
			Title:         libi.ContainerTypeDesc[libi.ContainerTypeEnum.CourseClassification],
			ResourceList:  srvindex.GetPracticeJingangDistrict(),
		}
		res.List = append([]*srvindex.ContainerItem{item}, res.List...)
	}
	t.Result(res)
}
