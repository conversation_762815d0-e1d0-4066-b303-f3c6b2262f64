package ocpx

import (
	"encoding/json"
	"strconv"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/client"
	"gitlab.dailyyoga.com.cn/server/children/service/app"
	"gitlab.dailyyoga.com.cn/server/children/service/ocpd"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type huaweiOCPD struct{}

var HuaweiOCPD huaweiOCPD

// huaweiData 华为归因结构
type huaweiData struct {
	HuaweiChannel  string `json:"channel"`
	HuaweiCallBack string `json:"callback"`
	HuaweiTaskID   string `json:"taskid"`
}
type HuaweiResponse struct {
	Status bool `json:"status"`
}

func (h *huaweiOCPD) OcpdPush(t *http.Context) {
	params := &ocpd.PushParams{}
	t.ParseRequestStruct(params)
	response := &HuaweiResponse{
		Status: false,
	}
	huawei := huaweiData{}
	headInfo := util.GetClientInfo(t)
	if strconv.Itoa(headInfo.Channel) != client.ChannelHuawei {
		t.Result(response)
		return
	}
	if params.Data == "" {
		logger.Info("客户端未传递data")
		t.Result(response)
		return
	}
	err := json.Unmarshal([]byte(params.Data), &huawei)
	if err != nil {
		logger.Info("data解析失败", err)
		t.Result(response)
		return
	}
	if params.OaID == "" {
		logger.Info("客户端未传递oaid")
		t.Result(response)
		return
	}
	uid, _ := strconv.ParseInt(params.DistinctID, 10, 64)
	pushInfo := &app.HuaweiPushInfo{
		HuaweiChannel:  huawei.HuaweiChannel,
		HuaweiCallBack: huawei.HuaweiCallBack,
		HuaweiTaskID:   huawei.HuaweiTaskID,
		CustomerID:     params.OaID,
		ActionType:     library.AppActive,
		Channel:        int64(headInfo.Channel),
		UID:            uid,
	}
	safelygo.GoSafelyByTraceID(func() {
		if _, err := app.SrvHuaweiOcpx.Huawei(pushInfo); err != nil {
			logger.Info(err.Error())
		}
	})
	response.Status = true
	t.Result(response)
}
