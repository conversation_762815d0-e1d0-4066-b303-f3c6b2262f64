package practice

import (
	"math"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	dbpractice "gitlab.dailyyoga.com.cn/server/children/databases/practice"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/course"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	srvc "gitlab.dailyyoga.com.cn/server/children/service/course"
	srvp "gitlab.dailyyoga.com.cn/server/children/service/practice"
	"gitlab.dailyyoga.com.cn/server/children/service/user"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

// 练习柱状图看板
func DataView(t *http.Context) {
	dateType := t.GetRequestIntD("date_type", 0)
	sid := t.GetRequestStringD("sid", "")
	uid := user.SrvLogin.SIDToUID(sid)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	isApp := t.GetReuqestBoolD("is_app", false)
	var res []*srvp.IntervalViewItem
	switch course.DateTypeInt(dateType) {
	case course.DateTypeEnum.Day:
		res = srvp.SrvView.GetPracticeDate(uid, isApp)
	case course.DateTypeEnum.Week:
		res = srvp.SrvView.GetPracticeWeek(uid)
	case course.DateTypeEnum.Month:
		res = srvp.SrvView.GetPracticeMonth(uid)
	case course.DateTypeEnum.Year:
		res = srvp.SrvView.GetPracticeYear(uid, 0)
	default:
		t.Result(errorcode.InvalidParams)
		return
	}
	if len(res) == 0 {
		res = make([]*srvp.IntervalViewItem, 0)
	}
	maxMinutes := 0
	for _, v := range res {
		if v.PracticeMinutes > int64(maxMinutes) {
			maxMinutes = int(v.PracticeMinutes)
		}
	}
	t.Result(map[string]interface{}{
		"list":                 res,
		"max_practice_minutes": maxMinutes,
	})
}

// nolint
func DataList(t *http.Context) {
	sid := t.GetRequestStringD("sid", "")
	uid := user.SrvLogin.SIDToUID(sid)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	startTime := t.GetRequestInt64D("start_time", 0)
	endTime := t.GetRequestInt64D("end_time", 0)
	page := t.GetRequestInt64D("page", 1)
	pageSize := t.GetRequestInt64D("page_size", 20)
	list := dbpractice.TbPracticeLog.GetLogByRange(uid, startTime, endTime, page, pageSize)
	res := make([]*srvp.PlayLog, 0)
	for k := range list {
		item := srvp.SrvLog.FormatPracticeLog(list[k])
		if item != nil {
			res = append(res, item)
		}
	}
	t.Result(map[string]interface{}{
		"list": res,
	})
}

type DataStatRsp struct {
	TodayCalorie         int64 `json:"today_calorie"`
	TodayPracticeMinutes int64 `json:"today_practice_minutes"`
	TotalPracticeMinutes int64 `json:"total_practice_minutes"`
	TotalPracticeDays    int64 `json:"total_practice_days"`
	LastPracticeDays     int64 `json:"last_practice_days"`
}

func DataStat(t *http.Context) {
	sid := t.GetRequestStringD("sid", "")
	uid := user.SrvLogin.SIDToUID(sid)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	rsp := &DataStatRsp{}
	colectItem := dbpractice.TbUserCollect.FindByUID(uid)
	if colectItem != nil {
		rsp.TotalPracticeDays = colectItem.TotalPracticeDay
		rsp.TotalPracticeMinutes = int64(math.Floor(float64(colectItem.TotalPlayTime) / util.SecondsPerMinute))
		rsp.LastPracticeDays = colectItem.LastContinuePracticeDay
	}
	todayItem := dbpractice.TbPlayDay.GetItem(uid, time.Now().Format("20060102"))
	if todayItem != nil {
		rsp.TodayCalorie = int64(todayItem.Calorie)
		rsp.TodayPracticeMinutes = int64(math.Floor(float64(todayItem.PlayTime) / util.SecondsPerMinute))
	}
	t.Result(rsp)
}

type FavoriteCourseItem struct {
	ProgramID        int64  `json:"program_id"`
	SessionID        int64  `json:"course_id"`
	Title            string `json:"title"`
	HoriImg          string `json:"hori_img"`
	SessionDescLabel string `json:"session_desc_label"`
}

func FavoriteCourse(t *http.Context) {
	sid := t.GetRequestStringD("sid", "")
	uid := user.SrvLogin.SIDToUID(sid)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	appClient := util.GetClientInfo(t)
	startTime := t.GetRequestInt64D("start_time", 0)
	endTime := t.GetRequestInt64D("end_time", 0)
	list := dbpractice.TbPracticeLog.GetFavoriteCourse(uid, startTime, endTime)
	var totalPracticeCount int64
	var courseInfo *srvc.ItemCourse
	for k := range list {
		v := dbpractice.TbPracticeLog.GetFavoriteCourseItem(uid, list[k].CourseID, startTime, endTime)
		if v == nil {
			continue
		}
		srvCourse := srvc.SrvOptionCourse{}
		courseInfo = srvCourse.GetCourseDetail(uid, list[k].CourseID, &course.OptionCourse{
			IsOnline: library.Yes,
		}, appClient, srvc.WithLabel())
		totalPracticeCount = dbpractice.TbPracticeLog.GetPracticeCount(uid, list[k].CourseID)
		break
	}
	t.Result(map[string]interface{}{
		"total_practice_count": totalPracticeCount,
		"course_info":          courseInfo,
	})
}

func TestPractice(t *http.Context) {
	params := &user.PracticeReportParam{}
	t.ParseRequestStruct(params)
	if params.UID == 0 || params.PlayTime == 0 ||
		params.PracticeStartTime == 0 || params.CourseID == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	err := user.SrvUser.PracticeReport(params)
	if err != nil {
		logger.Error(err)
		t.Result(errorcode.SystemError)
		return
	}
	t.Result(library.EmptyResponse{})
}

type TCStatResponse struct {
	LastPracticeDays int64       `json:"last_practice_days"`
	WeekList         []*WeekList `json:"week_list"`
}

type WeekList struct {
	WeekDay   string `json:"week_day"`
	DateDay   string `json:"date_day"`
	Timestamp int64  `json:"timestamp"`
	Status    int    `json:"status"`
}

// TrainingCompletionStat 此接口练习必调用
func TrainingCompletionStat(t *http.Context) {
	sid := t.GetRequestStringD("sid", "")
	uid := user.SrvLogin.SIDToUID(sid)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	rsp := &TCStatResponse{
		WeekList:         make([]*WeekList, 0),
		LastPracticeDays: 1,
	}
	collectItem := dbpractice.TbUserCollect.FindByUID(uid)
	if collectItem != nil {
		rsp.LastPracticeDays = collectItem.LastContinuePracticeDay
	}
	// 调用接口的时机问题 可能会导致今天练习还未加上
	todayDate := time.Now().Format("20060102")
	todayPractice := dbpractice.TbPlayDay.GetItem(uid, todayDate)
	if todayPractice == nil {
		yesterdayDate := time.Now().AddDate(0, 0, -1).Format("20060102")
		yesterdayPractice := dbpractice.TbPlayDay.GetItem(uid, yesterdayDate)
		if yesterdayPractice != nil {
			rsp.LastPracticeDays++
		}
	}
	todayStart := util.FormatStartTime(time.Now().Unix())
	calendar := util.TrainingCompletionCalendar()
	practiceDay := make([]string, 0)
	for c := range calendar {
		practiceDay = append(practiceDay, time.Unix(calendar[c].Timestamp, 0).Format("20060102"))
	}
	practiceDayMap := make(map[string]int64)
	practiceDayList := dbpractice.TbPlayDay.GetListByRange(uid, practiceDay)
	for p := range practiceDayList {
		practiceDayMap[practiceDayList[p].DateIndex] = 1
	}
	for c := range calendar {
		status := library.No
		if _, ok := practiceDayMap[time.Unix(calendar[c].Timestamp, 0).Format("20060102")]; ok {
			status = library.Yes
		}
		// 调用接口的时机问题 可能会导致今天练习还未加上
		if calendar[c].Timestamp == todayStart {
			if status != library.Yes {
				rsp.LastPracticeDays++
			}
			calendar[c].WeekDay = "今日"
			status = library.Yes
		}
		rsp.WeekList = append(rsp.WeekList, &WeekList{
			WeekDay:   calendar[c].WeekDay,
			DateDay:   strconv.Itoa(calendar[c].Day),
			Timestamp: calendar[c].Timestamp,
			Status:    status,
		})
	}
	t.Result(rsp)
}
