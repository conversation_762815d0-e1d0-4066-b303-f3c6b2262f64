package collect

import (
	"fmt"
	"strings"
	"sync"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/server/children/library"
	collectLib "gitlab.dailyyoga.com.cn/server/children/library/collect"
	libcourse "gitlab.dailyyoga.com.cn/server/children/library/course"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	collectSrv "gitlab.dailyyoga.com.cn/server/children/service/collect"
	"gitlab.dailyyoga.com.cn/server/children/service/course"
	"gitlab.dailyyoga.com.cn/server/children/service/user"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type Request struct {
	SID          string `json:"sid"`
	ResourceType int    `json:"resource_type"`
	ResourceIds  string `json:"resource_ids"`
	PlanIds      string `json:"plan_ids"`
}

func validate(t *http.Context) (*Param, errorcode.ErrorCode) {
	req := &Request{}
	t.ParseRequestStruct(req)
	uid := user.SrvLogin.SIDToUID(req.SID)
	if uid == 0 {
		return nil, errorcode.HasNotLogin
	}
	resourceIds := strings.Split(req.ResourceIds, ",")
	planIds := strings.Split(req.PlanIds, ",")
	return &Param{
		uid:          uid,
		resourceType: req.ResourceType,
		resourceIds:  resourceIds,
		planIds:      planIds,
	}, errorcode.Success
}

type Param struct {
	uid          int64
	resourceType int
	resourceIds  []string
	planIds      []string
}

// AddCollect 收藏
func AddCollect(t *http.Context) {
	param, errorCode := validate(t)
	if errorCode != errorcode.Success {
		t.Result(errorCode)
		return
	}
	service := collectSrv.Relation{
		UID:          param.uid,
		ResourceType: param.resourceType,
		ResourceIds:  param.resourceIds,
	}
	errorCode = service.AddCollect()
	if errorCode != errorcode.Success {
		t.Result(errorCode)
		return
	}
	t.Result(&library.EmptyResponse{})
}

// DelCollect 取消收藏
func DelCollect(t *http.Context) {
	param, errorCode := validate(t)
	if errorCode != errorcode.Success {
		t.Result(errorCode)
		return
	}
	if len(param.resourceIds) != 0 {
		service := collectSrv.Relation{
			UID:          param.uid,
			ResourceType: int(collectLib.ResourceTypeEnum.Course),
			ResourceIds:  param.resourceIds,
		}
		errorCode = service.DelCollect()
	}
	if errorCode != errorcode.Success {
		t.Result(errorCode)
		return
	}
	t.Result(&library.EmptyResponse{})
}

type CourseListRsp struct {
	List []*course.ItemCourse `json:"list,omitempty"`
}

// nolint
func UserCollectNew(t *http.Context) {
	sid := t.GetRequestStringD("sid", "")
	appClient := util.GetClientInfo(t)
	uid := user.SrvLogin.SIDToUID(sid)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	service := collectSrv.Relation{
		UID:      uid,
		PageNum:  t.GetRequestIntD("page", 0),
		PageSize: t.GetRequestIntD("page_size", 0),
	}
	collectIDArr := service.GetUserCollectList()
	srvCourse := &course.SrvOptionCourse{}
	courseIDArr := make([]int64, 0)
	for _, v := range collectIDArr {
		switch collectLib.ResourceTypeInt(v.ResourceType) {
		case collectLib.ResourceTypeEnum.Course:
			courseIDArr = append(courseIDArr, v.ResourceID)
		}
	}
	courseMap := make(map[string]*course.ItemCourse)
	var wgWait sync.WaitGroup
	wgWait.Add(1)
	safelygo.GoSafelyByTraceID(func() {
		defer wgWait.Done()
		courseList := srvCourse.BatchGetCourseDetail(uid, courseIDArr, appClient, &libcourse.OptionCourse{
			IsOnline: library.Yes,
		})
		for _, v := range courseList {
			courseMap[fmt.Sprintf("%d_%d", collectLib.ResourceTypeEnum.Course, v.ID)] = v
		}
	})
	wgWait.Wait()
	itemCollectList := make([]*ItemCollect, 0)
	for _, v := range collectIDArr {
		switch collectLib.ResourceTypeInt(v.ResourceType) {
		case collectLib.ResourceTypeEnum.Course:
			courseInfo := courseMap[fmt.Sprintf("%d_%d", collectLib.ResourceTypeEnum.Course, v.ResourceID)]
			if courseInfo == nil {
				continue
			}
			itemCollectList = append(itemCollectList, &ItemCollect{
				ResourceType: int(collectLib.ResourceTypeEnum.Course),
				Session:      courseInfo,
			})
		}
	}
	t.Result(&CourseListNewRsp{
		List: itemCollectList,
	})
}

type ItemCollect struct {
	ResourceType int                `json:"resource_type"`
	Session      *course.ItemCourse `json:"session,omitempty"`
}

type CourseListNewRsp struct {
	List []*ItemCollect `json:"list,omitempty"`
}
