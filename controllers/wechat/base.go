package wechat

import (
	"context"
	"crypto/sha1" // #nosec
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"net/url"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libcache "gitlab.dailyyoga.com.cn/server/children/library/cache"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
	"gitlab.dailyyoga.com.cn/server/go-artifact/requests"
)

type dfWechat struct {
}

var DfWechat dfWechat

type AccessTokenResponse struct {
	ErrCode     int64  `json:"errcode"`
	ErrMsg      string `json:"errmsg"`
	AccessToken string `json:"access_token"`
	ExpiresIn   int64  `json:"expires_in"`
}

// GetAccessToken 从redis中获取Token，如果取不到，新拿并且刷新缓存
// nolint
func (dw *dfWechat) GetAccessToken(scene string, retry int, needDel bool) (string, error) {
	rdc := cache.GetCRedis().GetClient()
	rdc.Del(context.Background(), libcache.WxAccessToken).Val()
	token := rdc.Get(context.Background(), libcache.WxAccessToken).Val()
	// 缓存读取，如果读不到新取值
	if token != "" {
		logger.Infof("微信调用，token缓存，%s", token)
		return token, nil
	}
	newToken, err := dw.getAccessTokenRequest(scene)
	// 最大重试3次
	var maxRetry int = 3
	if retry > maxRetry {
		retry = maxRetry
	}
	if err != nil {
		retry--
		if retry > 0 {
			newToken, err = dw.getAccessTokenRequest(scene)
			if err != nil {
				return dw.GetAccessToken(scene, retry, true)
			}
			if _, err := rdc.SetNX(context.Background(), libcache.WxAccessToken,
				newToken, library.WxTokenCacheSecond*time.Second).Result(); err != nil {
				return dw.GetAccessToken(scene, retry, true)
			}
		}
	}
	if _, err := rdc.SetNX(context.Background(), libcache.WxAccessToken,
		newToken, library.WxTokenCacheSecond*time.Second).Result(); err != nil {
		return "", err
	}
	return newToken, nil
}

func (dw *dfWechat) getAccessTokenRequest(scene string) (string, error) {
	client := &requests.Client{}
	client.UseHTTPS()
	client.SetBaseDomain(library.WxAPIDomain)
	values := make(url.Values)
	values.Add("grant_type", "client_credential")
	values.Add("appid", dw.GetAppid(scene))
	values.Add("secret", dw.GetSecret(scene))
	resp, err := client.GetByParams(library.WxGetAccessTokenAPI, values)
	if err != nil {
		logger.Error("获取access_token报错", err)
		return "", err
	}
	if !resp.IsOK() || resp.Body == "" {
		logger.Error("获取access_token出错", resp, values)
		return "", errors.New("微信服务异常")
	}

	response := AccessTokenResponse{}
	if err := json.Unmarshal([]byte(resp.Body), &response); err != nil {
		logger.Errorf("解析微信access_token数据失败，请求：%v，返回：%v", values, response)
		return "", errors.New("微信服务异常，解析Json失败")
	}

	if response.ErrCode != 0 {
		logger.Errorf("获取access_token出错，微信错误code:%d，信息:%s", response.ErrCode, response.ErrMsg)
		return "", errors.New("微信服务调用异常")
	}

	return response.AccessToken, nil
}

func (dw *dfWechat) GetAppid(scene string) string {
	if params, ok := library.WxAppParamsScene[scene]; ok {
		return params.AppID
	}
	return ""
}

func (dw *dfWechat) GetSecret(scene string) string {
	if params, ok := library.WxAppParamsScene[scene]; ok {
		return params.Secret
	}
	return ""
}

func (dw *dfWechat) GetJsTicket(scene string) (string, error) {
	rdc := cache.GetCRedis().GetClient()
	rdc.Del(context.Background(), libcache.WxJsTicket).Val()
	ticket := rdc.Get(context.Background(), libcache.WxJsTicket).Val()
	if ticket != "" {
		logger.Infof("微信调用，JsTk，缓存读取，%s", ticket)
		return ticket, nil
	}
	newTicket, err := dw.GetJsTicketRequest(scene)
	if err != nil {
		return "", err
	}
	if _, err := rdc.SetNX(context.Background(), libcache.WxJsTicket,
		newTicket, library.WxTokenCacheSecond*time.Second).Result(); err != nil {
		return "", err
	}
	return newTicket, nil
}

type JsTicketResponse struct {
	ErrCode   int64  `json:"errcode"`
	ErrMsg    string `json:"errmsg"`
	Ticket    string `json:"ticket"`
	ExpiresIn int64  `json:"expires_in"`
}

func (dw *dfWechat) GetJsTicketRequest(scene string) (string, error) {
	token, err := dw.GetAccessToken(scene, 0, true)
	if err != nil || token == "" {
		logger.Errorf("access_token获取失败，ERR: %s", err)
		return "", err
	}

	client := &requests.Client{}
	client.UseHTTPS()
	client.SetBaseDomain(library.WxAPIDomain)
	values := make(url.Values)
	values.Add("access_token", token)
	values.Add("type", "jsapi")
	resp, err := client.GetByParams(library.WxGetJsTicketAPI, values)
	if err != nil {
		logger.Error("获取js_ticket报错", err)
		return "", err
	}
	if !resp.IsOK() || resp.Body == "" {
		logger.Error("获取js_ticket出错", resp, values)
		return "", errors.New("微信获取Ticket服务异常")
	}

	response := JsTicketResponse{}
	if err := json.Unmarshal([]byte(resp.Body), &response); err != nil {
		logger.Errorf("解析微信js_ticket数据失败，请求：%v，返回：%v", values, response)
		return "", errors.New("微信服务调用异常")
	}

	if response.ErrCode != 0 {
		logger.Errorf("获取js_ticket出错，微信错误code:%d，信息:%s", response.ErrCode, response.ErrMsg)
		return "", errors.New("微信服务调用异常")
	}

	return response.Ticket, nil
}

type BuildJsTicketParamsResp struct {
	AppID     string `json:"app_id"`
	NonceStr  string `json:"nonce_str"`
	Timestamp int    `json:"timestamp"`
	Signature string `json:"signature"`
}

func (dw *dfWechat) BuildJsTicketParams(t *http.Context) {
	scene := "Default"
	jsTicket, err := dw.GetJsTicket(scene)
	if err != nil {
		t.Result(errorcode.SystemError)
		return
	}
	postURL := t.GetRequestStringD("url", "")
	signInfo, err := dw.makeJsTicketSign(jsTicket, postURL)
	if err != nil {
		t.Result(errorcode.SystemError)
		return
	}
	t.Result(&BuildJsTicketParamsResp{
		AppID:     dw.GetAppid(scene),
		NonceStr:  signInfo.NonceStr,
		Timestamp: signInfo.Timestamp,
		Signature: signInfo.Signature,
	})
}

type JsTicketSign struct {
	NonceStr  string
	Timestamp int
	Signature string
}

func (dw *dfWechat) makeJsTicketSign(jsTicket, postURL string) (*JsTicketSign, error) {
	dURL, _ := base64.StdEncoding.DecodeString(postURL)
	nonceStr := util.GetNonceStr()
	timestamp := int(time.Now().Unix())
	signStr := "jsapi_ticket=" + jsTicket + "&noncestr=" + nonceStr + "&timestamp=" +
		strconv.Itoa(timestamp) + "&url=" + string(dURL)
	/* #nosec */
	o := sha1.New()
	_, err := o.Write([]byte(signStr))
	if err != nil {
		return nil, err
	}
	Signature := hex.EncodeToString(o.Sum(nil))
	logger.Infof("微信调用，JsTk，%s, %s, %d, %s", jsTicket, string(dURL), timestamp, nonceStr)
	return &JsTicketSign{
		NonceStr:  nonceStr,
		Timestamp: timestamp,
		Signature: Signature,
	}, nil
}
