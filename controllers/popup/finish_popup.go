package popup

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/server/children/library"
	srvp "gitlab.dailyyoga.com.cn/server/children/service/product"
	"gitlab.dailyyoga.com.cn/server/children/service/user"
	srvutil "gitlab.dailyyoga.com.cn/server/children/service/util"
)

type finishPopup struct {
}

var AFinishPopup finishPopup

func (f *finishPopup) PraiseFinish(t *http.Context) {
	appClient := srvutil.GetClientInfo(t)
	sid := t.GetRequestStringD("sid", "")
	uid := user.SrvLogin.SIDToUID(sid)
	obChannel := srvp.GetObUserChannel(appClient, uid, true)
	resp := user.StorePraiseFinish(uid, appClient, obChannel)
	if resp == nil {
		t.Result(library.EmptyResponse{})
		return
	}
	t.Result(resp)
}
