package pay

import (
	"encoding/json"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/server/children/databases/iap"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	srvorder "gitlab.dailyyoga.com.cn/server/children/service/order"
	"gitlab.dailyyoga.com.cn/server/children/service/user"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type OrderIAPRsp struct {
	LogID int64 `json:"log_id"`
}

func OrderIAP(t *http.Context) {
	params := &srvorder.IAPClientPayReceipt{}
	t.ParseRequestStruct(params)
	logger.Info("IOS 完成订单上报", *params)
	uid := user.SrvLogin.SIDToUID(params.SID)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	res := &OrderIAPRsp{}
	if params.Receipt == "" {
		logger.Error("IOS订单上报Receipt为空", *params)
	}
	if params.TranID == "" || params.Receipt == "" {
		t.Result(errorcode.InvalidParams)
		return
	}
	requestByte, err := json.Marshal(params)
	if err != nil {
		logger.Error(err)
		t.Result(errorcode.SystemError)
		return
	}
	appClient := util.GetClientInfo(t)
	receipt := &iap.IOSReceiptLog{
		UID:           params.UID,
		OrderID:       params.OrderID,
		TransactionID: params.TranID,
		Request:       string(requestByte),
		IsDeal:        library.No,
	}
	if err := receipt.Save(); err != nil {
		logger.Error(err)
		t.Result(errorcode.SystemError)
		return
	}
	safelygo.GoSafelyByTraceID(func() {
		srvorder.DealIAPReceiptLog(receipt, appClient)
	})
	res.LogID = receipt.ID
	t.Result(res)
}

type CheckCompleteIAPRsp struct {
	LogID      int64 `json:"log_id"`
	DealStatus int   `json:"deal_status"`
}

const (
	DealStatusNot = iota
	DealStatusSuccessOrder
	DealStatusRenew
)

// CheckCompleteIAP 检查iap上报完成订单是否已处理
func CheckCompleteIAP(t *http.Context) {
	logID := t.GetRequestInt64D("log_id", 0)
	orderID := t.GetRequestStringD("order_id", "")
	if logID == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	receipt := iap.TbIOSReceipt.GetItemByID(logID)
	res := CheckCompleteIAPRsp{
		LogID:      receipt.ID,
		DealStatus: DealStatusNot,
	}
	appClient := util.GetClientInfo(t)
	if receipt.IsDeal != library.Yes {
		safelygo.GoSafelyByTraceID(func() {
			srvorder.DealIAPReceiptLog(receipt, appClient)
		})
		t.Result(res)
		return
	}
	pResult := srvorder.IAPOrderAnalysisRsp{}
	err := json.Unmarshal([]byte(receipt.ProcessResult), &pResult)
	if err != nil {
		logger.Error(err)
		t.Result(res)
		return
	}
	if orderID != "" && len(pResult.CompleteOrderList) > 0 {
		for _, v := range pResult.CompleteOrderList {
			if v != "" && orderID == v {
				res.DealStatus = DealStatusSuccessOrder
			}
		}
	}
	if orderID == "" && len(pResult.CompleteOrderList) > 0 {
		res.DealStatus = DealStatusSuccessOrder
	}
	// 处理成功 但是未生成新的订单，且客户端请求的原始交易可以在苹果交易中存在有效记录，则是续期，不生成新的订单
	if res.DealStatus != library.Yes &&
		pResult.CurrentOrder != nil && pResult.CurrentOrder.TransactionID != "" {
		res.DealStatus = DealStatusRenew
	}
	t.Result(res)
}
