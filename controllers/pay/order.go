package pay

import (
	"errors"
	"fmt"
	"strconv"
	"strings"

	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/config"
	dborder "gitlab.dailyyoga.com.cn/server/children/databases/order"
	dbproduct "gitlab.dailyyoga.com.cn/server/children/databases/product"
	dbuser "gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libclient "gitlab.dailyyoga.com.cn/server/children/library/client"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	"gitlab.dailyyoga.com.cn/server/children/library/pay"
	libp "gitlab.dailyyoga.com.cn/server/children/library/product"
	libuser "gitlab.dailyyoga.com.cn/server/children/library/user"
	"gitlab.dailyyoga.com.cn/server/children/service/audit"
	srvorder "gitlab.dailyyoga.com.cn/server/children/service/order"
	srvpaymid "gitlab.dailyyoga.com.cn/server/children/service/paymid"
	srvuser "gitlab.dailyyoga.com.cn/server/children/service/user"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type order struct{}

var DfOrder order

func (*order) PreOrder(t *http.Context) {
	req := &srvorder.PreOrderRequest{}
	t.ParseRequestStruct(req)
	uid := srvuser.SrvLogin.SIDToUID(req.SID)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	appClient := util.GetClientInfo(t)
	if appClient.IsGray && req.PayType == pay.PayTypeApple {
		t.Result(errorcode.IosIsGray)
		return
	}
	req.UID = uid
	t.ParseRequestStruct(req)
	// VIVO渠道自然量 审核状态或者线上开关开  必须用户主动登录
	if strconv.Itoa(appClient.Channel) == libclient.ChannelVIVO {
		accItem := dbuser.TbAccount.GetUserByID(uid)
		if accItem != nil && accItem.LoginType == libuser.LoginTypeShenCe {
			auditFunc := audit.GetFuncSwitchByKey(appClient, libclient.OnlineConfLoginBuy, audit.GetChannel(appClient, uid))
			if auditFunc.Audit || auditFunc.OnlineSwitch {
				t.Result(errorcode.HasNotLogin)
				return
			}
		}
	}

	product := dbproduct.TbWebProduct.GetItemByID(req.ProductID)
	if product == nil {
		t.Result(errorcode.DBError)
		return
	}
	payOrder := srvorder.PreOrder(req, product, appClient)
	if payOrder == nil {
		t.Result(errorcode.DBError)
		return
	}
	res := srvpaymid.GoToPay(payOrder, req.PayType, appClient)
	if res == nil {
		t.Result(errorcode.SystemError)
		return
	}
	logger.Info("用户预下单返回值", uid, *res)
	t.Result(res)
}

type OrderInfo struct {
	OrderID              string            `json:"order_id"`
	OrderStatus          int               `json:"order_status"`
	OrderTotalAmount     string            `json:"order_total_amount"`
	PayTime              int64             `json:"pay_time"`
	PayType              int               `json:"pay_type"`
	PayTypeInfo          *pay.TypeInfoItem `json:"pay_type_info,omitempty"`
	ProductInfo          OrderProduct      `json:"product_info,omitempty"`
	VipEndTime           int64             `json:"vip_end_time"`
	OrderSubscribeStatus int               `json:"order_subscribe_status"`
	PaymentOrderType     int               `json:"payment_order_type"`
	HideVipTime          bool              `json:"hide_vip_time"`
	DisplayOfferCard     int               `json:"display_offer_card"`
}

type OrderProduct struct {
	Name          string `json:"name"`
	OriginPrice   string `json:"origin_price"`
	ProductType   int    `json:"product_type"`
	MonthlyAmount string `json:"monthly_amount"`
	VipType       int    `json:"vip_type"`
}

func (*order) Info(t *http.Context) {
	sid := t.GetRequestStringD("sid", "")
	uid := srvuser.SrvLogin.SIDToUID(sid)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	appClient := util.GetClientInfo(t)
	orderID := t.GetRequestStringD("order_id", "")
	if orderID == "" {
		t.Result(errorcode.InvalidParams)
		return
	}
	info := dborder.TbWebOrder.GetItemByOrderID(orderID)
	if info == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	hideVipTime := false
	res := &OrderInfo{
		OrderID:          orderID,
		OrderStatus:      int(info.OrderStatus),
		OrderTotalAmount: strconv.FormatFloat(info.OrderAmount, 'f', 2, 64),
		PayTime:          info.PayTime,
		PayType:          info.PayType,
		PaymentOrderType: info.PaymentOrderType,
		HideVipTime:      hideVipTime,
	}
	if p, ok := pay.TypeInfoMap[res.PayType]; ok {
		res.PayTypeInfo = p
	}
	account := dbuser.TbAccount.GetUserByID(info.UID)
	if account == nil {
		t.Result(errorcode.SystemError)
		return
	}
	res, err := fmtOrderInfo(info, appClient, account, res)
	if err != nil {
		t.Result(errorcode.SystemError)
		return
	}
	t.Result(res)
}

func GetPaymentOrderType(vipTypeArr []int) int {
	if library.IntInArray(libp.ProductVipTypeVIP, vipTypeArr) {
		return pay.PaymentOrderTypeVip
	}
	return pay.PaymentOrderTypeVip
}

// nolint
func fmtOrderInfo(info *dborder.WebOrder, appClient *library.AppClient, account *dbuser.Account,
	res *OrderInfo) (*OrderInfo, error) {
	res.VipEndTime = account.EndTime
	// 客户端不识别 PaymentOrderType=4 1.9版本新加 后续可以删除次逻辑
	if info.PaymentOrderType == pay.PaymentOrderTypeVoiceStrong {
		res.PaymentOrderType = pay.PaymentOrderTypeVoice
	}
	product := dbproduct.TbWebProduct.GetItemByID(info.ProductID)
	if product == nil {
		return nil, errors.New("商品不存在")
	}
	res.OrderSubscribeStatus = product.IsSubscribe
	monthPrice := getMonthPrice(info.OrderAmount, product)
	res.DisplayOfferCard = library.No
	if product.ProductType == libp.ProcuctType12Month && product.IsSubscribe == library.Yes {
		res.DisplayOfferCard = library.Yes
		if product.OfferType == libp.ConstOfferTypeTrial ||
			product.OfferType == libp.ConstOfferTypeTrialFirstBuy {
			// 如果买过试用或者首购
			offerHistory := dbuser.TbWPSubU.CheckOfferTypeHistory(account.ID, product.ID, info.OrderID, product.OfferType)
			if len(offerHistory) == 0 {
				res.DisplayOfferCard = library.No
			}
		}
	}
	productName := product.Name
	substr := "iap"
	if strings.Contains(strings.ToLower(product.Name), substr) {
		productName = strings.Replace(strings.ToLower(product.Name), substr, "", -1)
	}
	res.ProductInfo = OrderProduct{
		Name:          productName,
		OriginPrice:   fmt.Sprintf("%.2f", product.OriginPrice),
		ProductType:   product.ProductType,
		MonthlyAmount: fmt.Sprintf("%.1f", monthPrice),
		VipType:       product.VipType,
	}
	return res, nil
}

type CheckAggrementRsp struct {
	ContractStatus int `json:"contract_status"`
}

// CheckAggrement 检查订单签约信息是否存在
func (*order) CheckAggrement(t *http.Context) {
	sid := t.GetRequestStringD("sid", "")
	uid := srvuser.SrvLogin.SIDToUID(sid)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	orderID := t.GetRequestStringD("order_id", "")
	// 默认协议存在
	res := &CheckAggrementRsp{}
	orderInfo := dborder.TbWebOrder.GetItemByOrderID(orderID)
	if orderInfo == nil {
		t.Result(errorcode.DBError)
		return
	}
	res.ContractStatus = int(srvorder.SrvOrderComplete.CheckOrderSubscribeStatus(orderInfo))
	logger.Info("查询签约状态返回结果", orderID, *res)
	t.Result(res)
}

func (*order) MockIosRefund(t *http.Context) {
	orderID := t.GetRequestStringD("order_id", "")
	refundStatus := t.GetRequestIntD("refund_status", 0)
	cfgEnv := config.Get().Service.Env
	if cfgEnv != microservice.Dev {
		t.Result("只有qa环境支持模拟ios退款成功")
		return
	}
	t.Result(srvorder.MockIosRefund(orderID, refundStatus))
}

// getMonthPrice 计算每个月的价格
func getMonthPrice(total float64, product *dbproduct.WebProduct) (monthPrice float64) {
	switch libp.DurationType(product.DurationType) {
	case libp.DurationTypeEnum.Month:
		monthPrice = total / float64(product.DurationValue)
	case libp.DurationTypeEnum.Year:
		monthPrice = total / (float64(product.DurationValue) * 12)
	default:
		return 0
	}
	monthPrice = float64(int(monthPrice*libp.Ten)) / libp.Ten
	return monthPrice
}

type orderItem struct {
	OrderID     string  `json:"order_id"`      // 订单ID
	PayAmount   float64 `json:"pay_amount"`    // 实付金额
	OrderAmount float64 `json:"order_amount"`  // 订单金额
	CreateTime  int64   `json:"create_time"`   // 下单时间
	PayTime     int64   `json:"pay_time"`      // 支付时间
	ProductName string  `json:"product_name"`  // 商品名称
	PayTypeDesc string  `json:"pay_type_desc"` // 支付方式描述
	ProductID   int64   `json:"product_id"`    // 会员产品ID和精练课堂ID
}

func (o *order) FormatOrder(order *dborder.WebOrder) *orderItem {
	res := &orderItem{
		OrderID:     order.OrderID,
		PayAmount:   order.OrderAmount,
		OrderAmount: order.OrderAmount,
		CreateTime:  order.CreateTime,
		PayTime:     order.PayTime,
		PayTypeDesc: pay.PayTypeDesc[order.PayType],
	}
	productID := order.ProductID
	productName := ""
	switch order.PaymentOrderType {
	case pay.PaymentOrderTypePlan:
	default:
		product := dbproduct.TbWebProduct.GetItemByID(productID)
		if product != nil {
			productName = product.Name
		}
	}
	res.ProductID = productID
	res.ProductName = productName
	return res
}

type OrderListResp struct {
	List []*orderItem `json:"list"`
}

func (o *order) OrderList(t *http.Context) {
	sid := t.GetRequestStringD("sid", "")
	uid := srvuser.SrvLogin.SIDToUID(sid)
	result := OrderListResp{
		List: make([]*orderItem, 0),
	}
	if uid == 0 {
		t.Result(result)
		return
	}
	orderList := dborder.TbWebOrder.GetListByUID(uid)
	for i := range orderList {
		result.List = append(result.List, o.FormatOrder(orderList[i]))
	}
	t.Result(result)
}
