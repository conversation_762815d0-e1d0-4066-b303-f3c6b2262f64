package pay

import (
	"encoding/json"
	"sync"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/server/children/databases/obpay"
	dbo "gitlab.dailyyoga.com.cn/server/children/databases/order"
	dbp "gitlab.dailyyoga.com.cn/server/children/databases/product"
	dbuser "gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libc "gitlab.dailyyoga.com.cn/server/children/library/client"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	"gitlab.dailyyoga.com.cn/server/children/library/pay"
	libproduct "gitlab.dailyyoga.com.cn/server/children/library/product"
	libu "gitlab.dailyyoga.com.cn/server/children/library/user"
	srvord "gitlab.dailyyoga.com.cn/server/children/service/order"
	"gitlab.dailyyoga.com.cn/server/children/service/popup"
	srvp "gitlab.dailyyoga.com.cn/server/children/service/product"
	"gitlab.dailyyoga.com.cn/server/children/service/rop"
	srvuser "gitlab.dailyyoga.com.cn/server/children/service/user"
	srvutil "gitlab.dailyyoga.com.cn/server/children/service/util"
)

type page struct{}

var DfPage page

type PagePayInfo struct {
	ID                int64              `json:"id"`
	HeadImg           *library.ImageInfo `json:"head_img,omitempty"`
	HeadVideo         *library.VideoInfo `json:"head_video,omitempty"`
	DescImg           *library.ImageInfo `json:"desc_img,omitempty"`
	RetainPopup       *popup.RetainPopup `json:"retain_popup,omitempty"`
	PayRetainPopup    *popup.RetainPopup `json:"pay_retain_popup,omitempty"`
	PayButtonImgPhone string             `json:"pay_button_img"`
	AbtInfo           *rop.SceneRop      `json:"abt_info,omitempty"`
	SkuStyle          int32              `json:"sku_style,omitempty"`
	SkuList           []*library.SkuItem `json:"sku_list,omitempty"`
}

type Barrage struct {
	Logo string `json:"logo"`
	Name string `json:"name"`
	Time string `json:"time"`
	Desc string `json:"desc"`
}

type Popup struct {
	Device *library.AppClient
	UID    int64
	Time   time.Time
	Logs   []string
}

const (
	PageTypeNormal = iota + 1
	PageTypePag
	PageTypePagHeightRatio
)

// nolint
func (*page) PageInfo(t *http.Context) {
	sid := t.GetRequestStringD("sid", "")
	uid := srvuser.SrvLogin.SIDToUID(sid)
	isInstallAp := t.GetReuqestBoolD("is_install_ap", true)
	paymentID := t.GetRequestInt64D("payment_id", 0)
	appClient := srvutil.GetClientInfo(t)
	if appClient.OsType == int(libc.DeviceTypeEnum.Harmony) {
		isInstallAp = true
	}
	var hasSub bool
	var obChannel *srvp.ObUserChannel
	var wgWait sync.WaitGroup
	wgWait.Add(2) //nolint
	safelygo.GoSafelyByTraceID(func() {
		defer wgWait.Done()
		hasSub = isUserHasMemberSub(uid)
	})
	safelygo.GoSafelyByTraceID(func() {
		defer wgWait.Done()
		obChannel = srvp.GetObUserChannel(appClient, uid, isInstallAp)
	})
	wgWait.Wait()
	js, _ := json.Marshal(obChannel)
	logger.Infof("PageInfoobChannel:%s", string(js))
	// 非合规
	if !obChannel.Compliance && !isInstallAp {
		// 未安装支付宝  非合规去1 返回微信支付
		paymentID = int64(libc.GetDefaultPageID(appClient.OsType))
		if appClient.OsType == int(libc.DeviceTypeEnum.IOS) && paymentID == 0 {
			paymentID = t.GetRequestInt64D("payment_id", 0)
		}
	} else if obChannel.Compliance {
		// 合规优先级最高
		if appClient.OsType == int(libc.DeviceTypeEnum.Android) || appClient.OsType == int(libc.DeviceTypeEnum.Harmony) {
			paymentID = pay.AndroidDefaultPayPageID
			// 安卓未安装支付宝/订阅 合规去66
			if hasSub || !isInstallAp {
				paymentID = int64(libc.GetDefaultPageID(appClient.OsType))
			}
		} else if appClient.OsType == int(libc.DeviceTypeEnum.IOS) {
			paymentID = pay.IOSDefaultPayPageID
			if hasSub {
				paymentID = int64(libc.GetDefaultPageID(appClient.OsType))
			}
		}
	}
	var (
		pageInfo *obpay.Page
		sRop     *rop.SceneRop
	)
	if appClient.OsType == int(libc.DeviceTypeEnum.IOS) && obChannel.AboutAudit.IsInAudit {
		paymentID = pay.DefaultAuditIosPayPageID
	}
	if (appClient.OsType == int(libc.DeviceTypeEnum.Android) ||
		appClient.OsType == int(libc.DeviceTypeEnum.Harmony)) && obChannel.AboutAudit.IsInAudit {
		paymentID = pay.DefaultAuditAndroidPayPageID
	}
	if paymentID == 0 {
		pageInfo, sRop = getPayPageConfig(uid, appClient, obChannel.Compliance)
	} else {
		pageInfo = obpay.TbPage.GetItemByID(uid, paymentID)
	}
	if pageInfo == nil {
		t.Result(errorcode.DBError)
		return
	}
	servTime := time.Now()
	logger.Infof("PageInfo%d-%f-1", uid, time.Since(servTime).Seconds())
	res := SrvPageInfo(uid, pageInfo, sRop, appClient, obChannel)
	if res == nil {
		t.Result(errorcode.SystemError)
		return
	}
	t.Result(res)
}

// nolint
func SrvPageInfo(uid int64, pageInfo *obpay.Page, sRop *rop.SceneRop, appClient *library.AppClient, obChannel *srvp.ObUserChannel) *PagePayInfo {
	res := &PagePayInfo{
		ID:                pageInfo.ID,
		HeadImg:           srvutil.UnmarshalImageStr(pageInfo.HeadImg),
		HeadVideo:         srvutil.UnmarshalVideoStr(pageInfo.HeadVideo),
		DescImg:           srvutil.UnmarshalImageStr(pageInfo.DescImg),
		SkuList:           make([]*library.SkuItem, 0),
		PayButtonImgPhone: srvutil.GetOnlineImageURL(pageInfo.PayButtonImgPhone),
		AbtInfo:           sRop,
		SkuStyle:          pageInfo.SkuStyle,
	}
	if appClient.ScreenType != int(libc.ScreenTypeEnum.Phone) {
		res.PayButtonImgPhone = srvutil.GetOnlineImageURL(pageInfo.PayButtonImgPad)
	}
	account := dbuser.TbAccount.GetUserByID(uid)
	if account != nil && account.Gender == libu.GenderMan {
		res.HeadImg = srvutil.UnmarshalImageStr(pageInfo.HeadImgMan)
		res.HeadVideo = srvutil.UnmarshalVideoStr(pageInfo.HeadVideoMan)
	}
	pMap := dbp.TbWebProduct.GetAllProductMap()
	if pMap == nil {
		return res
	}
	var isBuyProductIDs []int64
	orderList := dbo.TbWebOrder.GetListByUID(uid)
	for _, o := range orderList {
		isBuyProductIDs = append(isBuyProductIDs, o.ProductID)
	}
	pageSkuList := obpay.TbPageSku.GetListByPageID(pageInfo.ID)
	if len(pageSkuList) == 0 {
		logger.Error("付费方案页无sku", pageInfo.ID)
		return res
	}
	formatPagPageSkuItemMap := make(map[int64]*library.SkuItem)
	var wgSku sync.WaitGroup
	var lock sync.Mutex
	for _, vvv := range pageSkuList {
		vTemp := vvv
		wgSku.Add(1)
		safelygo.GoSafelyByTraceID(func() {
			defer wgSku.Done()
			product := pMap[vTemp.ProductID]
			item := srvp.SrvWProduct.FormatPagPageSkuItem(vTemp, product, appClient, obChannel)
			if item == nil || vTemp.ID == 0 {
				return
			}
			// 处理优惠产品
			if product != nil {
				formatOfferInfo(product, vTemp, isBuyProductIDs, item, appClient)
			}
			lock.Lock()
			defer lock.Unlock()
			formatPagPageSkuItemMap[vTemp.ID] = item
		})
	}
	wgSku.Wait()
	for _, v := range pageSkuList {
		item := formatPagPageSkuItemMap[v.ID]
		if item == nil || item.ID == 0 {
			continue
		}
		res.SkuList = append(res.SkuList, item)
	}
	if pageInfo.PayRetainPopupID > 0 {
		res.PayRetainPopup = &popup.RetainPopup{
			BgImg:       srvutil.UnmarshalImageStr(pageInfo.PayRetainImg),
			ProductInfo: srvp.SrvWProduct.GetPayPageSku(int64(pageInfo.PayRetainPopupID), appClient, obChannel),
		}
	}
	if pageInfo.RetainPopupID > 0 {
		res.RetainPopup = &popup.RetainPopup{
			BgImg:       srvutil.UnmarshalImageStr(pageInfo.RetainImg),
			ProductInfo: srvp.SrvWProduct.GetPayPageSku(int64(pageInfo.RetainPopupID), appClient, obChannel),
		}
	}
	return res
}

func isUserHasMemberSub(uid int64) bool {
	var hasSub bool
	subList := dbuser.TbWPSubU.GetValidSubscribeUser(uid)
	if len(subList) > 0 {
		hasSub = true
	}
	return hasSub
}

func formatOfferInfo(product *dbp.WebProduct, v *obpay.PageSku, isBuyProductIDs []int64,
	item *library.SkuItem, appClient *library.AppClient) {
	isOffer := false
	payType := 0
	if product.IsSubscribe == library.Yes && product.OfferType > libproduct.ConstOfferTypeNo &&
		!library.Int64InArray(v.ProductID, isBuyProductIDs) && srvord.CheckOfferVersion(product, appClient) {
		if library.IntInArray(pay.PayTypeAlipay, item.PayType) {
			isOffer = true
			payType = pay.PayTypeAlipay
		}
	}
	if isOffer {
		item.PayType = item.PayType[:0]
		item.PayType = append(item.PayType, payType)
		item.Img = srvutil.UnmarshalImageStr(v.OfferImage)
		item.SelectImg = srvutil.UnmarshalImageStr(v.OfferSelectImage)
		item.Price = library.FormatFloat(product.OfferTrialPrice)
		if product.OfferType == libproduct.ConstOfferTypeFirstBuy {
			item.Price = library.FormatFloat(product.OfferFirstBuyPrice)
		}
	}
}

// getPayPageConfig 获取付费方案页数据
func getPayPageConfig(uid int64, appClient *library.AppClient, isCompliance bool) (*obpay.Page, *rop.SceneRop) {
	var paymentPage *rop.PaymentPage
	var sRop rop.SceneRop
	paymentPage, sRop = rop.Serv.GetPageAbt(uid, appClient)
	page := obpay.TbPage.GetItemByID(uid, paymentPage.PageID)
	if page == nil {
		// 非合规，未被付费页ABT命中，安卓 ios都去1
		page = obpay.TbPage.GetItemByID(uid, int64(libc.GetDefaultPageID(appClient.OsType)))
		if isCompliance {
			page = obpay.TbPage.GetItemByID(uid, int64(libc.GetDefaultPageID(appClient.OsType)))
		}
	}
	sRop.AbtContent = paymentPage.PageID
	return page, &sRop
}
