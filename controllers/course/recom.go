package course

import (
	srvc "gitlab.dailyyoga.com.cn/server/children/service/course"
)

type recommend struct {
}

var FtRecommend recommend

type RecommendConf struct {
	Label string `json:"label"`
	Key   string `json:"key"`
	Value int    `json:"value"`
}

type RecommendLabelItem struct {
	Label           string             `json:"label"`
	Minutes         int                `json:"minutes,omitempty"`
	PracticePart    int                `json:"practice_part,omitempty"`
	IsNeedApparatus int                `json:"is_need_apparatus,omitempty"`
	Level           int                `json:"level,omitempty"`
	FitnessPurpose  int                `json:"fitness_purpose,omitempty"`
	CourseList      []*srvc.ItemCourse `json:"course_list"`
}
type RecommendLabelResp struct {
	List []*RecommendLabelItem `json:"list"`
}
