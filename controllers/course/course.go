package course

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	dbu "gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libcourse "gitlab.dailyyoga.com.cn/server/children/library/course"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	libuser "gitlab.dailyyoga.com.cn/server/children/library/user"
	"gitlab.dailyyoga.com.cn/server/children/service/course"
	"gitlab.dailyyoga.com.cn/server/children/service/user"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

func GetCourseDetail(t *http.Context) {
	sid := t.GetRequestStringD("sid", "")
	uid := user.SrvLogin.SIDToUID(sid)
	courseID := t.GetRequestInt64D("course_id", 0)
	if courseID == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	appClient := util.GetClientInfo(t)
	srvCourse := &course.SrvOptionCourse{}
	courseInfo := srvCourse.GetCourseDetail(uid, courseID, &libcourse.OptionCourse{
		IsOnline: library.Yes,
	}, appClient, course.WithLabel())
	if courseInfo == nil {
		t.Result(errorcode.SystemError)
		return
	}
	t.Result(courseInfo)
}

type GuessUserLikeRsp struct {
	List  []*course.ItemCourse `json:"list,omitempty"`
	Title string               `json:"title"`
}

func GuessUserLike(t *http.Context) {
	sid := t.GetRequestStringD("sid", "")
	appClient := util.GetClientInfo(t)
	uid := user.SrvLogin.SIDToUID(sid)
	title := "猜你喜欢"
	sceneType := t.GetRequestInt32D("scene_type", 0)
	if uid > 0 {
		accItem := dbu.TbAccount.GetUserByID(uid)
		// 个性化开关关闭 按首页逻辑走
		if accItem != nil && accItem.IsRecommend != library.Yes {
			sceneType = int32(libcourse.GuessLikeSceneTypeEnum.Index)
			title = "热门课程"
		}
	}
	courseID := t.GetRequestInt64D("course_id", 0)
	courseList := course.GuessUserLike(uid, sceneType, courseID, appClient)
	t.Result(&GuessUserLikeRsp{
		List:  courseList,
		Title: title,
	})
}

type LastPracticeRsp struct {
	List []*LastPracticeRspItem `json:"list"`
}

type LastPracticeRspItem struct {
	SceneType        int64              `json:"scene_type"`
	LastPracticeTime int64              `json:"last_practice_time"`
	PracticeLabels   []string           `json:"practice_labels,omitempty"`
	SceneTypeID      int64              `json:"scene_type_id,omitempty"`
	SceneTypeIDIndex int64              `json:"scene_type_index,omitempty"`
	IsCustomize      int                `json:"is_customize"`
	CourseInfo       *course.ItemCourse `json:"course_info"`
}

func LastPractice(t *http.Context) {
	sid := t.GetRequestStringD("sid", "")
	uid := user.SrvLogin.SIDToUID(sid)
	if uid == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	appClient := util.GetClientInfo(t)
	lastList := user.SrvLastPractice.GetUserLastPractice(uid)
	res := &LastPracticeRsp{
		List: make([]*LastPracticeRspItem, 0),
	}
	courseIDArr := make([]int64, 0)
	imPraCourseIDArr := make([]int64, 0)
	for _, v := range lastList {
		if v.SceneType == int32(libuser.SceneTypeEnum.ImmediatePractice) {
			imPraCourseIDArr = append(imPraCourseIDArr, v.CourseID)
		} else {
			courseIDArr = append(courseIDArr, v.CourseID)
		}
	}
	srvCourse := &course.SrvOptionCourse{}
	courseInfoList := srvCourse.BatchGetCourseDetail(uid, courseIDArr, appClient, &libcourse.OptionCourse{
		IsOnline: library.Yes,
	}, course.WithLabel())
	srvCourse = &course.SrvOptionCourse{}
	imPraCoInfoList := srvCourse.BatchGetCourseDetail(uid, imPraCourseIDArr, appClient, &libcourse.OptionCourse{
		IsOnline: library.Yes,
	}, course.WithLabel())
	if len(imPraCoInfoList) > 0 {
		courseInfoList = append(courseInfoList, imPraCoInfoList...)
	}
	for _, v := range lastList {
		item := &LastPracticeRspItem{
			SceneType:        int64(v.SceneType),
			LastPracticeTime: v.PracticeStartTime,
			PracticeLabels:   v.PracticeLabels,
		}
		for k := range courseInfoList {
			if courseInfoList[k].ID == v.CourseID {
				item.CourseInfo = courseInfoList[k]
			}
		}
		if item.CourseInfo == nil {
			continue
		}
		maxLabel := 3
		if v.SceneType != int32(libuser.SceneTypeEnum.ImmediatePractice) {
			if len(item.CourseInfo.PracticeLabels) > maxLabel {
				item.PracticeLabels = item.CourseInfo.PracticeLabels[0:maxLabel]
			} else {
				item.PracticeLabels = item.CourseInfo.PracticeLabels
			}
		}
		cv := util.UVersion.Format(appClient.Version)
		if cv.Version < libuser.PlanAppVersion && item.SceneType == int64(libuser.SceneTypeEnum.PlanProgram) {
			continue
		}
		item.IsCustomize = library.No
		res.List = append(res.List, item)
	}
	t.Result(res)
}

type LabelListRsp struct {
	LabelList []*course.SessionLabelItem `json:"label_list"`
}

func LabelList(t *http.Context) {
	data := course.GetSessionFilterLabelList()
	if len(data) < 1 {
		t.Result(errorcode.InvalidParams)
		return
	}
	resp := &LabelListRsp{
		LabelList: data,
	}
	t.Result(resp)
}

type SelectTabFilterRsp struct {
	List []*course.ItemCourse `json:"list"`
}

func SelectTabFilter(t *http.Context) {
	labelID := t.GetRequestInt64D("label_id", 0)
	client := util.GetClientInfo(t)
	sid := t.GetRequestStringD("sid", "")
	uid := user.SrvLogin.SIDToUID(sid)
	if labelID == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	srvSelectTabFilter := &course.SrvSelectTabFilter{
		LabelID:   labelID,
		Page:      t.GetRequestIntD("page", 0),
		PageSize:  t.GetRequestIntD("page_size", 0),
		AppDevice: client,
		UID:       uid,
	}
	resp := &SelectTabFilterRsp{
		List: make([]*course.ItemCourse, 0),
	}
	courseList := srvSelectTabFilter.SelectTabFilter()
	if courseList == nil {
		t.Result(resp)
		return
	}
	resp.List = courseList
	t.Result(resp)
}
