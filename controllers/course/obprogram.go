package course

import (
	"encoding/json"
	"strings"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"

	dbcourse "gitlab.dailyyoga.com.cn/server/children/databases/course"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	libuser "gitlab.dailyyoga.com.cn/server/children/library/user"
	"gitlab.dailyyoga.com.cn/server/children/service/course"
	"gitlab.dailyyoga.com.cn/server/children/service/user"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

// ObProgramDetail Ob课表详情
func ObProgramDetail(t *http.Context) {
	sid := t.GetRequestStringD("sid", "")
	uid := user.SrvLogin.SIDToUID(sid)
	appClient := util.GetClientInfo(t)
	var detail *course.ObProgramDetail
	if uid == 0 {
		detail = course.GetDefaultDisplayObProgram(appClient)
		t.Result(detail)
		return
	}
	detail = course.GetObProgramDetail(uid, appClient, 0)
	if detail == nil {
		t.Result(errorcode.SystemError)
		return
	}
	t.Result(detail)
}

// RestartObProgram 重新开始Ob课表
func RestartObProgram(t *http.Context) {
	sid := t.GetRequestStringD("sid", "")
	uid := user.SrvLogin.SIDToUID(sid)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	obProgramID := t.GetRequestInt64D("ob_program_id", 0)
	if obProgramID == 0 {
		// 获取正在进行中的计划，如果没有则创建兜底计划
		inProcessProgram := dbcourse.TbObProgram.GetInProcessProgram(uid)
		if inProcessProgram != nil {
			obProgramID = inProcessProgram.ID
		} else {
			t.Result(library.EmptyResponse{})
			return
		}
	}
	ecode := course.RestartObProgram(uid, obProgramID)
	if ecode > 0 {
		t.Result(ecode)
		return
	}
	t.Result(library.EmptyResponse{})
}

// CreateObProgram 创建Ob课表
// nolint
func CreateObProgram(t *http.Context) {
	choice := &course.ObUserChoice{}
	appClient := util.GetClientInfo(t)
	t.ParseRequestStruct(choice)
	// 签名兼容新接口
	jsonChoice, _ := json.Marshal(choice)
	logger.Info("CreateObProgram", "choice", string(jsonChoice))
	uid := user.SrvLogin.SIDToUID(choice.SID)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	if choice.Gender == libuser.GenderUnknown {
		choice.Gender = libuser.GenderMan
	}
	if choice.Height == 0 {
		choice.Height = 120
	}
	if choice.Weight == 0 {
		choice.Weight = 30
	}
	birthday := strings.Split(choice.Birthday, "-")
	if len(birthday) == 2 {
		if len(birthday[1]) == 1 {
			choice.Birthday = birthday[0] + "0" + birthday[1]
		} else {
			choice.Birthday = birthday[0] + birthday[1]
		}
	}
	if choice.Birthday != "" {
		t, err := time.Parse("200601", choice.Birthday)
		if err == nil {
			choice.Age = time.Now().Year() - t.Year()
		}
	}
	if choice.Age == 0 {
		choice.Age = 8
	}
	user.SrvLabel.CalcUserLabelBefore(uid, appClient, choice)
	ecode := course.CreateObProgram(uid, appClient, choice, 0)
	if ecode > 0 {
		t.Result(ecode)
		return
	}
	// 同步用户信息
	safelygo.GoSafelyByTraceID(
		func() {
			information := &user.Information{
				Gender: choice.Gender,
				Weight: choice.Weight,
				Age:    choice.Age,
				Height: choice.Height,
			}
			if err := user.UpdateUserInformation(uid, information, false); err != errorcode.Success {
				t.Result(err)
				return
			}
		})
	// 更新用户标签
	safelygo.GoSafelyByTraceID(
		func() {
			user.SrvLabel.CalcUserLabel(uid, appClient)
		})
	// 根据用户OB选择更新用户信息
	if !course.UpdateAccountByObChoice(uid, choice) {
		t.Result(errorcode.DBError)
		return
	}
	t.Result(library.EmptyResponse{})
}
