package user

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/crypto"
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/gokit/sensorsdata"
	"gitlab.dailyyoga.com.cn/server/children/config"
	dbuser "gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/client"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	libuser "gitlab.dailyyoga.com.cn/server/children/library/user"
	"gitlab.dailyyoga.com.cn/server/children/service/app"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	srvsensor "gitlab.dailyyoga.com.cn/server/children/service/sensor"
	srvuser "gitlab.dailyyoga.com.cn/server/children/service/user"
	srvutil "gitlab.dailyyoga.com.cn/server/children/service/util"
)

type login struct {
}

var DfLogin login

// Login 登录接口
// nolint
func (*login) Login(t *http.Context) {
	params := &srvuser.LoginParams{}
	t.ParseRequestStruct(params)
	params.Mobile = srvuser.GetMobilePhone(t, params.Mobile)
	if params.Password != "" {
		cfg := config.Get()
		params.Password = string(crypto.NewAes(cfg.Service.PassWdAesKey, cfg.Service.PassWdAesKey).Decrypt(params.Password))
	}
	appClient := srvutil.GetClientInfo(t)
	if params.LoginType == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	if params.UID == 0 && params.LoginType != libuser.LoginTypeShenCe {
		params.AnonymousID = ""
	}
	srvLogin := &srvuser.Login{
		AppClient:   appClient,
		AnonymousID: params.AnonymousID,
	}
	logger.Infof("用户anonymousID:%+v", srvLogin)
	var loginUserDetail *srvuser.LoginUserDetail
	var ecode errorcode.ErrorCode
	switch params.LoginType {
	case libuser.LoginTypeMobileVercode:
		loginUserDetail, ecode = srvLogin.LoginVercode(params)
	case libuser.LoginTypePassword:
		loginUserDetail, ecode = srvLogin.LoginPassword(params)
	case libuser.LoginTypeOneKey:
		loginUserDetail, ecode = srvLogin.LoginOneKey(params)
	case libuser.LoginTypeWechat:
		loginUserDetail, ecode = srvLogin.LoginWechat(params)
	case libuser.LoginTypeApple:
		loginUserDetail, ecode = srvLogin.LoginApple(params)
	case libuser.LoginTypeShenCe:
		loginUserDetail, ecode = srvLogin.LoginShenCe(params)
	}
	if ecode > 0 {
		t.Result(ecode)
		return
	}
	if loginUserDetail == nil {
		t.Result(errorcode.SystemError)
		return
	}
	sid, errCode := srvLogin.GenerateSID(loginUserDetail.UID)
	if errCode != errorcode.Success || sid == "" {
		t.Result(errCode)
		return
	}
	logger.Infof("用户anonymousID:%+v", srvLogin)
	loginUserDetail.SID = sid
	// OCPX数据回传
	oaid := params.OaID
	if oaid != "" {
		rd := cache.GetYoga01Redis().GetClient()
		cacheKeyOaid := fmt.Sprintf(libuser.OcpdOaidKey+"%s", oaid)
		err := rd.Set(context.Background(), cacheKeyOaid, loginUserDetail.UID, 35*srvutil.SecondsPerDay*time.Second).Err()
		if err != nil {
			logger.Warnf("π项目 ocpd 设置出错 %s", err.Error())
		}
	}
	if loginUserDetail.IsRegister {
		action := &app.Activation{
			AnonymousID: params.AnonymousID,
			ChannelStr:  strconv.Itoa(appClient.Channel),
			UID:         loginUserDetail.UID,
			ActionType:  int64(client.OppoOcpxTypeEnum.Register),
		}
		if oaid != "" {
			action.OuID = oaid
		}
		safelygo.GoSafelyByTraceID(func() {
			action.UserRegister()
			srvsensor.UserProfileVipType(loginUserDetail.UID)
		})
	}
	if params.AnonymousID != "" && loginUserDetail.UID > 0 {
		srvsensor.TrackSignup(loginUserDetail.UID, params.AnonymousID)
	}
	safelygo.GoSafelyByTraceID(func() {
		ProfileSubChannelByLoginParams(loginUserDetail.UID, appClient, params)
	})
	t.Result(loginUserDetail)
}

func ProfileSubChannelByLoginParams(uid int64, appClient *library.AppClient, params *srvuser.LoginParams) {
	if strconv.Itoa(appClient.Channel) == client.ChannelXiaomi {
		subChannel := appClient.SubChannel
		if appClient.SubChannel == "" {
			subChannel = params.SubChannel
		}
		p := map[string]interface{}{
			"DownloadSecondChannel": subChannel,
		}
		logger.Info("DownloadSecondChannel", uid, subChannel)
		err := sensorsdata.ProfileSet(uid, p, true)
		if err != nil {
			logger.Warnf("神策设置出错 %s", err.Error())
		}
		return
	}
}

type LogoutReq struct {
	SID string `json:"sid"`
}

// Logout 退出登录接口
func (*login) Logout(t *http.Context) {
	req := &LogoutReq{}
	t.ParseRequestStruct(req)
	uid := srvuser.SrvLogin.SIDToUID(req.SID)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	if !srvuser.SrvLogin.LogoutSID(req.SID, uid) {
		t.Result(errorcode.SystemError)
		return
	}
	t.Result(library.EmptyResponse{})
}

type FindPasswordReq struct {
	Mobile      string `json:"mobile_phone"`
	Code        int    `json:"mobile_vercode"`
	Password    string `json:"password"`
	AnonymousID string `json:"anonymous_id"` // 神策匿名ID
}

// FindPassword 找回密码
func (*login) FindPassword(t *http.Context) {
	req := &FindPasswordReq{}
	t.ParseRequestStruct(req)
	req.Mobile = srvuser.GetMobilePhone(t, req.Mobile)
	// 密码解密
	if req.Password != "" {
		cfg := config.Get()
		req.Password = string(crypto.NewAes(cfg.Service.PassWdAesKey, cfg.Service.PassWdAesKey).Decrypt(req.Password))
	}
	if req.Password == "" {
		t.Result(errorcode.InvalidParams)
		return
	}
	// 验证验证码是否正确
	if !srvuser.SrvVercode.VerifyVercode(req.Mobile, req.Code, libuser.VercodeTypeFindPassword) {
		t.Result(errorcode.VercodeError)
		return
	}
	ecode := (&srvuser.Login{}).FindPassword(req.Mobile, req.Password)
	if ecode > 0 {
		t.Result(ecode)
		return
	}
	appClient := srvutil.GetClientInfo(t)
	srvLogin := &srvuser.Login{
		AppClient:   appClient,
		AnonymousID: req.AnonymousID,
	}
	params := &srvuser.LoginParams{
		Mobile:    req.Mobile,
		Password:  req.Password,
		LoginType: libuser.LoginTypePassword,
	}
	loginUserDetail, ecode := srvLogin.LoginPassword(params)
	if ecode > 0 {
		t.Result(ecode)
		return
	}
	if loginUserDetail == nil {
		t.Result(errorcode.SystemError)
		return
	}
	sid, errCode := srvLogin.GenerateSID(loginUserDetail.UID)
	if errCode != errorcode.Success || sid == "" {
		t.Result(errCode)
		return
	}
	loginUserDetail.SID = sid
	t.Result(loginUserDetail)
}

type ResetPasswordReq struct {
	SID      string `json:"sid"`
	Mobile   string `json:"mobile_phone"`
	Code     int    `json:"mobile_vercode"`
	Password string `json:"password"`
}

func (*login) ResetPassword(t *http.Context) {
	req := &ResetPasswordReq{}
	t.ParseRequestStruct(req)
	req.Mobile = srvuser.GetMobilePhone(t, req.Mobile)
	uid := srvuser.SrvLogin.SIDToUID(req.SID)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	account := dbuser.TbAccount.GetUserByID(uid)
	if account != nil {
		req.Mobile = account.Mobile
	}
	// 密码解密
	if req.Password != "" {
		cfg := config.Get()
		req.Password = string(crypto.NewAes(cfg.Service.PassWdAesKey, cfg.Service.PassWdAesKey).Decrypt(req.Password))
	}
	if req.Password == "" {
		t.Result(errorcode.InvalidParams)
		return
	}
	// 验证验证码是否正确
	if !srvuser.SrvVercode.VerifyVercode(req.Mobile, req.Code, libuser.VercodeTypeResetPassword) {
		t.Result(errorcode.VercodeError)
		return
	}
	ecode := (&srvuser.Login{}).FindPassword(req.Mobile, req.Password)
	if ecode > 0 {
		t.Result(ecode)
		return
	}
	t.Result(library.EmptyResponse{})
}

type LogoffReq struct {
	SID string `json:"sid"`
}

// Logoff 注销账号
func (*login) Logoff(t *http.Context) {
	req := &LogoffReq{}
	t.ParseRequestStruct(req)
	uid := srvuser.SrvLogin.SIDToUID(req.SID)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	params := &srvuser.LogoffAccountReq{}
	t.ParseRequestStruct(params)
	params.UID = uid
	sub := dbuser.TbWPSubU.GetWebSubscribeUser(uid)
	if sub != nil {
		t.Result(errorcode.SubscribeNotClose)
		return
	}
	ecode := srvuser.SrvLogin.LogoffAccount(params)
	if ecode > 0 {
		t.Result(ecode)
		return
	}
	if !srvuser.SrvLogin.LogoffAllSID(uid) {
		t.Result(errorcode.SystemError)
		return
	}
	t.Result(library.EmptyResponse{})
}
