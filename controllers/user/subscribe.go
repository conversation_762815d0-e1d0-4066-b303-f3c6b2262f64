package user

import (
	"fmt"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	"gitlab.dailyyoga.com.cn/server/children/service/order"
	"gitlab.dailyyoga.com.cn/server/children/service/user"
)

type ManageRsp struct {
	UserSubscribeList []*SubscribeItem `json:"user_subscribe_list"`
}

type SubscribeItem struct {
	BusinessTips     string `json:"business_tips,omitempty"`
	ID               int64  `json:"id,omitempty"`
	Name             string `json:"name"`
	Price            string `json:"price"`
	PayTypeDesc      string `json:"pay_type_desc"`
	PayType          int32  `json:"pay_type"`
	NextChargingDate string `json:"next_charging_date"`
	BusinessType     int32  `json:"business_type"`
}

func Manage(t *http.Context) {
	sid := t.GetRequestStringD("sid", "")
	uid := user.SrvLogin.SIDToUID(sid)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	result := &ManageRsp{
		UserSubscribeList: make([]*SubscribeItem, 0),
	}
	subList := user.GetUserSubscribe(uid)
	for i := range subList {
		result.UserSubscribeList = append(result.UserSubscribeList, &SubscribeItem{
			BusinessTips:     "您已开通小树苗运动会员自动续费",
			ID:               subList[i].ID,
			Name:             subList[i].Name,
			Price:            subList[i].Price,
			PayTypeDesc:      subList[i].PayTypeDesc,
			PayType:          subList[i].PayType,
			NextChargingDate: subList[i].NextChargingDate,
			BusinessType:     subList[i].BusinessType,
		})
	}
	t.Result(result)
}

type UnSubscribeParams struct {
	ID  int64  `json:"id"`
	SID string `json:"sid"`
}

func UnSubscribe(t *http.Context) {
	params := &UnSubscribeParams{}
	t.ParseRequestStruct(params)
	uid := user.SrvLogin.SIDToUID(params.SID)
	if ok, err := order.UnsubscribeByID(uid, params.ID); !ok {
		t.Result(errorcode.SystemError, fmt.Sprintf("取消订阅失败，%s", err.Error()))
		return
	}
	t.Result(library.EmptyResponse{})
}
