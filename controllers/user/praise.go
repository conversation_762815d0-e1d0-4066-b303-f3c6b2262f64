package user

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	dbuser "gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	libuser "gitlab.dailyyoga.com.cn/server/children/library/user"
	srvuser "gitlab.dailyyoga.com.cn/server/children/service/user"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type StorePraiseReportReq struct {
	SID            string `json:"sid"`
	Img            string `json:"img"`
	PraiseID       int64  `json:"praise_id"`
	PraisePlatform int32  `json:"praise_platform"`
	EntranceType   int32  `json:"entrance_type"`
}

// StorePraiseReport 应用商店好评上报
func StorePraiseReport(t *http.Context) {
	params := &StorePraiseReportReq{}
	t.ParseRequestStruct(params)
	uid := srvuser.SrvLogin.SIDToUID(params.SID)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	appClient := util.GetClientInfo(t)
	if item := dbuser.TbStorePraise.GetItemByUID(uid); item != nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	if item := dbuser.TbStorePraise.GetItemByDeviceID(appClient.DeviceID); item != nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	if params.PraisePlatform == 0 {
		params.PraisePlatform = library.Yes
	}
	awardType := int64(library.Yes)
	awardDuration := int64(library.Const3)
	if params.PraiseID != 0 {
		itemResource := dbuser.TbPraiseResource.GetItemByID(params.PraiseID)
		if itemResource != nil {
			awardType = itemResource.AwardType
			awardDuration = itemResource.AwardDuration
		}
	}
	if params.Img != "" && !srvuser.SensitiveImageCheck([]string{params.Img}) {
		t.Result(errorcode.HasSensitiveImage)
		return
	}
	sp := dbuser.StorePraise{
		UID:              uid,
		Img:              params.Img,
		DeviceID:         appClient.DeviceID,
		AuditStatus:      int32(libuser.StorePraiseAuditEnum.Wait),
		PraiseResourceID: params.PraiseID,
		PraiseLatform:    params.PraisePlatform,
		AwardType:        awardType,
		AwardDuration:    awardDuration,
		EntranceType:     params.EntranceType,
	}
	if err := sp.Save(); err != nil {
		t.Result(errorcode.DBError)
		return
	}
	t.Result(library.EmptyResponse{})
}
