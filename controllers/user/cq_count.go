package user

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	dbuser "gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libc "gitlab.dailyyoga.com.cn/server/children/library/client"
	"gitlab.dailyyoga.com.cn/server/children/service/audit"
	srvuser "gitlab.dailyyoga.com.cn/server/children/service/user"
	srvutil "gitlab.dailyyoga.com.cn/server/children/service/util"
)

func GetOBIsEnter(t *http.Context) {
	appClient := srvutil.GetClientInfo(t)
	count := t.GetRequestInt64D("count", 1)
	sid := t.GetRequestStringD("sid", "")
	uid := srvuser.SrvLogin.SIDToUID(sid)
	if userInfo := dbuser.TbAccount.GetUserByID(uid); userInfo != nil {
		userInfo.ObEnterNum += count
		if err := userInfo.Update(); err != nil {
			logger.Error(err.Error())
		}
	}
	// 累加次数
	audit.StatisticsEntranceCount(libc.SceneAllOb, count)
	// 重庆特殊处理，优先级最高
	if audit.IsChongqingIP(appClient.IPAds) {
		audit.StatisticsEntranceCount(libc.SceneCqOb, count)
	}
	t.Result(library.EmptyResponse{})
}

func GetPageIsEnter(t *http.Context) {
	appClient := srvutil.GetClientInfo(t)
	// 累加次数
	audit.StatisticsEntranceCount(libc.SceneAllPage, 1)
	// 重庆特殊处理，优先级最高
	if audit.IsChongqingIP(appClient.IPAds) {
		audit.StatisticsEntranceCount(libc.SceneCqPage, 1)
	}
	t.Result(library.EmptyResponse{})
}
