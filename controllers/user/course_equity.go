package user

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/course"
)

type CourseEquity struct {
	ID           int64 `xorm:"not null autoincr pk bigint(20) 'id'"`
	UID          int64 `xorm:"not null bigint(20) 'uid'" json:"uid"`
	ResourceType int   `xorm:"not null int(6) 'resource_type'" json:"resource_type"`
	ResourceID   int64 `xorm:"not null int(6) 'resource_id'" json:"resource_id"`
	IsValid      int   `xorm:"not null tinyint(4) 'is_valid'" json:"is_valid"`
	CreateTime   int64 `xorm:"int(11) notnull 'create_time'" json:"create_time"`
	UpdateTime   int64 `xorm:"int(11) notnull 'update_time'" json:"update_time"`
}

// TableName 获取表名
func (CourseEquity) TableName() string {
	return "user_course_equity"
}

// Save 保存数据
func (c *CourseEquity) SaveByTran(session *xorm.Session) error {
	c.CreateTime = time.Now().Unix()
	c.UpdateTime = c.CreateTime
	_, err := session.Insert(c)
	return err
}

// Update 更新数据
func (c *CourseEquity) Update() error {
	c.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(c.ID).Update(c)
	return err
}

type courseEquity struct{}

var TbUserCourseEquity courseEquity

func (*courseEquity) GetItem(uid int64, resourceType course.ResourceTypeInt, resourceID int64) *CourseEquity {
	var table CourseEquity
	ok, err := databases.GetEngine().
		Where("uid = ? and resource_type = ? and resource_id = ? and is_valid = ?",
			uid, resourceType, resourceID, library.Yes).
		Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
