package user

import (
	"encoding/json"
	"net"
	netHttp "net/http"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	srvuser "gitlab.dailyyoga.com.cn/server/children/service/user"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

const (
	XForwardedFor = "X-Forwarded-For"
	XRealIP       = "X-Real-IP"
	// IosIdfaNullVal  iOS idfa 空值
	IosIdfaNullVal = "00000000-0000-0000-0000-000000000000"
)

type ClientIntegralWallRsp struct {
	Aso bool `json:"aso"`
}

type Respose struct {
	Status    int    `json:"status"`
	ErrorCode int    `json:"error_code"`
	ErrorDesc string `json:"error_desc"`
	Result    string `json:"result"`
}

func IntegralWallSubmit(t *http.Context) {
	params := &srvuser.IntegralWallSubmitReq{}
	t.ParseRequestStruct(params)
	if params.IP == "" || params.OS == "" || params.Device == "" || params.Idfa == "" || params.AppID == "" ||
		params.Keywords == "" {
		t.Result(errorcode.InvalidParams)
		return
	}
	record := &user.IntegralWall{
		IP:       params.IP,
		OS:       params.OS,
		Device:   params.Device,
		Idfa:     params.Idfa,
		AppID:    params.AppID,
		Keywords: params.Keywords,
	}
	if err := record.Save(); err != nil {
		logger.Error(err)
	}
	resp := Respose{
		Status: library.Yes,
		Result: "ok",
	}
	respByte, _ := json.Marshal(resp)
	util.WriteRespose(t, string(respByte))
}

func IntegralWallReport(t *http.Context) {
	ip := RemoteIP(t.Request)
	params := &srvuser.IntegralWallSubmitReq{}
	t.ParseRequestStruct(params)
	if params.Idfa == IosIdfaNullVal {
		params.Idfa = ""
	}
	resp := &ClientIntegralWallRsp{}
	record := user.TbIntegralWall.GetItem(ip, params.OS, params.Device, params.Idfa)
	if record == nil || record.ID == 0 {
		resp.Aso = false
	} else {
		resp.Aso = true
	}
	logger.Infof("IOS asc上报：idfa：%s, ip:%s, os:%s, device:%s, aso:%v", params.Idfa, ip, params.OS, params.Device, resp.Aso)
	t.Result(resp)
}

func RemoteIP(req *netHttp.Request) string {
	remoteAddr := req.RemoteAddr
	if ip := req.Header.Get(XRealIP); ip != "" {
		remoteAddr = ip
	} else if ip = req.Header.Get(XForwardedFor); ip != "" {
		remoteAddr = ip
	} else {
		remoteAddr, _, _ = net.SplitHostPort(remoteAddr)
	}

	if remoteAddr == "::1" {
		remoteAddr = "127.0.0.1"
	}

	return remoteAddr
}
