package user

import (
	"encoding/json"
	"fmt"
	"math"
	h "net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/databases/client"
	"gitlab.dailyyoga.com.cn/server/children/databases/practice"
	dbuser "gitlab.dailyyoga.com.cn/server/children/databases/user"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libc "gitlab.dailyyoga.com.cn/server/children/library/client"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	libob "gitlab.dailyyoga.com.cn/server/children/library/ob"
	librop "gitlab.dailyyoga.com.cn/server/children/library/rop"
	libuser "gitlab.dailyyoga.com.cn/server/children/library/user"
	"gitlab.dailyyoga.com.cn/server/children/service/app"
	"gitlab.dailyyoga.com.cn/server/children/service/audit"
	course "gitlab.dailyyoga.com.cn/server/children/service/course"
	"gitlab.dailyyoga.com.cn/server/children/service/equity"
	srvp "gitlab.dailyyoga.com.cn/server/children/service/practice"
	"gitlab.dailyyoga.com.cn/server/children/service/rop"
	"gitlab.dailyyoga.com.cn/server/children/service/sensor"
	srvuser "gitlab.dailyyoga.com.cn/server/children/service/user"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type dfuser struct {
}

const (
	WeightBeforeBlue       = "weightBeforeBlue"
	WeightBeforeOrange     = "weightBeforeOrange"
	WeightAfterBlueRobot   = "weightAfterBlueRobot"
	WeightAfterOrangeRobot = "weightAfterOrangeRobot"
	WeightAfterBlueCoach   = "weightAfterBlueCoach"
	WeightAfterOrangeCoach = "weightAfterOrangeCoach"

	ScanBody        = "scanBody"
	transitionVideo = "transitionVideo"
	Seconds         = 60
)

var WeightKeys = []string{
	WeightBeforeBlue,
	WeightBeforeOrange,
	WeightAfterBlueRobot,
	WeightAfterOrangeRobot,
	WeightAfterBlueCoach,
	WeightAfterOrangeCoach,
}

var TransitionKeys = []string{
	ScanBody,
	transitionVideo,
}

var ChongqingKeys = []string{"transitionTarget", "purposeAll", "bodyPositionAll", "transitionBody", "gender",
	"weightCurrent", "weightTarget", "transitionAboutYou", "bodyHurt", "transitionSport", "practicePeriod",
	"ability", "apparatus"}

var DfUser dfuser

type UpdateInfoReq struct {
	SID         string `json:"sid"`
	Gender      int    `json:"gender"`
	NickName    string `json:"nick_name"`
	IsRecommend int    `json:"is_recommend"`
	Avatar      string `json:"avatar"`
	Age         int    `json:"age"`
	Height      int    `json:"height"`
	Weight      int    `json:"weight"`
}

// UpdateInfo 更新用户信息接口
func (*dfuser) UpdateInfo(t *http.Context) {
	req := &UpdateInfoReq{}
	t.ParseRequestStruct(req)
	uid := srvuser.SrvLogin.SIDToUID(req.SID)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	information := &srvuser.Information{
		Gender:      req.Gender,
		NickName:    req.NickName,
		Avatar:      req.Avatar,
		Weight:      req.Weight,
		Age:         req.Age,
		Height:      req.Height,
		IsRecommend: req.IsRecommend,
	}
	eCode := srvuser.UpdateUserInformation(uid, information, true)
	if eCode != errorcode.Success {
		t.Result(eCode)
		return
	}
	t.Result(&library.EmptyResponse{})
}

// CommonInfo 用户通用信息接口
func (*dfuser) CommonInfo(t *http.Context) {
	sid := t.GetRequestStringD("sid", "")
	uid := srvuser.SrvLogin.SIDToUID(sid)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	appClient := util.GetClientInfo(t)
	attributes := []string{
		libuser.AttributeBindInfo,
		libuser.AttributeLastPracticeCount,
		libuser.CanJoinStorePraise,
		libuser.OBChoiceInfo,
		libuser.AttributePermanentlyVip,
	}
	res := srvuser.SrvUser.GetUserAttribute(uid, attributes, appClient)
	if res == nil {
		t.Result(errorcode.HasNotLogin)
		return
	}
	res.SID = sid
	t.Result(res)
}

type BindMobileReq struct {
	SID         string `json:"sid"`
	MobilePhone string `json:"mobile_phone"`
	Code        int    `json:"mobile_vercode"`
}

// BindMobile 绑定手机号
func (*dfuser) BindMobile(t *http.Context) {
	req := &BindMobileReq{}
	t.ParseRequestStruct(req)
	req.MobilePhone = srvuser.GetMobilePhone(t, req.MobilePhone)
	uid := srvuser.SrvLogin.SIDToUID(req.SID)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	// 验证验证码是否正确
	if !srvuser.SrvVercode.VerifyVercode(req.MobilePhone, req.Code, libuser.VercodeTypeBindMobile) {
		t.Result(errorcode.VercodeError)
		return
	}
	account := dbuser.TbAccount.GetUserByID(uid)
	if account == nil {
		t.Result(errorcode.SystemError)
		return
	}
	account.Mobile = req.MobilePhone
	if err := account.Update(); err != nil {
		logger.Error(err)
		t.Result(errorcode.DBError)
		return
	}
	t.Result(library.EmptyResponse{})
}

// BindWechat 账号绑定微信
func (*dfuser) BindWechat(t *http.Context) {
	params := &srvuser.BindWechatReq{}
	t.ParseRequestStruct(params)
	uid := srvuser.SrvLogin.SIDToUID(params.SID)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	params.UID = uid
	ecode := srvuser.BindAccountWechat(params)
	if ecode > 0 {
		t.Result(ecode)
		return
	}
	t.Result(library.EmptyResponse{})
}

// BindApple 账号绑定苹果
func (*dfuser) BindApple(t *http.Context) {
	params := &srvuser.BindAppleReq{}
	t.ParseRequestStruct(params)
	uid := srvuser.SrvLogin.SIDToUID(params.SID)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}

	params.UID = uid
	ecode := srvuser.BindAccountApple(params)
	if ecode > 0 {
		t.Result(ecode)
		return
	}
	t.Result(library.EmptyResponse{})
}

// BindOneKey 一键登录绑定手机号
func (*dfuser) BindOneKey(t *http.Context) {
	params := &srvuser.BindOneKeyReq{}
	t.ParseRequestStruct(params)
	uid := srvuser.SrvLogin.SIDToUID(params.SID)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	params.UID = uid
	ecode := srvuser.BindAccountOneKey(params)
	if ecode > 0 {
		t.Result(ecode)
		return
	}
	t.Result(library.EmptyResponse{})
}

// PracticeReport 练习数据上报
func (*dfuser) PracticeReport(t *http.Context) {
	params := &srvuser.PracticeReportParam{}
	t.ParseRequestStruct(params)
	uid := srvuser.SrvLogin.SIDToUID(params.SID)
	if uid <= 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	params.UID = uid
	safelygo.GoSafelyByTraceID(func() {
		if err := srvuser.SrvUser.PracticeReport(params); err != nil {
			logger.Info(err.Error())
		}
		account := dbuser.TbAccount.GetUserByID(uid)
		if account != nil {
			srvuser.GetPracticeCourseNum(uid, account.CreateTime, false)
		}
	})
	t.Result(library.EmptyResponse{})
}

// PracticeFeelReport 练习感受上报
func (*dfuser) PracticeFeelReport(t *http.Context) {
	params := &srvuser.PracticeFeelReportParam{}
	t.ParseRequestStruct(params)
	uid := srvuser.SrvLogin.SIDToUID(params.SID)
	if uid <= 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	params.UID = uid
	err := srvuser.SrvUser.PracticeFeelReport(params)
	if err != nil {
		logger.Error(err)
		t.Result(errorcode.SystemError)
		return
	}
	t.Result(library.EmptyResponse{})
}

// ReptocalSensor 用户同意隐私政策之前，客户端调服务端接口上报神策
// nolint
func (*dfuser) ReptocalSensor(t *http.Context) {
	event := t.GetRequestStringD("event", "click_general_cs")
	appClient := util.GetClientInfo(t)
	properties := &sensor.ClickEvent{
		CommData:        sensor.InitCommDataByClient(appClient),
		DownloadChannel: strconv.Itoa(appClient.Channel),
	}
	if event == "click_general_cs" {
		properties.ClickID = t.GetRequestIntD("click_id", 90000)
		properties.ClickSourceURL = t.GetRequestStringD("click_source_url", "")
	} else {
		properties.PageID = t.GetRequestIntD("page_id", 90000)
	}
	properties.Track("0")
	t.Result(library.EmptyResponse{})
}

type AuditInfoRsp struct {
	IsAudit  int  `json:"is_audit,omitempty"`
	IsSwitch bool `json:"is_switch"`
}
type AuditInfoReq struct {
	UID int64 `json:"uid"`
}

// AuditInfo 审核相关配置 (1.6以后升级成为合规开关)
func (*dfuser) AuditInfo(t *http.Context) {
	req := &AuditInfoReq{}
	t.ParseRequestStruct(req)
	appClient := util.GetClientInfo(t)
	res := &AuditInfoRsp{
		IsSwitch: audit.GetCompliance(req.UID, appClient),
	}
	t.Result(res)
}

// GetAppAudit 1.6以后审核相关配置
func (*dfuser) GetAppAudit(t *http.Context) {
	appClient := util.GetClientInfo(t)
	res := &AuditInfoRsp{
		IsAudit: library.No,
	}
	if audit.GetAuditInfo(appClient).IsInAudit {
		res.IsAudit = library.Yes
	}
	t.Result(res)
}

type BindAnonymousIDReq struct {
	AnonymousID string `json:"anonymous_id"`
}

func (*dfuser) BindAnonymousID(t *http.Context) {
	req := &BindAnonymousIDReq{}
	t.ParseRequestStruct(req)
	appClient := util.GetClientInfo(t)
	logger.Info("客户端匿名ID上报", appClient.Channel, appClient.DeviceID, appClient.OsType, appClient.Version, req.AnonymousID)
	safelygo.GoSafelyByTraceID(func() {
		audit.UploadProcessData(appClient, req.AnonymousID)
	})
	t.Result(library.EmptyResponse{})
}

// BindByteAttribution 巨量实时归因
func (*dfuser) BindByteAttribution(t *http.Context) {
	req := &app.BindAttributionReq{}
	t.ParseRequestStruct(req)
	appClient := util.GetClientInfo(t)
	logger.Info("巨量实时归因 上报", appClient.Channel, appClient.DeviceID, appClient.OsType, appClient.Version, req.AnonymousID)
	a := app.OceanEngine{}
	if err := a.UploadInfo(appClient, req); err != nil {
		logger.Warn("巨量归因数据异常上报", appClient.Channel, appClient.DeviceID, appClient.OsType,
			appClient.Version, req.AnonymousID, req.PackageName)
		t.Result(errorcode.InvalidParams)
		return
	}
	t.Result(library.EmptyResponse{})
}

func (*dfuser) SetUtmSource(t *http.Context) {
	anonymousID := t.GetRequestStringD("anonymous_id", "")
	channel := t.GetRequestIntD("channel", 0)
	if anonymousID == "" {
		t.Result(errorcode.InvalidParams)
		return
	}
	audit.SetSensorUtmSource(anonymousID, channel)
	t.Result(library.EmptyResponse{})
}

type CommonAbtResult struct {
	AbtList []*rop.SceneRop `json:"abt_list"` // ABT列表
}

// nolint
func (*dfuser) CommonAbtResult(t *http.Context) {
	sid := t.GetRequestStringD("sid", "")
	uid := srvuser.SrvLogin.SIDToUID(sid)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	abtSceneID := t.GetRequestStringD("abt_scene_id", "")
	if abtSceneID == "" {
		t.Result(errorcode.InvalidParams)
		return
	}
	abtSceneList := strings.Split(abtSceneID, ",")
	if len(abtSceneList) == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	appClient := util.GetClientInfo(t)
	res := CommonAbtResult{
		AbtList: make([]*rop.SceneRop, 0),
	}
	result := make(map[string]CommonAbtResult)
	var wg sync.WaitGroup
	var lock sync.Mutex
	for _, v := range abtSceneList {
		vTemp := v
		wg.Add(1)
		safelygo.GoSafelyByTraceID(
			func() {
				defer wg.Done()
				data := CommonAbtResultSrv(vTemp, uid, appClient)
				if len(data.AbtList) == 0 {
					return
				}
				lock.Lock()
				defer lock.Unlock()
				result[vTemp] = data
			})
	}
	wg.Wait()
	for _, abtScene := range abtSceneList {
		var v CommonAbtResult
		var ok bool
		if v, ok = result[abtScene]; !ok {
			continue
		}
		if len(v.AbtList) == 0 {
			continue
		}
		res.AbtList = append(res.AbtList, v.AbtList...)
	}
	t.Result(res)
}

func CommonAbtResultSrv(v string, uid int64, appClient *library.AppClient) CommonAbtResult {
	res := CommonAbtResult{
		AbtList: make([]*rop.SceneRop, 0),
	}
	sceneType, err := strconv.Atoi(v)
	if err != nil {
		return res
	}
	var sRop rop.SceneRop
	switch sceneType {
	case librop.ChannelPaymentPage:
		paymentPage, sRop := rop.Serv.GetPageAbt(uid, appClient)
		sRop.AbtContent = paymentPage.PageID
		res.AbtList = append(res.AbtList, &sRop)
	case librop.ChannelPlanGenerationPage:
		abtContentID := int64(0)
		abtContentID, sRop = rop.Serv.GetPlanGenerationPage(uid, appClient)
		sRop.AbtContent = abtContentID
		res.AbtList = append(res.AbtList, &sRop)
	default:
	}
	return res
}

type VipHistoryRsp struct {
	List []*equity.UserVipHistoryItem `json:"list,omitempty"`
}

func (*dfuser) UserVipHistory(t *http.Context) {
	sid := t.GetRequestStringD("sid", "")
	uid := srvuser.SrvLogin.SIDToUID(sid)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	rsp := &VipHistoryRsp{
		List: equity.GetUserVipHistory(uid),
	}
	t.Result(rsp)
}

type AccountUnlockParams struct {
	Phone   string `json:"phone"`
	VerCode int64  `json:"vercode"`
}

func (*dfuser) AccountUnlock(t *http.Context) {
	params := AccountUnlockParams{}
	t.ParseRequestStruct(&params)
	info := dbuser.TbDeviceLock.GetLockUserByMobile(params.Phone)
	if info == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	if info.ValidType != libuser.UserDeviceValidMobile {
		t.Result(errorcode.InvalidParams)
		return
	}
	if info.ValidResources != strconv.FormatInt(params.VerCode, 10) {
		t.Result(errorcode.InvalidParams, "验证码校验不正确")
		return
	}
	if !srvuser.UnlockDevice(info) {
		t.Result(errorcode.SystemError)
		return
	}
	info.OperateTime = time.Now().Unix()
	info.AdminName = "userOwn"
	info.LockStatus = library.No
	if err := info.Update(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

type ConfDataResp struct {
	ClearTime    string `json:"clear_time"`
	ClearURLTime string `json:"clear_url_time"`
}

func (*dfuser) ConfData(t *http.Context) {
	t.Result(ConfDataResp{
		ClearTime:    "********",
		ClearURLTime: "********",
	})
}

type SwitchReq struct {
	UID             int64  `json:"uid"`
	SID             string `json:"sid"`
	RecommendSwitch int32  `json:"recommend_switch"`
	AdSwitch        int32  `json:"ad_switch"`
	ProgramSwitch   int32  `json:"program_switch"`
}

func (*dfuser) UserSwitchInfo(t *http.Context) {
	sid := t.GetRequestStringD("sid", "")
	uid := srvuser.SrvLogin.SIDToUID(sid)
	appClient := util.GetClientInfo(t)
	switchInfo := srvuser.SrvLabel.GetUserSwitch(uid, appClient)
	t.Result(switchInfo)
}

func (*dfuser) UserSwitchChange(t *http.Context) {
	req := &SwitchReq{}
	t.ParseRequestStruct(req)
	uid := srvuser.SrvLogin.SIDToUID(req.SID)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	appClient := util.GetClientInfo(t)
	safelygo.GoSafelyByTraceID(func() {
		srvuser.SrvLabel.ChangeUserSwitch(uid, appClient, &srvuser.AccountSwitchInfo{
			RecommendSwitch: req.RecommendSwitch,
			AdSwitch:        req.AdSwitch,
			ProgramSwitch:   req.ProgramSwitch,
		})
	})
	t.Result(library.EmptyResponse{})
}

func (*dfuser) CollectUserInfo(t *http.Context) {
	uid := t.GetRequestInt64D("uid", 0)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	account := dbuser.TbAccount.GetUserByID(uid)
	if account == nil {
		t.Result(errorcode.HasNotLogin)
		return
	}
	rsp := &srvuser.CollectUserInfoRes{
		CollectList: make([]*srvuser.CollectUserInfoItem, 0),
	}
	collectLists, err := srvuser.CollectBaseUserInfo(account)
	if err != nil || len(collectLists) == 0 {
		t.Result(rsp)
		return
	}
	rsp.CollectList = collectLists
	t.Result(rsp)
}

type PrivacyAgreementResp struct {
	IPAddr string `json:"ip_addr"`
}

// PrivacyAgreement 用户隐私协议
func (*dfuser) PrivacyAgreement(t *http.Context) {
	appClient := util.GetClientInfo(t)
	res := &PrivacyAgreementResp{
		IPAddr: appClient.IPAds,
	}
	t.Result(res)
}

type DataResp struct {
	List []*DataItem `json:"data"`
}

type DataItem struct {
	Key   string `json:"key"`
	Label string `json:"label"`
	Value string `json:"value"`
}

type EquityItem struct {
	DurationValue int64  `json:"duration_value"`
	Desc          string `json:"desc"`
}

type SearchRPCResp struct {
	OrderID           string `json:"order_id"`             // 订单号
	OrderDesc         string `json:"order_desc"`           // 订单号
	PurchaseInfo      string `json:"purchase_info"`        // 购买信息
	SubscribeTypeDesc string `json:"subscribe_type_desc"`  // 订阅类型
	EquityDesc        string `json:"equity_desc"`          // 购买权益
	PayTypeDesc       string `json:"pay_type_desc"`        // 扣款方式
	PayTime           int64  `json:"pay_time"`             // 支付时间
	EffectiveDay      int64  `json:"effective_day"`        // 生效时间
	WelfareID         int64  `json:"welfare_id"`           // 瑜伽福利赛id
	RefundCount       int64  `json:"history_refund_count"` // 支付宝账号退款次数
}

func (*dfuser) GetUserData(t *http.Context) {
	sid := t.GetRequestStringD("sid", "")
	uid := srvuser.SrvLogin.SIDToUID(sid)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	appClient := util.GetClientInfo(t)
	var data DataResp
	// 调用 yoga-cs 获取信息
	address := fmt.Sprintf("http://"+config.Get().YogaCsAddress+"/api/order/list/rpc?search_content=%d", uid)
	res := DataResp{
		List: make([]*DataItem, 0),
	}
	header := h.Header{}
	header.Set("Content-Type", "application/x-www-form-urlencoded")
	header.Set("dy-request-type", "internal-go")
	body, err := util.HTTPRequest(address, "GET", header, nil)
	logger.Infof("调用 yoga-cs 获取信息 %s", string(body))
	if err != nil {
		logger.Info("调用 yoga-cs 获取信息 失败", err)
		t.Result(res)
		return
	}
	logger.Infof("调用 yoga-cs 获取信息 地址： %s", address)
	var result = new(struct {
		Status    int              `json:"status"`
		ErrorCode int              `json:"error_code"`
		ErrorDesc string           `json:"error_desc"`
		Response  []*SearchRPCResp `json:"result"`
	})
	if err = json.Unmarshal(body, &result); err != nil {
		logger.Info("调用yoga-cs 获取信息返回值解析失败", err)
		t.Result(res)
		return
	}
	if result.Status != 1 || result.ErrorCode != 0 || len(result.Response) == 0 {
		logger.Info("调用yoga-cs 获取信息返回值失败或为空", err)
		t.Result(res)
		return
	}
	if len(result.Response) == 0 {
		t.Result(res)
		return
	}

	for k, v := range result.Response {
		data.List = append(data.List,
			&DataItem{
				Key:   fmt.Sprintf("device_id_%d", k+1),
				Label: "设备ID",
				Value: appClient.DeviceID,
			},
			&DataItem{
				Key:   fmt.Sprintf("history_refund_count_%d", k+1),
				Label: "历史退款次数",
				Value: strconv.FormatInt(v.RefundCount, 10),
			},
			&DataItem{
				Key:   fmt.Sprintf("order_id_%d", k+1),
				Label: "订单号",
				Value: v.OrderDesc,
			},
			&DataItem{
				Key:   fmt.Sprintf("buy_info_%d", k+1),
				Label: "购买信息",
				Value: v.PurchaseInfo,
			},
			&DataItem{
				Key:   fmt.Sprintf("subscribe_type_%d", k+1),
				Label: "订阅信息",
				Value: v.SubscribeTypeDesc,
			},
			&DataItem{
				Key:   fmt.Sprintf("pay_type_%d", k+1),
				Label: "扣款方式",
				Value: v.PayTypeDesc,
			},
			&DataItem{
				Key:   fmt.Sprintf("buy_equity_%d", k+1),
				Label: "购买权益",
				Value: v.EquityDesc,
			})
		practiceInfo := getPracticeInfo(uid, v.PayTime)
		data.List = append(data.List, &DataItem{
			Key:   fmt.Sprintf("equity_%d", k+1),
			Label: "权益使用",
			Value: fmt.Sprintf("权益生效%d天，%s", v.EffectiveDay, practiceInfo),
		})
	}
	t.Result(data)
}

// nolint
func getPracticeInfo(uid, payTime int64) string {
	// 查询练习天数
	practiceData := srvp.SrvView.GetPracticeYear(uid, payTime)
	var practiceSeconds int // 练习分钟数
	// 查询练习过的课程
	endTime := time.Now()
	startTime := endTime.AddDate(0, -3, 0).Unix()
	list := practice.TbPracticeLog.GetLogByRange(uid, startTime, endTime.Unix(), 1, 100)
	res := make([]*srvp.PlayLog, 0)
	for k, v := range list {
		item := srvp.SrvLog.FormatPracticeLog(list[k])
		if item != nil {
			res = append(res, item)
		}
		practiceSeconds += v.PlayTime
	}
	practiceTime := int(math.Ceil(float64(practiceSeconds) / Seconds))
	return fmt.Sprintf("练习%d天，练习%d节课程，累计练习%d分钟", len(practiceData), len(res), practiceTime)
}

func (*dfuser) PagePlanGeneration(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	genderType := t.GetRequestIntD("gender", libuser.GenderWomen)
	if genderType == 0 {
		genderType = libuser.GenderWomen
	}
	sid := t.GetRequestStringD("sid", "")
	uid := srvuser.SrvLogin.SIDToUID(sid)
	t.Result(course.GetProgramFinishInfo(id, genderType, uid, util.GetClientInfo(t)))
}

type PageListResp struct {
	ObID     int64    `json:"id,omitempty"`
	PageList []string `json:"page_list,omitempty"`
	ObType   int      `json:"ob_type"`
}

func (*dfuser) GetObProcess(t *http.Context) {
	sid := t.GetRequestStringD("sid", "")
	uid := srvuser.SrvLogin.SIDToUID(sid)
	defUID := t.GetRequestInt64D("uid", 0)
	if uid == 0 && defUID != 0 {
		account := dbuser.TbAccount.GetUserByID(defUID)
		if account != nil {
			uid = defUID
		}
	}
	var res = &PageListResp{
		ObType: libob.ObTypeGeneral,
	}
	appClient := util.GetClientInfo(t)
	abtContentID, _ := rop.Serv.GetTallerObSwitch(uid, appClient)
	if abtContentID == library.Yes {
		res.ObType = libob.ObTypeTaller
	}
	t.Result(res)
}

type PrivacyProtocolRsp struct {
	Timestamp int64 `json:"timestamp"`
}

type PrivacyProtocolSwitch struct {
	Version   string `json:"version"`
	Timestamp int64  `json:"timestamp"`
}

func (*dfuser) GetPrivacyProtoCol(t *http.Context) {
	resp := &PrivacyProtocolRsp{}
	appClient := util.GetClientInfo(t)
	info := client.TbConfig.GetItemByKey(libc.PrivacyProtocolSwitch)
	if info == nil {
		t.Result(resp)
		return
	}
	privacyConfig := &PrivacyProtocolSwitch{}
	err := json.Unmarshal([]byte(info.Value), privacyConfig)
	if err != nil {
		logger.Error("解析隐私协议配置失败", err, info.Value)
		t.Result(resp)
		return
	}
	value := util.UVersion.Format(privacyConfig.Version)
	if !value.Success {
		logger.Errorf("解析隐私协议版本转换失败%v", value)
		t.Result(resp)
		return
	}
	if util.UVersion.LeVersion(appClient, value.Version, value.Version) {
		resp.Timestamp = privacyConfig.Timestamp
	}
	t.Result(resp)
}
