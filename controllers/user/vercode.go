package user

import (
	"fmt"
	"strconv"
	"time"

	"github.com/qiniu/go-sdk/v7/auth/qbox"
	"github.com/qiniu/go-sdk/v7/storage"
	"gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/client"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	dbuser "gitlab.dailyyoga.com.cn/server/children/databases/user"
	libcache "gitlab.dailyyoga.com.cn/server/children/library/cache"
	libuser "gitlab.dailyyoga.com.cn/server/children/library/user"
	"gitlab.dailyyoga.com.cn/server/children/service/user"
	"gitlab.dailyyoga.com.cn/server/children/service/util"
)

type vercode struct {
}

var DfVercode vercode

type GetVercodeReq struct {
	MobilePhone string `json:"mobile_phone"`
	SceneType   int    `json:"scene_type"`
	UID         int64  `json:"uid"`
	AnonymousID string `json:"anonymous_id"` // 神策匿名ID
}

// GetCode 获取验证码
// nolint
func (*vercode) GetCode(t *http.Context) {
	req := &GetVercodeReq{}
	t.ParseRequestStruct(req)
	reqTime := t.GetRequestInt64D("time", 0)
	if reqTime < (time.Now().Unix()-900)*1000 {
		t.Result(errorcode.InvalidParams)
		return
	}
	sign := t.GetRequestStringD("sign", "")
	if sign != "" {
		lockKey := libcache.LockGetVerCodeVal + sign
		expireTime := 1
		rd := cache.GetCRedis()
		if lock := rd.Lock(lockKey, cache.LockExpiration(time.Duration(expireTime)*time.Minute)); !lock {
			t.Result(library.EmptyResponse{})
			return
		}
	}
	req.MobilePhone = user.GetMobilePhone(t, req.MobilePhone)
	if req.SceneType == libuser.VercodeTypeLogoff || req.SceneType == libuser.VercodeTypeResetPassword {
		account := dbuser.TbAccount.GetUserByID(req.UID)
		if account != nil {
			req.MobilePhone = account.Mobile
		}
	}
	if req.MobilePhone == "" || req.SceneType == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	if req.SceneType != libuser.VercodeDeviceUnlock {
		// 设备登录限制
		account := dbuser.TbAccount.GetUserByMobile(req.MobilePhone)
		if account != nil {
			if errCode := user.IsDeviceLock(account.ID); errCode != errorcode.Success {
				t.Result(errCode)
				return
			}
		}
	}
	ecode := user.SrvVercode.VerifyParamsValid(req.MobilePhone, req.SceneType)
	if ecode > 0 {
		logger.Warn(ecode, req.MobilePhone, req.SceneType)
		t.Result(ecode)
		return
	}
	// 生成验证码
	code := user.SrvVercode.GenerateCode()
	if code == 0 {
		t.Result(errorcode.FailGetVercode)
		return
	}
	if req.SceneType == libuser.VercodeDeviceUnlock {
		lockItem := dbuser.TbDeviceLock.GetLockUserByMobile(req.MobilePhone)
		if lockItem == nil || lockItem.ValidType != libuser.UserDeviceValidMobile {
			t.Result(errorcode.InvalidParams, "当前手机号关联账号未锁定")
			return
		}
		lockItem.ValidResources = strconv.Itoa(code)
		if err := lockItem.Update(); err != nil {
			logger.Error(err)
			t.Result(errorcode.DBError)
			return
		}
	}
	// 发送验证码
	err := user.SrvVercode.SendMsgCode(req.MobilePhone, code)
	if err != nil {
		logger.Error(err)
		t.Result(errorcode.FailSendVercode)
		return
	}
	err = user.SrvVercode.SaveVercode(req.MobilePhone, req.SceneType, code)
	if err != nil {
		logger.Error(err)
		t.Result(errorcode.FailSendVercode)
		return
	}
	t.Result(library.EmptyResponse{})
}

type CheckVercodeReq struct {
	MobilePhone string `json:"mobile_phone"`
	SceneType   int    `json:"scene_type"`
	Code        int    `json:"mobile_vercode"`
	UID         int64  `json:"uid"`
}

// CheckCode 单纯检查验证码是否正确但不更新数据状态
func (*vercode) CheckCode(t *http.Context) {
	req := &CheckVercodeReq{}
	t.ParseRequestStruct(req)
	req.MobilePhone = user.GetMobilePhone(t, req.MobilePhone)
	if req.SceneType == libuser.VercodeTypeLogoff || req.SceneType == libuser.VercodeTypeResetPassword {
		account := dbuser.TbAccount.GetUserByID(req.UID)
		if account != nil {
			req.MobilePhone = account.Mobile
		}
	}
	if req.MobilePhone == "" || req.SceneType == 0 || req.Code == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	if !user.SrvVercode.CheckVercode(req.MobilePhone, req.Code, req.SceneType) {
		t.Result(errorcode.VercodeError)
		return
	}
	t.Result(library.EmptyResponse{})
}

func (*vercode) QueryToken(t *http.Context) {
	sid := t.GetRequestStringD("sid", "")
	uid := user.SrvLogin.SIDToUID(sid)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	nums := t.GetRequestIntD("num", 1)
	ignoreInspect := t.GetReuqestBoolD("ignoreInspect", false)
	// 增加图片上传
	clientTime := t.GetRequestInt64D("time", 0)
	if clientTime < 1 {
		t.Result(errorcode.InvalidParams)
		return
	}
	now := time.Now().Unix()
	if clientTime < now-util.SecondsPerHour {
		t.Result(errorcode.InvalidParams)
		return
	}
	uniqueKey := fmt.Sprintf("%s:%d:%d", libcache.LockQiniuQuery, uid, clientTime)
	if !cache.GetCRedis().Lock(uniqueKey, cache.LockExpiration(time.Hour)) {
		logger.Warnf("QueryToken 调用异常 重复, UID: %d 客户端time: %d", uid, clientTime)
		t.Result(errorcode.InvalidParams, "请求参数异常")
		return
	}
	mimeType := "image/*"
	putPolicy := storage.PutPolicy{
		Scope:     client.QiniuResourceBucket,
		MimeLimit: mimeType,
	}

	type uploadToken struct {
		ResourceKey string `json:"resource_key"`
		ImageURL    string `json:"image_url"`
		Token       string `json:"token"`
	}
	type TokenResponse struct {
		StorageZone []string      `json:"storage_zone"`
		Tokens      []uploadToken `json:"tokens"`
	}
	res := TokenResponse{
		StorageZone: []string{
			"upload.qiniup.com",
		},
		Tokens: make([]uploadToken, 0),
	}
	length := 32
	for i := 0; i < nums; i++ {
		putPolicy.SaveKey = client.FileTypeDir[client.FileTypeEnum.Image] + "/" + util.GetRandomString(length)
		if ignoreInspect {
			putPolicy.SaveKey += "xayl"
		}
		putPolicy.Expires = 300
		cfg := config.Get().QiniuCDN
		mac := qbox.NewMac(cfg.AccessKey, cfg.AccessSecret)
		token := putPolicy.UploadToken(mac)
		res.Tokens = append(res.Tokens, uploadToken{
			ResourceKey: putPolicy.SaveKey,
			Token:       token,
			ImageURL:    client.QiniuResourceDomain + "/" + putPolicy.SaveKey,
		})
	}
	t.Result(res)
}
