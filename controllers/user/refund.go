package user

import (
	"errors"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/databases/order"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	"gitlab.dailyyoga.com.cn/server/children/library/pay"
	"gitlab.dailyyoga.com.cn/server/children/service/assist"
	srvuser "gitlab.dailyyoga.com.cn/server/children/service/user"
)

type SubmitRefundReq struct {
	SID             string  `json:"sid"`
	UID             int64   `json:"uid"`
	NickName        string  `json:"nick_name"`
	PhoneNumber     string  `json:"phone_number"`
	DebitMethod     int     `json:"debit_method"`
	DebitTime       string  `json:"debit_time"`
	DebitAmount     float32 `json:"debit_amount"`
	OrderID         string  `json:"order_id"`
	SpecificIssues  string  `json:"specific_issues"`
	OrderPictureURL string  `json:"order_picture_url"`
}

func SubmitRefund(t *http.Context) {
	params := &SubmitRefundReq{}
	t.ParseRequestStruct(params)
	uid := srvuser.SrvLogin.SIDToUID(params.SID)
	if uid == 0 {
		t.Result(errorcode.HasNotLogin)
		return
	}
	if err := checkRefundOrder(params); err != nil {
		logger.Warn("用户自主提交退款失败，校验失败", params.OrderID, uid)
		if err.Error() == "订单号无效" || err.Error() == "用户不匹配" {
			t.Result(errorcode.SubmitRefundFail)
		} else {
			t.Result(errorcode.InvalidParams, err.Error())
		}
		return
	}
	isAutoRefund := library.No
	if CheckAutoRefund(params.OrderID) {
		isAutoRefund = library.Yes
	}
	refundItem := &order.RefundUser{
		UID:               params.UID,
		OrderID:           params.OrderID,
		PhoneNumber:       params.PhoneNumber,
		NickName:          params.NickName,
		DebitMethod:       int32(params.DebitMethod),
		DebitTime:         params.DebitTime,
		DebitAmount:       params.DebitAmount,
		SpecificIssues:    params.SpecificIssues,
		OrderPictureURL:   params.OrderPictureURL,
		CommunicateStatus: int32(pay.CommunicateStatusEnum.Wait),
		IsAutoRefund:      int32(isAutoRefund),
	}
	if err := refundItem.Save(); err != nil {
		logger.Error(err)
		t.Result(errorcode.DBError)
		return
	}
	t.Result(library.EmptyResponse{})
}

func checkRefundOrder(params *SubmitRefundReq) error {
	orderItem := order.TbWebOrder.GetItemByOrderID(params.OrderID)
	if orderItem == nil {
		return errors.New("订单号无效")
	}
	if orderItem.OrderStatus != library.Yes {
		return errors.New("订单号无效")
	}
	if orderItem.UID != params.UID {
		return errors.New("用户不匹配")
	}
	if orderItem.PayType == pay.PayTypeApple {
		return errors.New("请联系客服")
	}
	refundList := order.TbRefund.GetRefundListByOrderID(orderItem.OrderID)
	if len(refundList) > 0 {
		for _, v := range refundList {
			if v.RefundStatus == int(pay.RefundStatusEnum.Wait) {
				return errors.New("该订单已提交退款 预计2个工作日完成处理 请勿重复提交")
			}
			if v.RefundStatus == int(pay.RefundStatusEnum.Success) {
				return errors.New("该订单已有过退款记录 如有问题可联系客服")
			}
		}
	}
	refundUser := order.TbRefundUser.GetRefundUserItem(orderItem.OrderID)
	if refundUser != nil && refundUser.CommunicateStatus != int32(pay.CommunicateStatusEnum.Ignore) {
		return errors.New("该订单已提交退款 预计2个工作日完成处理 请勿重复提交")
	}
	return nil
}

func CheckAutoRefund(orderID string) bool {
	refundUnsubscribeQuota := assist.LimitAssist.Get()
	refundList := order.TbRefund.GetRefundListByOrderID(orderID)
	if len(refundList) > 0 {
		return false
	}
	refundUserList := order.TbRefundUser.GetRefundUserItemByOrderID(orderID)
	if len(refundUserList) > 1 {
		return false
	}
	if refundUnsubscribeQuota == nil || refundUnsubscribeQuota.AutoRefundReviewUser != library.Yes {
		return false
	}
	orderItem := order.TbWebOrder.GetItemByOrderID(orderID)
	if orderItem == nil {
		return false
	}
	if time.Now().Unix()-orderItem.CreateTime > int64(refundUnsubscribeQuota.OrderTimeLimitDay)*library.DayTime {
		return false
	}
	return true
}
