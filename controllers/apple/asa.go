package apple

import (
	"encoding/json"
	"strconv"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"

	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	"gitlab.dailyyoga.com.cn/server/children/service/app"
)

func AdsInfo(t *http.Context) {
	params := &app.AdsInfoAdServiceReq{}
	t.ParseRequestStruct(params)
	if params.Data == "" || params.DistinctID == "" {
		t.Result(errorcode.InvalidParams)
		return
	}
	data := params.Data
	distinctID := params.DistinctID
	isLogin := isNum(distinctID)
	var payload app.AdsInfoAdService
	err := json.Unmarshal([]byte(data), &payload)
	if err != nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	temp := &payload
	temp.DistinctID = distinctID
	temp.IsLogin = isLogin
	logger.Infof("ads temp %+v", temp)
	safelygo.GoSafelyByTraceID(func() {
		temp.UploadScAppleAdsInfoAdService()
	})
	t.Result(library.EmptyResponse{})
}

func isNum(s string) bool {
	_, err := strconv.ParseFloat(s, 64)
	return err == nil
}
