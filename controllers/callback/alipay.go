package callback

import (
	"context"
	"strings"

	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	pbgrpc "gitlab.dailyyoga.com.cn/protogen/pay-bridge-go/paybridge"
	"gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/grpc"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libpay "gitlab.dailyyoga.com.cn/server/children/library/pay"
	"gitlab.dailyyoga.com.cn/server/children/service/app"
	srvorder "gitlab.dailyyoga.com.cn/server/children/service/order"
	"gitlab.dailyyoga.com.cn/server/children/service/paymid"
	"gitlab.dailyyoga.com.cn/server/gopay"
	"gitlab.dailyyoga.com.cn/server/gopay/alipay"
)

type _alipay struct{}

var Alipay _alipay

const (
	AlipayNotifySuccess      = "success"
	AlipayNotifyFail         = "fail"
	AlipayTradeNotifyFinish  = "TRADE_FINISHED"
	AlipayTradeNotifySuccess = "TRADE_SUCCESS"
)

// TradePayNotify 支付宝付款回调
func (*_alipay) TradePayNotify(t *http.Context) {
	notifyReq, ok := verifyAlipaySign(t)
	if !ok {
		WriteRespose(t, AlipayNotifyFail)
		return
	}
	if notifyReq["trade_status"] != AlipayTradeNotifyFinish && notifyReq["trade_status"] != AlipayTradeNotifySuccess {
		WriteRespose(t, AlipayNotifySuccess)
		return
	}
	orderID, ok := notifyReq["out_trade_no"].(string)
	if !ok {
		logger.Error("【支付宝】回调获取out_trade_no获取失败", notifyReq)
		WriteRespose(t, AlipayNotifyFail)
		return
	}
	data, err := paymid.LogCompleteOrderNotify(orderID, libpay.PayTypeAlipay, notifyReq)
	if err != nil {
		logger.Error(err)
		WriteRespose(t, AlipayNotifyFail)
		return
	}
	if data != nil && data.IsProcessed == library.No {
		if !srvorder.CompleteOrder(data, true) {
			WriteRespose(t, AlipayNotifyFail)
			return
		}
	}
	WriteRespose(t, AlipayNotifySuccess)
}

// AgreementSign 支付宝签约解约回调
func (*_alipay) AgreementSign(t *http.Context) {
	notifyReq, ok := verifyAlipaySign(t)
	logger.Info("支付宝网关通知信息", notifyReq, ok)
	if !ok {
		WriteRespose(t, AlipayNotifyFail)
		return
	}
	switch notifyReq["msg_method"] {
	case "alipay.fund.trans.order.changed":
		_, err := grpc.GetPayBridgeClient().TransferAliPayReturn(context.Background(),
			&pbgrpc.TransferAliPayReturnReq{
				RequestBody: notifyReq.JsonBody(),
			})
		if err != nil {
			logger.Warnf("支付宝资金状态变更：%s err:%s", notifyReq.JsonBody(), err.Error())
			WriteRespose(t, AlipayNotifyFail)
		}
		WriteRespose(t, AlipayNotifySuccess)
		return
	case "alipay.merchant.tradecomplain.changed":
		logger.Info("支付宝网关通知投诉信息", notifyReq)
		bizContent := notifyReq["biz_content"].(string)
		appID := notifyReq["app_id"].(string)
		app.AlipayComplaint(bizContent, appID)
		WriteRespose(t, AlipayNotifySuccess)
		return
	}

	agreeno, ok := notifyReq["external_agreement_no"].(string)
	if !ok {
		logger.Errorf("支付宝回调获取参数失败 %v", notifyReq)
		WriteRespose(t, AlipayNotifyFail)
		return
	}
	if config.Get().Service.Env != microservice.Dev && strings.Contains(agreeno, "dev") {
		logger.Warn("支付宝签约回调测试环境调到线上", notifyReq)
		WriteRespose(t, AlipayNotifySuccess)
		return
	}
	if srvorder.SrvOrderComplete.AlipayContract(notifyReq, libpay.SubscribeModeEnum.OnlySubscribe) {
		WriteRespose(t, AlipayNotifySuccess)
		return
	}
	WriteRespose(t, AlipayNotifyFail)
}

// AgreementPaySign 支付宝签约回调 支付中签约模式
func (*_alipay) AgreementPaySign(t *http.Context) {
	notifyReq, ok := verifyAlipaySign(t)
	if !ok {
		WriteRespose(t, AlipayNotifyFail)
		return
	}
	agreeno, ok := notifyReq["external_agreement_no"].(string)
	if !ok {
		logger.Errorf("支付宝回调获取参数失败 %v", notifyReq)
		WriteRespose(t, AlipayNotifyFail)
		return
	}
	if config.Get().Service.Env != microservice.Dev && strings.Contains(agreeno, "dev") {
		logger.Warn("支付宝签约回调测试环境调到线上", notifyReq)
		WriteRespose(t, AlipayNotifySuccess)
		return
	}
	if srvorder.SrvOrderComplete.AlipayContract(notifyReq, libpay.SubscribeModeEnum.PayAndSubscribe) {
		WriteRespose(t, AlipayNotifySuccess)
		return
	}
	WriteRespose(t, AlipayNotifyFail)
}

func (*_alipay) TradePayCharge(t *http.Context) {
	notifyReq, ok := verifyAlipaySign(t)
	if !ok {
		WriteRespose(t, AlipayNotifyFail)
		return
	}
	if notifyReq["trade_status"] != "TRADE_FINISHED" && notifyReq["trade_status"] != "TRADE_SUCCESS" {
		// 非成功订单回调返回成功
		WriteRespose(t, AlipayNotifySuccess)
		return
	}
	orderID, ok := notifyReq["out_trade_no"].(string)
	if !ok {
		logger.Error("【支付宝】回调获取out_trade_no获取失败", notifyReq)
		WriteRespose(t, AlipayNotifyFail)
		return
	}
	data, err := paymid.LogCompleteOrderNotify(orderID, libpay.PayTypeAlipay, notifyReq)
	if err != nil {
		logger.Error(err)
		WriteRespose(t, AlipayNotifyFail)
		return
	}
	// 已处理请求直接返回成功
	if data != nil && data.IsProcessed == library.Yes {
		WriteRespose(t, AlipayNotifySuccess)
		return
	}
	// 未处理请求处理
	if srvorder.SrvOrderComplete.AlipayTradeCharge(notifyReq, data) {
		WriteRespose(t, AlipayNotifySuccess)
		return
	}
	WriteRespose(t, AlipayNotifyFail)
}

// verifyAlipaySign 解析支付宝参数，验证签名
func verifyAlipaySign(t *http.Context) (gopay.BodyMap, bool) {
	// 解析异步通知的参数
	notifyReq, err := alipay.ParseNotifyToBodyMap(t.Request)
	logger.Info("支付宝签约回调内容：", notifyReq)
	if err != nil {
		logger.Errorf("支付宝回调解析失败 %v", t.Request)
		return nil, false
	}
	appID, ok := notifyReq["app_id"].(string)
	if !ok {
		logger.Error("【支付宝】回调获取APP_ID失败", notifyReq)
		return nil, false
	}
	cfg := config.Get().GetAlipayKeyByAppID(appID)
	if cfg == nil {
		logger.Error("【支付宝】回调获取APP_ID配置失败", notifyReq)
		return nil, false
	}
	// 支付宝异步通知验签（公钥模式）
	if cfg.IsCertMode {
		// 支付宝异步通知验签（证书模式）
		ok, err = alipay.VerifySignWithCert([]byte(cfg.AliPayPublicCertKey), notifyReq)
		if err != nil {
			logger.Errorf("支付宝回调证书模式验签失败 %v", err.Error())
			return nil, false
		}
	} else {
		// 支付宝异步通知验签（公钥模式）
		ok, err = alipay.VerifySign(cfg.AlipayPublicKey, notifyReq)
		if err != nil {
			logger.Errorf("支付宝回调公钥模式验签失败 %v", t.Request)
			return nil, false
		}
	}
	return notifyReq, ok
}

// nolint
func WriteRespose(t *http.Context, r string) {
	rw := t.Writer
	var err error
	_, err = rw.Write([]byte(r))
	if err != nil {
		logger.Warn("Write返回错误", err)
	}
}
