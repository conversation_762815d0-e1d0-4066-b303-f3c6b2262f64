package callback

import (
	"encoding/json"
	"fmt"
	hh "net/http"

	"github.com/awa/go-iap/appstore"
	"github.com/gin-gonic/gin"
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/server/children/databases/iap"
	"gitlab.dailyyoga.com.cn/server/children/library"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	"gitlab.dailyyoga.com.cn/server/children/service/order"
)

// nolint
func ServerNotify(t *http.Context) {
	requstBody, _ := t.GetRawData()
	logger.Info("IOS IAP 通知", string(requstBody))
	noticeData := fmt.Sprintf("%s", requstBody)
	messageData := order.ParseIosNotificationData(noticeData)
	if messageData != nil {
		item := &iap.Notification{
			NotificationType:    string(messageData.NotificationType),
			NotificationContent: noticeData,
			IsDeal:              library.No,
		}
		if err := item.Save(); err != nil {
			logger.Error(err)
		}
		safelygo.GoSafelyByTraceID(
			func() {
				order.DealIOSNotification(item)
			},
		)
		WriteRespose(t, "OK")
		return
	}
	t.Result(errorcode.SystemError)
}

func ServerNotifyV2(t *http.Context) {
	requestBody, _ := t.GetRawData()
	logger.Info("IOS IAP v2 通知", string(requestBody))
	noticeData := string(requestBody)
	messageData := order.ParseIosV2NotificationData(noticeData)
	if messageData != nil {
		claims, err := order.ParseNotificationData(messageData.SignedPayload)
		if err != nil {
			t.JSON(hh.StatusBadRequest, gin.H{})
			return
		}
		notificationData, _ := json.Marshal(claims)
		pResult := appstore.SubscriptionNotificationV2DecodedPayload{}
		err = json.Unmarshal(notificationData, &pResult)
		if err != nil {
			t.JSON(hh.StatusBadRequest, gin.H{})
			return
		}
		item := &iap.Notification{
			NotificationType:    string(pResult.NotificationType),
			NotificationContent: string(notificationData),
			Subtype:             string(pResult.Subtype),
			IsDeal:              library.No,
		}
		if err := item.Save(); err != nil {
			logger.Error(err)
		}
		safelygo.GoSafelyByTraceID(func() {
			order.DealV2IOSNotification(item)
		})
		WriteRespose(t, "")
		return
	}
	t.JSON(hh.StatusOK, gin.H{})
}

func LookUpIosIAPOrder(t *http.Context) {
	orderID := t.GetRequestStringD("order_id", "")
	if orderID == "" {
		t.Result(errorcode.InvalidParams)
		return
	}
	resp := order.LookUpOrderID(orderID)
	if resp == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	t.Result(resp)
}
