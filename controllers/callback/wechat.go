package callback

import (
	"encoding/json"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/library"
	libpay "gitlab.dailyyoga.com.cn/server/children/library/pay"
	srvorder "gitlab.dailyyoga.com.cn/server/children/service/order"
	"gitlab.dailyyoga.com.cn/server/children/service/paymid"
	wx3 "gitlab.dailyyoga.com.cn/server/gopay/wechat/v3"
)

type weixin struct{}

var Weixin weixin

const WeixinSuccessMsg = `<xml>
<return_code><![CDATA[SUCCESS]]></return_code>
<return_msg><![CDATA[OK]]></return_msg>
</xml>`
const WeixinFailMsg = `<xml>
<return_code><![CDATA[FAIL]]></return_code>
<return_msg><![CDATA[验证失败]]></return_msg>
</xml>`

const WeixinV3SuccessMsg = `{"code": "SUCCESS"}`
const WeixinV3FailMsg = `{"code": "FAIL"}`

func (*weixin) UnifiedOrderNotify(t *http.Context) {
	wechatPay := config.Get().WechatPay
	notifyReq, err := wx3.V3ParseNotify(t.Request)
	if err != nil {
		logger.Error("微信v3回调通知异常", err)
		WriteRespose(t, WeixinV3FailMsg)
		return
	}
	var notifyData *wx3.V3DecryptResult
	notifyData, err = notifyReq.DecryptCipherText(wechatPay.APIV3Key)
	if err != nil {
		logger.Error("微信v3回调通知解密异常", err, notifyReq)
		WriteRespose(t, WeixinV3FailMsg)
		return
	}
	if notifyData.OutTradeNo == "" {
		WriteRespose(t, WeixinV3FailMsg)
		return
	}
	var mapResult map[string]interface{}
	resultBytes, err := json.Marshal(notifyData)
	if err != nil {
		logger.Error(err)
		WriteRespose(t, WeixinV3FailMsg)
		return
	}
	if err = json.Unmarshal(resultBytes, &mapResult); err != nil {
		WriteRespose(t, WeixinV3FailMsg)
		return
	}
	data, err := paymid.LogCompleteOrderNotify(notifyData.OutTradeNo, libpay.PayTypeWechat, mapResult)
	// 记录失败返回失败，记录成功返回成功
	if err != nil {
		logger.Error(err)
		WriteRespose(t, WeixinV3FailMsg)
		return
	}
	// 记录成功且状态为未处理
	if data != nil && data.IsProcessed == library.No {
		if !srvorder.CompleteOrder(data, true) {
			WriteRespose(t, WeixinV3FailMsg)
			return
		}
	}
	WriteRespose(t, WeixinV3SuccessMsg)
}
