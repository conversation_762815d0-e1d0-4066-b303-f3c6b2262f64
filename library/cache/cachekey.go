package cache

import "time"

// 普通key
// nolint
const (
	SensorSignUpProfileSet = "Cs:Sensor:SignUpProfile:Set"
	ModifyInfoLimitRate    = "Cs:Limit:Rate:Modify:Info"

	// 用户最近练习
	UserLastPracticeInfo = "Cs:User:LastPractice:Course"
	// sid 有效期 30天
	LoginSIDExpirationTime        = 30 * 86400 * time.Second
	LoginSIDCacheKeyPrefix        = "Cs:sid:"
	DataCheckAlipayContractStatus = "Cs:Data:AlipayContract:Status"
	WxAccessToken                 = "Cs:Wx:AccessToken:Default"
	WxJsTicket                    = "Cs:Wx:JsTicket:Default"
	DataRefundUnsubscribeQuota    = "Cs:Today:Refund:Unsubscribe:Quota"
	TradeComplainMsg              = "Set:TradeComplain"
	UIDBindSidProfile             = "Cs:UIDBindSidProfile"
	// 支付宝扣款当天总数量
	AlipayChargeDayTotal = "Cs:Cron:AlipayCharge:DayTotal"
	// 支付宝扣款当天已扣款数量
	AlipayChargeDayHasCharge = "Cs:Cron:AlipayCharge:DayHasCharge"
	// 锁定用户集合key
	UIDByDeviceIDLock = "Cs:deviceLock"
	// 用户设备列表
	DeviceLimitCacheKeyPrefix = "Cs:deviceLimit:"
	// 用户在线数
	UserOnlineCacheKeyPrefix = "Cs:deviceOline:"
	// 阶梯扣费延时队列
	StepDelayCharge = "Cs:StepDelayCharge:ZSet"
	// 节假日列表缓存
	YearHolidayList = "Cs:YearHolidayList:string"
	// 获取部分用户信息
	UserPartInformation = "Cs:UserPartInformation:string"
	// 设置订单的付费页ID
	OrderPaymentPageID = "Cs:OrderPaymentPageID:"
	// 付费方案页更新次数
	DealUpdateTimes = "Cs:DealUpdateTimes:"
	// 会员状态
	UserVipStatus = "Cs:UserVipStatus:"
	// 历史购买次数
	UserOldBuyNum = "Cs:UserOldBuyNum:"
	// 用户练习课程数量
	UniqueCourseIDs = "Cs:UniqueCourseIDs"
	// 巨量广告激活数据
	OceanEnginePrefix = "Cs:ocean:engine:"
)

// 锁
const (
	// 注册属性上报
	LockCronSignUpProfile = "Cs:Lock:Cron:SignUpProfile"
	// 支付宝签约告警
	LockCronAlipayContractWarning = "Cs:Lock:Cron:AlipayContractWarning"
	// 获取七牛token
	LockQiniuQuery = "Cs:Lock:Qiniu:Query:Token"
	// 支付宝订阅状态检查
	LockCheckAlipayContractStatus = "Cs:Lock:Check:AlipayContractStatus"
	// 处理IOS客户端上报完成订单
	LockDealIOSReceiptLog = "Cs:Lock:Deal:IOS:IAP:Receipt:Log"
	// 根据最后一条交易处理续订锁
	LockRenewIOSTransaction = "Cs:Lock:Renew:IOS:Transaction"
	// 处理IOS交易锁
	LockGenerateIOSTransaction = "Cs:Lock:Generate:IOS:Transaction"
	// 处理IOS服务端通知
	LockDealIOSNotificationLog = "Cs:Lock:Deal:IOS:IAP:Notification:Log"
	// 支付宝签约扣款
	LockCronAlipayContractCharge = "Cs:Lock:Cron:AlipayContractCharge"
	// 支付优惠宝签约扣款 0731
	LockCronAlipayOfferContractCharge = "Cs:Lock:Cron:AlipayOfferContractCharge"
	// IOS客户端上报重试处理脚本
	LockCronIOSClientReceiptRetry = "Cs:Lock:Cron:IOSClientReceiptRetry"
	// IOS通知处理重试脚本
	LockCronIOSNotificationRetry = "Cs:Lock:Cron:IOSNotificationRetry"
	// IOS续订脚本
	LockCronIOSRenewCheck = "Cs:Lock:Cron:IOSRenewCheck"
	// 权益转移
	LockEquityTransferDistinct = "Cs:Lock:EquityTransferDistinct"
	// 用户渠道来源
	LockUIDADChannel = "Cs:Lock:UIDADChannel"
	// 设备渠道来源
	LockDeviceADChannel = "Cs:Lock:DeviceADChannel"
	// 练习上报锁
	LocalPracticeReport = "Cs:Lock:PracticeReport"
	// 会员到期上报
	LockProfileUserVipExpired = "Cs:Lock:UserVipExpired"
	// 会员变更
	LockChangeUserVip = "Cs:Lock:ChangeUserVip"
	// 合规上报
	LockComplianceEvent = "Cs:Lock:ComplianceEvent"
	// 同步用户练习数据
	LockSyncUserPracticeLog = "Cs:Lock:SyncUserPracticeLog"
	// 每日扣款计划生成
	LockDailyChargePlan = "Cs:Lock:Cron:DailyChargePlan"
	// 每日扣款计划报告
	LockDailyChargePlanReport = "Cs:Lock:Cron:DailyChargePlanReport"
	// 挑战赛奖励发放
	LockChallengeRefundAward = "Cs:Lock:ChallengeRefundAward"
	// 挑战赛退款脚本
	LockChallengeRefund = "Cs:Lock:Cron:ChallengeRefund"
	// 挑战赛神策上报脚本
	LockChallengeSensorReport = "Cs:Lock:Cron:ChallengeSensorReport"
	// 订阅扣款锁
	LockAlipaySubscribeCharge = "Cs:Lock:AlipaySubscribeChargeUID"
	// 阶梯扣款重试锁
	LockStepDelayCharge = "Cs:Lock:Cron:StepDelayCharge"
	// 挑战赛退款脚本
	LockChallengeAutoEquity = "Cs:Lock:Cron:LockChallengeAutoEquity"
	// 防止验证码被刷
	LockGetVerCodeVal = "Cs:Lock:GetVerCode"
	// 抖音用户登陆加锁
	LockTiktokLogin = "Cs:Lock:TiktokLogin"
	// 设置抖音用的code2Session
	TiktokCode2Session = "Cs:TiktokCode2Session:string"
	// tiktok签约签约扣款
	LockCronTiktokContractCharge = "Cs:Lock:Cron:TiktokContractCharge"
	// tiktok订阅扣款锁
	LockTiktokPaySubscribeCharge = "Cs:Lock:TiktokSubscribeChargeUID"
	// CoursePlayCount30Days 课程近30天播放量
	CoursePlayCount30Days = "course:play:count:30d:"
	// CoursePlayTotal30Days 课程近30天总播放量
	CoursePlayTotal30Days = "course:play:total:30d"
)

const (
	BytePromotion = "byte:promotion:Cs:"
)
