package client

import (
	"gitlab.dailyyoga.com.cn/server/children/library/pay"
)

const (
	ChannelOther         = "-1"
	ChannelHuawei        = "1600001"
	ChannelOPPO          = "1600002"
	ChannelVIVO          = "1600003"
	ChannelXiaomi        = "1600004"
	ChannelTencent       = "1600005"
	ChannelBaidu         = "1600006"
	Channel360           = "1600007"
	ChannelMeizu         = "1600008"
	ChannelLianxiang     = "1600009"
	ChannelPP            = "1600010"
	ChannnelSamsung      = "1600011"
	ChannelLittle        = "1600012"
	ChannelHonor         = "1600013"
	ChannelJuliang       = "1600014"
	ChannelApple         = "1700001"
	ChannelWechatMiNiApp = "100-1"
	ChannelTiktok        = "100-2"
	ChannelHarmonyHuawei = "100-3"
)

var ChannelName = map[string]string{
	ChannelHuawei:    "CS_huawei",        // 华为
	ChannelOPPO:      "CS_oppo",          // OPPO
	ChannelVIVO:      "CS_vivo",          // VIVO
	ChannelXiaomi:    "CS_xiaomi",        // 小米
	ChannelTencent:   "CS_tencent",       // 腾讯
	ChannelBaidu:     "CS_baidu",         // 百度
	Channel360:       "CS_qihu360",       // 360
	ChannelMeizu:     "CS_meizu",         // 魅族
	ChannelLianxiang: "CS_lenovo",        // 联想
	ChannelPP:        "CS_PP",            // PP助手
	ChannnelSamsung:  "CS_samsung",       // 三星
	ChannelLittle:    "CS_littleChannel", // 小渠道统一包
	ChannelHonor:     "CS_honor",         // 荣耀
	ChannelApple:     "CS_iOS",           // 苹果
	ChannelJuliang:   "CS_juliang",       // 巨量
}

type DeviceTypeInt int

var DeviceTypeEnum = struct {
	Unknown DeviceTypeInt
	Android DeviceTypeInt
	IOS     DeviceTypeInt
	Harmony DeviceTypeInt
}{
	Unknown: 0,
	Android: 1,
	IOS:     2,
	Harmony: 3,
}
var DeviceTypeDesc = map[DeviceTypeInt]string{
	DeviceTypeEnum.Unknown: "unknown",
	DeviceTypeEnum.Android: "android",
	DeviceTypeEnum.IOS:     "ios",
	DeviceTypeEnum.Harmony: "Harmony", // 建议了小写，数据说按文档
}

type OppoOcpxType int

// OppoOcpxTypeEnum OPPO OPCX数据上报类型
var OppoOcpxTypeEnum = struct {
	AppActivation OppoOcpxType // app激活
	Register      OppoOcpxType // 注册
	Purchase      OppoOcpxType // 应用内付费
	CustomGoal    OppoOcpxType // 自定义目标
}{
	AppActivation: 1,
	Register:      2,
	Purchase:      7,
	CustomGoal:    8,
}

func (e OppoOcpxType) ToInt64() int64 {
	return int64(e)
}

func GetDefaultPageID(dType int) int {
	if DeviceTypeInt(dType) == DeviceTypeEnum.IOS {
		return pay.DefaultIOSPayPageID
	}
	return pay.DefaultAndroidPayPageID
}

const DeviceTypeEnumAll = 99

type FileType int

var FileTypeEnum = struct {
	Image FileType
	Video FileType
	Audio FileType
}{
	Image: 1,
	Video: 2,
	Audio: 3,
}

var FileTypeDir = map[FileType]string{
	FileTypeEnum.Image: "image",
	FileTypeEnum.Video: "video",
	FileTypeEnum.Audio: "audio",
}

const (
	QiniuResourceDomain     = "https://qnchildren.childrenworkout.com.cn"
	QiniuResourceDomainTmp  = "cimage.childrenworkout.com.cn"
	QiniuResourceBucket     = "dailychildren"
	QiniuTokenDefaultExpire = 300
)

// AB 测试场景
const (
	ABTestPayPageSceneID = 20000 // 硬汉项目OB付费页
)

var DeviceTypeToPayType = map[DeviceTypeInt][]int{
	DeviceTypeEnum.Unknown: {pay.PayTypeAlipay, pay.PayTypeWechat},
	DeviceTypeEnum.Android: {pay.PayTypeAlipay, pay.PayTypeWechat},
	DeviceTypeEnum.IOS:     {pay.PayTypeAlipay, pay.PayTypeApple, pay.PayTypeWechat},
	DeviceTypeEnum.Harmony: {pay.PayTypeAlipay},
}

const (
	UserChanneDef = iota
	UserChannelJuLiang
	UserChannelHuawei
)

var ChannelADList = []int{
	UserChannelJuLiang,
	UserChannelHuawei,
}

var UserPredictChannelMap = map[int]int{
	UserChanneDef:      0,
	UserChannelJuLiang: 1,
	UserChannelHuawei:  2,
}

var ChannelMap = map[int]string{
	UserChannelJuLiang: "【CS_巨量】",
	UserChannelHuawei:  "【CS_huawei】",
}

// SelfUtmSourceMap IP识别对应的渠道
var SelfUtmSourceMap = map[string]int32{
	"【CS_巨量】":     1,
	"【CS_huawei】": 2,
}

var ChannelClientMap = map[string]int{}

type ScreenType int

var ScreenTypeEnum = struct {
	Unknown ScreenType
	Phone   ScreenType
	Pad     ScreenType
	TV      ScreenType
}{
	Unknown: 0,
	Phone:   1,
	Pad:     2,
	TV:      3,
}

const (
	SceneCqOb    = 1
	SceneAllOb   = 2
	SceneCqPage  = 3
	SceneAllPage = 4

	RedisCqObKey    = "pai_cq_compliance_ob_"
	RedisAllObKey   = "pai_all_compliance_ob_"
	RedisCqPageKey  = "pai_cq_compliance_page_"
	RedisAllPageKey = "pai_all_compliance_page_"

	ExpireTime = 172800 // 2天
)

var CountComplianceMap = map[int64]string{
	SceneCqOb:    RedisCqObKey,
	SceneAllOb:   RedisAllObKey,
	SceneCqPage:  RedisCqPageKey,
	SceneAllPage: RedisAllPageKey,
}

const AnonymousIDPrefix = "children"

const (
	OnlineConfObJump       = "ob_jump"
	OnlineConfIsCanFreeBuy = "is_can_free_play"
	OnlineConfLoginBuy     = "login_buy"
)

const (
	FromSourceApp    = 1
	FromSourceTiktok = 2
	FromSourceWechat = 3
)

// 后台测试用到的自然量标签
const UserChannelNaturalTest = "【CS_小树苗运动】"

// ChannelToAppStoreUtmSourceMap 渠道号到应用商店付费渠道utmSource的映射
var ChannelToAppStoreUtmSourceMap = map[string]string{
	ChannelHuawei: "【CS_huawei】",
}

// NameToChannelMap 好评资源池请与后台配置保持一致
var NameToChannelMap = map[string]string{
	ChannelApple:  "IOS",
	ChannelHuawei: "华为",
	ChannelOPPO:   "oppo",
	ChannelVIVO:   "vivo",
	ChannelXiaomi: "xiaomi",
}
