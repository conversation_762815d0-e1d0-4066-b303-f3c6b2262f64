package course

const TotalObProgramCourseNum = 28

type FitnessPurposeInt int

// FitnessPurposeEnum 健身目的
var FitnessPurposeEnum = struct {
	ReduceWeight FitnessPurposeInt
	Confident    FitnessPurposeInt
	Elegant      FitnessPurposeInt
	Health       FitnessPurposeInt
	All          FitnessPurposeInt
	Muscles      FitnessPurposeInt
}{
	ReduceWeight: 1, // 减脂塑形
	Confident:    2, // 更自信
	Elegant:      3, // 看起来更匀称
	Health:       4, // 保持健康
	All:          5, // 以上都是
	Muscles:      6, // 增肌塑形
}

var PredictModelTargetMap = map[FitnessPurposeInt]int{
	FitnessPurposeEnum.ReduceWeight: 1,
	FitnessPurposeEnum.Muscles:      2,
	FitnessPurposeEnum.Health:       3,
}

type PracticePartInt int

// PracticePartEnum 练习部位
var PracticePartEnum = struct {
	Shoulder PracticePartInt
	Arm      PracticePartInt
	Chest    PracticePartInt
	Belly    PracticePartInt
	Pygal    PracticePartInt
	Legs     PracticePartInt
	All      PracticePartInt
	Back     PracticePartInt
}{
	All:      1, // 全身
	Shoulder: 2, // 肩部
	Chest:    3, // 胸部
	Arm:      4, // 手臂
	Belly:    5, // 腹部
	Pygal:    6, // 臀部
	Legs:     7, // 腿部
	Back:     8, // 背部
}

// PracticePartDesc 练习部位描述
var PracticePartDesc = map[PracticePartInt]string{
	PracticePartEnum.Shoulder: "肩部",
	PracticePartEnum.Arm:      "手臂",
	PracticePartEnum.Chest:    "胸部",
	PracticePartEnum.Belly:    "腹部",
	PracticePartEnum.Pygal:    "臀部",
	PracticePartEnum.Legs:     "腿部",
	PracticePartEnum.All:      "全身",
	PracticePartEnum.Back:     "背部",
}

const (
	IsDefault   = iota + 1 // 默认
	IsGetConfig            // 取配置
)

var GymSportsVenTypeArr = []int{2, 3}

var ConditionType = struct {
	Sedentary int
	Night     int
	Insomnia  int
	Other     int
}{
	Sedentary: 1,
	Night:     2,
	Insomnia:  3,
	Other:     4,
}

const (
	ObProgramScheduleID = 24
)

const PlanPageDef = 1

const (
	PlanPageGenerationImage = iota + 1
	PlanPageGenerationVideo
	PlanPageGenerationComponent
)
