package course

import (
	"crypto/rand"
	"math/big"
)

const (
	LabelFitnessPurpose  = "fitness_purpose"
	LabelMinutes         = "minutes"
	LabelPracticePart    = "practice_part"
	LabelIsNeedApparatus = "is_need_apparatus"
	LabelLevel           = "level"
)

type DurationLimit struct {
	Start int
	End   int
}

const (
	PracticePartAll      = 10
	PracticePartShoulder = 11
	PracticePartArm      = 12
	PracticePartChest    = 13
	PracticePartBelly    = 14
	PracticePartPygal    = 15
	PracticePartLegs     = 16
)

var RecommendMinuteMapStartEnd = map[int]*DurationLimit{
	10: {
		Start: 10,
		End:   15,
	},
	20: {
		Start: 15,
		End:   20,
	},
	30: {
		Start: 25,
		End:   100,
	},
}

var MinuteMapStartEnd = map[int]*DurationLimit{
	10: {
		Start: 0,
		End:   15,
	},
	20: {
		Start: 15,
		End:   25,
	},
	30: {
		Start: 25,
		End:   100,
	},
}

// nolint
func DurationMapStartEnd(duration int) int {
	if duration < 1 {
		return 10
	}
	minute := duration / 60
	switch {
	case 0 < minute && minute <= 10:
		return 10
	case 10 < minute && minute < 20:
		return 20
	default:
		return 30
	}
}

var PracticePartMapSEnum = map[PracticePartInt]int{
	PracticePartEnum.All:      10,
	PracticePartEnum.Shoulder: 11,
	PracticePartEnum.Arm:      12,
	PracticePartEnum.Chest:    13,
	PracticePartEnum.Belly:    14,
	PracticePartEnum.Pygal:    15,
	PracticePartEnum.Legs:     16,
	PracticePartEnum.Back:     48,
}

func RandomInt64Value(slice []int64) int64 {
	// 如果切片为空，返回错误
	if len(slice) == 0 {
		return 0
	}

	// 生成一个随机的索引
	max := big.NewInt(int64(len(slice)))
	randIndex, err := rand.Int(rand.Reader, max)
	if err != nil {
		return 0
	}

	// 根据随机索引返回切片中的值
	return slice[randIndex.Int64()]
}

const RangeNumDef = 500
