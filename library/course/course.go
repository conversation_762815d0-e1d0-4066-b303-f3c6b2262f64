package course

const (
	// 老师标签父ID
	LabelCoachPID = 13
	// 课程功效标签父ID
	LabelCourseEffectPID = 9
	// 课程类型父ID
	LabelCourseTypePID = 15
	// 课程难度父ID
	LabelCourseLevelPID = 20
	// 课程时长父ID
	LabelCourseDurationPID = 24
	// 课程年龄父ID
	LabelCourseAgePID = 29
)

type GuessLikeSceneTypeInt int

var GuessLikeSceneTypeEnum = struct {
	Index        GuessLikeSceneTypeInt
	CourseDetail GuessLikeSceneTypeInt
}{
	Index:        1,
	CourseDetail: 2,
}

const (
	LevelPrimary = iota + 1
	LevelMiddle
	LevelSenior
)

type LevelInt int

var LevelEnum = struct {
	Primary LevelInt
	Middle  LevelInt
	Senior  LevelInt
}{
	Primary: 1,
	Middle:  2,
	Senior:  3,
}

var LevelToCalorieRatio = map[LevelInt]float64{
	LevelEnum.Primary: 0.075,
	LevelEnum.Middle:  0.09,
	LevelEnum.Senior:  0.105,
}

type ResourceTypeInt int

var ResourceTypeEnum = struct {
	Action ResourceTypeInt
	Course ResourceTypeInt
}{
	Action: 1,
	Course: 2,
}

const (
	// 猜你喜欢推荐课程数量
	GuessUserLikeRecommendNum = 5
)

const PraPartLabelsLimit = 3

var LevelDesc = map[LevelInt]string{
	LevelEnum.Primary: "初级",
	LevelEnum.Middle:  "中级",
	LevelEnum.Senior:  "高级",
}

var EnumByLevelDBID = map[int]LevelInt{
	21: LevelEnum.Primary,
	22: LevelEnum.Middle,
	23: LevelEnum.Senior,
}

type TypeInt int

// TypeEnum 课程类型枚举
var TypeEnum = struct {
	JumpRope         TypeInt // 跳绳
	Aerobics         TypeInt // 有氧操
	PhysicalTraining TypeInt
	Basketball       TypeInt
}{
	JumpRope:         1,
	Aerobics:         2,
	PhysicalTraining: 3,
	Basketball:       4,
}

// TypeEnumDesc 课程类型描述
var TypeEnumDesc = map[TypeInt]string{
	TypeEnum.JumpRope:         "跳绳",
	TypeEnum.Aerobics:         "有氧操",
	TypeEnum.PhysicalTraining: "体能训练",
	TypeEnum.Basketball:       "篮球",
}

const (
	ObProSchID = 1
)

var AllSessionFilterLabel = []int{
	LabelCourseAgePID, LabelCourseDurationPID, LabelCourseEffectPID, LabelCourseLevelPID,
}

type OptionCourse struct {
	IsOnline int
	PageSize int
	Page     int
}
