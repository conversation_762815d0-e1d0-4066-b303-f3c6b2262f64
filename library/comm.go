package library

import (
	"math"
	"reflect"
	"sort"
	"strconv"

	"gitlab.dailyyoga.com.cn/server/children/library/pay"
)

// 全局数据库布尔值公用是/否状态码
const (
	Yes = iota + 1
	No
)
const (
	SensorQueryAPI       = "http://172.16.3.136:8107/api/sql/query"
	SensorToken          = "6a01d4aab49db83e6ef1054a94354ba7224793f5ab94fe19dcb6790f545fadfb" // nolint
	FormTypeByUID        = 1
	FormTypeByDistinctID = 2
)

// OPPO 延迟队列
const (
	OPPOQueueKey          = "cs:delay:queue:oppo"
	OPPOQueueDelaySeconds = 150
	UserLabel             = "user_label_config"
)

type EmptyResponse struct {
}

// TimeZoneBeijing 北京时间时区
var TimeZoneBeijing = "Asia/Shanghai"

// AppClient app客户端公共参数
type AppClient struct {
	Version    string
	OsType     int
	Channel    int
	DeviceID   string
	ScreenType int
	IPAds      string
	IsGray     bool
	SubChannel string
	Brand      string // {"Apple":1,"HUAWEI":2,"VIVO":3,"OPPO":4,"HONOR":5,"XIAOMI":6,"其他":7}
	PlanForm   int
}

type ImageInfo struct {
	URL    string `json:"url"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
}

type VideoInfo struct {
	URL    string `json:"url"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
}

type LinkTypeInt int

var LinkTypeEnum = struct {
	Course    LinkTypeInt // 课程详情
	Payment   LinkTypeInt // 付费方案页
	InnerLink LinkTypeInt
	Praise    LinkTypeInt // 好评弹窗
}{
	Course:    1,
	Payment:   2,
	InnerLink: 3,
	Praise:    4,
}

type SkuItem struct {
	ID            int64               `json:"id"`
	Price         string              `json:"price"`
	Name          string              `json:"name"`
	PayType       []int               `json:"pay_type,omitempty"`
	PayTypeInfo   []*pay.TypeInfoItem `json:"pay_type_info,omitempty"`
	IsSubscribe   bool                `json:"is_subscribe"`
	SubscribeDesc string              `json:"subscribe_desc"`
	Img           *ImageInfo          `json:"img,omitempty"`
	SelectImg     *ImageInfo          `json:"select_img,omitempty"`
	Selected      bool                `json:"selected"`
	IOSProductID  string              `json:"ios_product_id"`
	DurationDesc  string              `json:"duration_desc"`
	ProductType   int                 `json:"product_type"`
	VipType       int                 `json:"vip_type"`
	DurationDay   int64               `json:"duration_day"`
}

type Link struct {
	LinkType    LinkTypeInt `json:"link_type"`
	LinkTitle   string      `json:"link_title,omitempty"`
	LinkContent string      `json:"link_content,omitempty"`
	SkuInfo     *SkuItem    `json:"sku_info,omitempty"`
}

type LinkContent struct {
	LinkType    int    `json:"link_type"`
	LinkContent string `json:"link_content"`
}

const TimeFormat = "2006-01-02 15:04:05"

// 微信相关参数
// nolint
const (
	WxAPIDomain         = "api.weixin.qq.com"
	WxGetAccessTokenAPI = "/cgi-bin/token"
	WxGetJsTicketAPI    = "/cgi-bin/ticket/getticket"
)

type WxAppParams struct {
	AppID  string
	Secret string
}

const WxAppSceneDefault = "Default"

// WxAppParamsScene 微信调用场景
// nolint
var WxAppParamsScene = map[string]*WxAppParams{
	WxAppSceneDefault: {
		AppID:  "wx324b86af5f9893b1",
		Secret: "e1bc09ca0a5cd728404bf431ca58c503",
	},
}

const WxTokenCacheSecond = 6000

func StringInArray(t string, arr []string) bool {
	for _, v := range arr {
		if v == t {
			return true
		}
	}
	return false
}

const DayTime = 86400

const MinuteTime = 60

func Int64InArray(t int64, arr []int64) bool {
	if len(arr) < 1 {
		return false
	}
	for _, v := range arr {
		if v == t {
			return true
		}
	}
	return false
}

func IntInArray(t int, arr []int) bool {
	for _, v := range arr {
		if v == t {
			return true
		}
	}
	return false
}

func EqualSlices(a, b []int) bool {
	sort.Ints(a)
	sort.Ints(b)
	if len(a) != len(b) {
		return false
	}
	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}
	return true
}

const (
	AppActive   = 1 // 激活应用
	AppRegister = 7 // 注册应用
	AppPay      = 4 // 付费
)

const IsTrue = "true"

func GetMaxID(arr []int64) int64 {
	max := int64(0)
	if len(arr) < 1 {
		return max
	}
	for _, e := range arr {
		if e > max {
			max = e
		}
	}
	return max
}

type ProjectType int

// ProjectTypeEnum 项目枚举
var ProjectTypeEnum = struct {
	Children ProjectType
}{
	Children: 5,
}

func FormatFloat(f float64) string {
	if f == math.Trunc(f) {
		return strconv.FormatFloat(f, 'f', 0, 64)
	}
	return strconv.FormatFloat(f, 'f', -1, 64)
}

const (
	ABTestPayPageSceneID = iota + 1
)

const Const30 = 30

const AppStoreAccountPrivateKey = `
******************************************************************************************************************************************************************************************************************************************************************` // 苹果私钥

const DayHour = 24

const Const20 = 20
const Const10000 = 10000
const Const3 = 3
const Const28 = 28
const Const7 = 7

const (
	SceneTypeIntelligence = 1
	SceneTypeTrySee       = 3
)

const Const60 = 60

const (
	PlanFormApp     = 1
	PlanFormMiNiAPP = 2 // 小程序 (抖音、微信）
)
const Target = "跑步课程"

const AppNameCN = "小树苗运动"

func InArray(val, array interface{}) bool {
	if reflect.TypeOf(array).Kind() == reflect.Slice {
		s := reflect.ValueOf(array)
		for i := 0; i < s.Len(); i++ {
			if reflect.DeepEqual(val, s.Index(i).Interface()) {
				return true
			}
		}
	}
	return false
}
