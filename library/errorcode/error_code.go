package errorcode

type ErrorCode int

// 错误码 分段 10000以上，对用户展示 0-10000 内部错误不对用户展示
const (
	Success         ErrorCode = 0
	InvalidParams   ErrorCode = 2 // 无效参数
	FailGetVercode  ErrorCode = 3 // 获取验证码失败
	FailSendVercode ErrorCode = 4 // 发送验证码失败
	SystemError     ErrorCode = 5 // 系统错误
	DBError         ErrorCode = 6 // DB错误

	VercodeError                   ErrorCode = 10001
	PwdLoginUserNotExist           ErrorCode = 10002
	PhoneUserNotExist              ErrorCode = 10004
	UserPasswdError                ErrorCode = 10003
	VercodeTimesError              ErrorCode = 10005
	HasNotLogin                    ErrorCode = 10006
	PhoneHasBindUser               ErrorCode = 10007
	HasSensitiveImage              ErrorCode = 10008
	HasSensitiveText               ErrorCode = 10009
	WechatHasBindOther             ErrorCode = 10010
	ModifyInfoLimit                ErrorCode = 10011
	SubscribeNotClose              ErrorCode = 10012
	AppleHasBindOther              ErrorCode = 10013
	ThirdAccountError              ErrorCode = 10014
	HasSubscribeSuccess            ErrorCode = 10015
	SubscribeButChargeFail         ErrorCode = 10016
	SubmitRefundFail               ErrorCode = 10020
	ChallengeNotExist              ErrorCode = 10022
	ChallengeHasJoin               ErrorCode = 10023
	ChallengeSameJoinOne           ErrorCode = 10024
	IosIsGray                      ErrorCode = 10025
	HasVideoSubscribeSuccess       ErrorCode = 10026
	SubscribeVideoButChargeFail    ErrorCode = 10027
	ShenCeUserHasChallenge         ErrorCode = 10028
	LimitUserHasLock               ErrorCode = 10029
	HasPlanCardSubscribeSuccess    ErrorCode = 10030
	SubscribePlanCardButChargeFail ErrorCode = 10031
	ActivityHasExpiredOROffline    ErrorCode = 10033
	RestrictionsSigningSameLimit   ErrorCode = 10032
	HasKegelSubscribeSuccess       ErrorCode = 10034
	SubscribeKegelButChargeFail    ErrorCode = 10035
)

// ErrorCodeMessage 错误码对应提示表
var ErrorCodeMessage = map[interface{}]string{
	// 以下为内部错误，不对用户展示
	InvalidParams:   "无效参数",
	FailGetVercode:  "获取验证码失败",
	FailSendVercode: "发送验证码失败",
	SystemError:     "系统错误",
	DBError:         "DB错误",
	// 以下报错对用户展示
	VercodeError:                   "验证码不正确",
	PwdLoginUserNotExist:           "手机号还未注册，推荐使用验证码登录",
	PhoneUserNotExist:              "该手机号还未注册",
	UserPasswdError:                "账号或密码不正确",
	VercodeTimesError:              "当天验证码获取次数已达上限",
	HasNotLogin:                    "账号未登录",
	PhoneHasBindUser:               "该手机号已被其他账号绑定",
	HasSensitiveImage:              "所选图片内容不符合相关法律规定，请重新选择",
	HasSensitiveText:               "发布内容含有敏感词，请修改",
	WechatHasBindOther:             "该微信号已被其他账号绑定",
	AppleHasBindOther:              "该苹果账号已被其他账号绑定",
	ModifyInfoLimit:                "头像、昵称、性别每月限改3次",
	SubscribeNotClose:              "系统检测到您当前有正在生效中的自动订阅产品，请先前往【支付宝 - 个人中心 - 设置 - 支付设置 - 免密支付/自动扣款】关闭",
	ThirdAccountError:              "三方账号不匹配",
	HasSubscribeSuccess:            "您已购买会员产品，如需继续购买，请前往会员中心。",
	SubscribeButChargeFail:         "您已订阅会员产品，请尽快充值，扣费成功后即可获得权益。",
	SubmitRefundFail:               "请正确填写订单号",
	ChallengeNotExist:              "挑战赛不存在",
	ChallengeHasJoin:               "当前有挑战赛正在进行中",
	ChallengeSameJoinOne:           "同一挑战赛仅能参加一次",
	IosIsGray:                      "当前版本为内测版，如需购买商品请前往AppStore下载正式版",
	HasVideoSubscribeSuccess:       "您已购买语音包会员订阅产品",
	SubscribeVideoButChargeFail:    "您已订阅语音包会员产品，请尽快充值，扣费成功后即可获得权益",
	LimitUserHasLock:               "登录设备过多已锁定",
	ShenCeUserHasChallenge:         "您有过挑战赛购买记录，请联系客服进行绑定",
	HasPlanCardSubscribeSuccess:    "您已购买精练课堂卡订阅产品",
	SubscribePlanCardButChargeFail: "您已购买精练课堂卡订阅产品，请尽快充值，扣费成功后即可获得权益",
	ActivityHasExpiredOROffline:    "当前活动已过期或下线",
	RestrictionsSigningSameLimit:   "签约次数达上限，请咨询客服购买",
	HasKegelSubscribeSuccess:       "您已购买凯格尔专区卡订阅产品",
	SubscribeKegelButChargeFail:    "您已购买凯格尔专区卡订阅产品，请尽快充值，扣费成功后即可获得权益",
}

const (
	ServiceResultSuccess = 1001
)
