package product

// 会员产品类型
const (
	ProductType1Month = iota + 1
	ProductType3Month
	ProcuctType12Month
)

var ProductTypeDesc = map[int]string{
	ProductType1Month:  "月度会员",
	ProductType3Month:  "季度会员",
	ProcuctType12Month: "年度会员",
}

const ProductTypeChallengeDesc = "挑战赛"

type DurationType int

// DurationTypeEnum 会员产品的会员时间类型
var DurationTypeEnum = struct {
	Day   DurationType
	Month DurationType
	Year  DurationType
}{
	Day:   1,
	Month: 2,
	Year:  3,
}

var DurationTypeDesEnum = map[DurationType]string{
	DurationTypeEnum.Day:   "天",
	DurationTypeEnum.Month: "月",
	DurationTypeEnum.Year:  "年",
}

var DurationTypeToDays = map[DurationType]int{
	DurationTypeEnum.Day:   1,
	DurationTypeEnum.Month: 30,
	DurationTypeEnum.Year:  365,
}

var DurationTypeToDaysSc = map[DurationType]int{
	DurationTypeEnum.Day:   1,
	DurationTypeEnum.Month: 30,
	DurationTypeEnum.Year:  360,
}

const (
	ConstOfferTypeNo            = 1
	ConstOfferTypeFirstBuy      = 2
	ConstOfferTypeTrial         = 3
	ConstOfferTypeTrialFirstBuy = 4
)

var OfferTypeDesc = map[int]string{
	ConstOfferTypeNo:            "无",
	ConstOfferTypeFirstBuy:      "首购",
	ConstOfferTypeTrial:         "试用",
	ConstOfferTypeTrialFirstBuy: "试用+首购",
}

const (
	One              = 1
	Ten              = 10
	YearToMonth      = 12
	QuarterlyToMonth = 3
	SixMonthsToMonth = 6
	Hundred          = 100
)

var ConstOfferTypeFirstBuyDesc = "开通成功后，即可解锁全部VIP课程，首次按{n4}扣费，{n2}后按{n3}每{n1}自动续费，可随时取消，取消后不再自动续费。"
var ConstOfferTypeFirstBuyPackCardDesc = "开通成功后，可解锁套餐内全部权益，首次按{n4}扣费，{n2}后按{n3}每{n1}自动续费，可随时取消，取消后不再自动续费。"
var ConstOfferTypeTrialBuyDesc = "开通成功后，即可解锁全部VIP课程，首次按{n4}扣费，{n2}后按{n3}每{n1}自动续费，可随时取消，取消后不再自动续费。"
var ConstOfferTypeTrialFirstBuyDesc = "开通成功后，即可解锁全部VIP课程，首次按{n4}扣费，{n2}后按{n6}自动扣费，{n5}后按{n3}每{n1}自动续费，可随时取消，取消后不再自动续费。"
var ConstOfferTypeTrialBuyPackCardDesc = "开通成功后，可解锁套餐内全部权益，首次按{n4}扣费，{n2}后按{n3}每{n1}自动续费，可随时取消，取消后不再自动续费。"
var ConstOfferTypeTrFiBuyPaCardDesc = "开通成功后，可解锁套餐内全部权益，首次按{n4}扣费，{n2}后按{n6}自动扣费，{n5}后按{n3}每{n1}自动续费，可随时取消，取消后不再自动续费。"

const (
	ProductVipTypeVIP = iota + 1
)

var GiftProductTypeDesc = map[int]string{
	ProductVipTypeVIP: "会员",
}

var ProductVipTypeDesc = map[int]string{
	ProductVipTypeVIP: "会员",
}

var ProductVipTypeMapDesc = map[int]string{
	ProductVipTypeVIP: "vip",
}

type FTProductTypeInt int

var FTProductTypeEnum = struct {
	MemberProduct FTProductTypeInt
}{
	MemberProduct: 1, // 会员产品
}

var ForPermanentlyYears = 99

// 1全屏 2 普通弹窗
const (
	StrongPaymentResourceScreen = iota + 1
	StrongPaymentResourcePop
)

type StepPTypeInt int

var StepPTypeEnum = struct {
	Month    StepPTypeInt
	Quarter  StepPTypeInt
	Year     StepPTypeInt
	HalfYear StepPTypeInt
}{
	Month:    1,
	Quarter:  2,
	Year:     3,
	HalfYear: 4,
}

const (
	MonthThree  = 3
	MonthSix    = 6
	MonthTwelve = 12
)

var LowLimitByStepPType = map[StepPTypeInt]float64{
	StepPTypeEnum.Month:    20,
	StepPTypeEnum.Quarter:  60,
	StepPTypeEnum.Year:     100,
	StepPTypeEnum.HalfYear: 80,
}

const PlanCardVIPDesc = "精选会员"

const IosSubscribeDesc = `• 付款：自动续费商品为“连续包%s”，您确认购买后，会从您的苹果iTunes账户扣费；
• 自动续费：您的会员到期前24小时，苹果会自动为您从iTunes账户扣费，成功后有效期自动延长一个周期，连续包%s商品延期%s； 
• 取消续订：打开苹果iOS设备“App Store”-->点击右上角苹果账户头像-->进入“账户”-->点击“订阅”-->选择“小树苗运动”，取消订阅即可；
• 查看《用户协议》和《隐私政策》，如有疑问可发邮件至******************************`

const AndroidSubscribeDesc = `确认购买自动续订项目后，我们将向您支付宝账户进行收款，用于购买相关产品，到期前24小时扣费，
在此之前，您可以在支付宝内取消自动续订，路径：支付宝-我的-设置-支付设置-免密支付/自动扣款。`

var ConstOfferTypeConfKey = map[int]string{
	ConstOfferTypeNo:            "offer_type_none",
	ConstOfferTypeFirstBuy:      "offer_type_first_offer",
	ConstOfferTypeTrial:         "offer_type_trial_offer",
	ConstOfferTypeTrialFirstBuy: "offer_type_first_trial_offer",
}
var OfferTypeDescTrackAbt = map[int]string{
	ConstOfferTypeNo:            "常规文案",
	ConstOfferTypeFirstBuy:      "首购优惠文案",
	ConstOfferTypeTrial:         "试用优惠文案",
	ConstOfferTypeTrialFirstBuy: "首购+试用优惠文案",
}
