package user

const (
	GenderUnknown = iota
	GenderWomen
	GenderMan
)

const (
	AttributeIsFirstBuy        = "is_first_buy"
	AttributeBindInfo          = "bind_info"
	AttributeLastPracticeCount = "last_practice_count"
	CanJoinStorePraise         = "can_join_android_positive"
	OBChoiceInfo               = "ob_choice_info"
	AttributePermanentlyVip    = "permanently_vip"
)

type VipSourceType int

// VipSourceTypeEnum 会员发放来源
var VipSourceTypeEnum = struct {
	System VipSourceType // 系统发放
	Admin  VipSourceType // 后台发放
}{
	System: 1,
	Admin:  2,
}

type VipChangeReason int

// VipChangeReasonEnum 会员权益变更原因
var VipChangeReasonEnum = struct {
	Purchase       VipChangeReason
	Refund         VipChangeReason
	Activity       VipChangeReason
	TransferEquity VipChangeReason
	StorePraise    VipChangeReason
	Other          VipChangeReason
}{
	Purchase:       1,
	Refund:         2,
	Activity:       3,
	TransferEquity: 4,
	StorePraise:    5,
	Other:          999,
}

var VipChangeReasonDesc = map[VipChangeReason]string{
	VipChangeReasonEnum.Purchase:       "购买",
	VipChangeReasonEnum.Refund:         "退款",
	VipChangeReasonEnum.Activity:       "活动发放",
	VipChangeReasonEnum.TransferEquity: "购买", // 权益转移 算购买
	VipChangeReasonEnum.StorePraise:    "好评活动赠送",
	VipChangeReasonEnum.Other:          "其他",
}

type VipOperateType int

var VipOperateTypeEnum = struct {
	Add VipOperateType
	Sub VipOperateType
}{
	Add: 1,
	Sub: 2,
}

var VipOperateTypeDesc = map[VipOperateType]string{
	VipOperateTypeEnum.Add: "加",
	VipOperateTypeEnum.Sub: "减",
}

const (
	ModifyInfoLimitPerMonth = 3
	UserLabel               = "user_label_config"
	UserPracticeStat        = "user_practice_stat"
	UserPartInformation     = "user_part_information"
)

// 部分用户信息
type PartInformation struct {
	Age    int `json:"age"`
	Height int `json:"height"`
	Weight int `json:"weight"`
}

type StorePraiseAuditInt int

var StorePraiseAuditEnum = struct {
	Wait   StorePraiseAuditInt
	Pass   StorePraiseAuditInt
	Refuse StorePraiseAuditInt
}{
	Wait:   1,
	Pass:   2,
	Refuse: 3,
}

const UserVipDurationLimit = 11

const (
	ProfileVipTypeNotVip = 1 + iota
	ProfileVipTypeVip
)

const ChangeUserVipKey = "Cs:delay:queue:ChangeUserVip"
const ChangeVipQueueDelaySeconds = 120 // 2分钟

const Unknown = "未知"

type BigGiftSourceType int

// BigGiftSourceTypeEnum 大礼包发放来源
var BigGiftSourceTypeEnum = struct {
	Order     BigGiftSourceType // 订单来源
	Challenge BigGiftSourceType // 挑战赛
}{
	Order:     1,
	Challenge: 2,
}

const (
	PrivacyThumbnail = iota + 1
	PrivacyNickName
	PrivacyGender
	PrivacyAge
	PrivacyHeight
	PrivacyWeight
	PrivacyMobile
)

type PrivacyPersonalInfoType int

var PrivacyPersonalInfoEnum = struct {
	Thumbnail PrivacyPersonalInfoType
	NickName  PrivacyPersonalInfoType
	Gender    PrivacyPersonalInfoType
	Age       PrivacyPersonalInfoType
	Height    PrivacyPersonalInfoType
	Weight    PrivacyPersonalInfoType
	Mobile    PrivacyPersonalInfoType
}{
	Thumbnail: PrivacyThumbnail,
	NickName:  PrivacyNickName,
	Gender:    PrivacyGender,
	Age:       PrivacyAge,
	Height:    PrivacyHeight,
	Weight:    PrivacyWeight,
	Mobile:    PrivacyMobile,
}

var PrivacyPersonalInfoMap = map[PrivacyPersonalInfoType]string{
	PrivacyThumbnail: "头像",
	PrivacyNickName:  "昵称",
	PrivacyGender:    "性别",
	PrivacyAge:       "年龄",
	PrivacyHeight:    "身高",
	PrivacyWeight:    "体重",
	PrivacyMobile:    "手机号",
}

var PrivacyPersonalInfoSort = []PrivacyPersonalInfoType{
	PrivacyPersonalInfoEnum.Thumbnail,
	PrivacyPersonalInfoEnum.NickName,
	PrivacyPersonalInfoEnum.Gender,
	PrivacyPersonalInfoEnum.Age,
	PrivacyPersonalInfoEnum.Height,
	PrivacyPersonalInfoEnum.Weight,
	PrivacyPersonalInfoEnum.Mobile,
}

const Const100 = 100.0

const Const4 = 4

const MobileLen = 11

const RegistrationTime = 1727409600

const (
	SendTypeOrder = 1
	SendTypeAgent = 2
)
