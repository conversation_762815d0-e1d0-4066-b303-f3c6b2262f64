package user

// 登录类型
const (
	LoginTypeVistor        = iota // 游客
	LoginTypePassword             // 账号密码登录
	LoginTypeMobileVercode        // 手机号验证码登录
	LoginTypeWechat               // 微信登录
	LoginTypeOneKey               // 极光一键登录
	LoginTypeApple                // 苹果登陆
	LoginTypeShenCe               // 神策distinctID登陆
	LoginTypeTikTok               // 抖音
)

var LoginTypeEnDesc = map[int]string{
	LoginTypePassword:      "telephone",
	LoginTypeOneKey:        "telephone_onekey",
	LoginTypeMobileVercode: "telephone_code",
	LoginTypeWechat:        "weixin",
	LoginTypeApple:         "ios",
	LoginTypeShenCe:        "sensorsdata",
	LoginTypeTikTok:        "douyin",
}

func LoginTypeToEnDesc(loginType int) string {
	ed, ok := LoginTypeEnDesc[loginType]
	if !ok {
		return ""
	}
	return ed
}

// 验证码获取类型
const (
	// 手机号验证码登录
	VercodeTypeLogin = iota + 1
	// 找回密码
	VercodeTypeFindPassword
	// 绑定手机号
	VercodeTypeBindMobile
	// 注销
	VercodeTypeLogoff
	// 设置密码（已登录）
	VercodeTypeResetPassword
	// 设备解锁
	VercodeDeviceUnlock
)

// 验证码获取每个场景每天获取次数
const VercodeDayLimit = 5

const (
	JiGuangSuccessCode    = 8000
	JiGuangLoginVerifyURL = "https://api.verification.jpush.cn/v1/web/loginTokenVerify"
	WechatVerifyURL       = "https://api.weixin.qq.com/sns/userinfo"
	DouYinVerifyTokenURL  = "https://developer.toutiao.com/api/apps/v2/jscode2session" //nolint
	WechatMiNiVerTokenURL = "https://api.weixin.qq.com/sns/jscode2session"             //nolint
)

const OcpdOaidKey = "cs:oaid:"

const UIDBindSidLen = 20

const UIDBindSidLimitTime = 1697731200

// 有3个设备保持登录状态
const UserDeviceLimitLen = 3
const UserOlineLimitLen = 2

const (
	UserDeviceValidMobile = 1
	UserDeviceValidbill   = 2
)
