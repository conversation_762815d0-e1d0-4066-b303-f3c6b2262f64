package index

import "gitlab.dailyyoga.com.cn/server/children/library/course"

type ContainerTypeInt int

var ContainerTypeEnum = struct {
	HorizontalCourse     ContainerTypeInt
	VerticalCourse       ContainerTypeInt
	BillboardCourse      ContainerTypeInt
	CustomCard           ContainerTypeInt
	GuessLike            ContainerTypeInt
	RecentPractice       ContainerTypeInt
	CourseCollect        ContainerTypeInt
	VideoStreaming       ContainerTypeInt
	CourseClassification ContainerTypeInt
	SessionFilter        ContainerTypeInt
}{
	HorizontalCourse:     1,
	VerticalCourse:       2,
	BillboardCourse:      3,
	CustomCard:           4,
	GuessLike:            5,
	RecentPractice:       6,
	CourseCollect:        7,
	VideoStreaming:       8,
	CourseClassification: 9,
	SessionFilter:        10,
}

var ContainerTypeDesc = map[ContainerTypeInt]string{
	ContainerTypeEnum.HorizontalCourse:     "横滑课程组",
	ContainerTypeEnum.VerticalCourse:       "纵向课程组",
	ContainerTypeEnum.BillboardCourse:      "榜单课程组",
	ContainerTypeEnum.CustomCard:           "自定义卡片",
	ContainerTypeEnum.GuessLike:            "猜你喜欢",
	ContainerTypeEnum.RecentPractice:       "最近练习",
	ContainerTypeEnum.CourseCollect:        "我的课程收藏",
	ContainerTypeEnum.VideoStreaming:       "视频流",
	ContainerTypeEnum.CourseClassification: "课程分类",
	ContainerTypeEnum.SessionFilter:        "二级分类筛选",
}

const DefaultIndexGroup = 1

const (
	ResourceSession = 1
	ResourcePlan    = 2
)

const (
	ResourceTypeDef = iota
	ResourceTypeHome
	ResourceTypePractice
)

const DefaultCourseCollect = 39
const DefaultGuessLike = 53
const DefaultRecentPractice = 65

var ResourceValDefault = map[ContainerTypeInt]int64{
	ContainerTypeEnum.GuessLike:      DefaultGuessLike,
	ContainerTypeEnum.CourseCollect:  DefaultCourseCollect,
	ContainerTypeEnum.RecentPractice: DefaultRecentPractice,
}

// JingangDistrict 首页容器一级分类（金刚区）
var JingangDistrict = []int{
	course.LabelCourseAgePID, course.LabelCourseDurationPID, course.LabelCourseEffectPID, course.LabelCourseLevelPID,
}

var JingangDistrictNameMap = map[int]string{
	course.LabelCourseEffectPID:   "目标",
	course.LabelCourseLevelPID:    "难度",
	course.LabelCourseDurationPID: "时长",
	course.LabelCourseAgePID:      "年龄",
}
var JingangDistrictIconMap = map[int]string{
	course.LabelCourseEffectPID:   "https://qnchildren.childrenworkout.com.cn/image/0e05fa28d8d2c511eaef6d6cccb6d7bcxayl.png",
	course.LabelCourseLevelPID:    "https://qnchildren.childrenworkout.com.cn/image/48260e551a19046111c5584016052864xayl.png",
	course.LabelCourseDurationPID: "https://qnchildren.childrenworkout.com.cn/image/23f7a40167fc7f0ff55457a010ed97fexayl.png",
	course.LabelCourseAgePID:      "https://qnchildren.childrenworkout.com.cn/image/257e906b6b8c5ae9bd018177a0f4db86xayl.png",
}
