package kafka

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/server/children/config"
)

// GetTopicName 获取主题名
func GetTopicName(name string) string {
	currentEnv := config.Get().Service.Env
	prefix := "topic_qa_"
	if currentEnv == microservice.Mirror {
		prefix = "topic_mirror_"
	}
	if currentEnv == microservice.Product {
		prefix = "topic_"
	}
	return prefix + name
}

// AsyncPayload
type AsyncPayload struct {
	Type   string      `json:"type"`
	Params interface{} `json:"params"`
}

// TopicEnum topic常量
var TopicEnum = struct {
	Practice string
}{
	Practice: "cs_practice",
}

// AsyncPayloadType async类型
var AsyncPayloadType = struct {
	Practice     string
	PracticeFeel string
}{
	Practice:     "cs_practice",      // 上报练习记录
	PracticeFeel: "cs_practice_feel", // 练习感受
}
