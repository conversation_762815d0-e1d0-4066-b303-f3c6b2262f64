package rop

var (
	DefaultParamsKey          = "goal_id"
	ChannelPaymentPage        = 1 // ob付费页面
	ChannelPlanGenerationPage = 2 // 计划生成页面
	TallerOb                  = 3 // 长高ob

	PaymentPageID        = "payment_page_id"
	PlanGenerationPageID = "plan_generation_page_id"
	TallerObSwitch       = "taller_ob_switch"
)

const (
	DeviceTypeAll = iota + 1
	DeviceTypeAndroid
	DeviceTypeIos
	DeviceTypeAndroidNOHUAWEI
	DeviceTypeAndroidHUAWEI
)

const (
	DefaultOBResultUIType = 2 // abt不再分流量，默认给新 1旧 2新
	DefaultPracticeType   = 1
)

const (
	NotVipPracticeYES = iota + 1
	NotVipPracticeNO
)

const (
	SubscribeComplianceUIOld = iota + 1
	SubscribeComplianceUINew
)

// 一节课/两节课
const (
	OneCourse = iota + 1
	TwoCourse
)

var SubscribeMonthPopupResource = map[string]interface{}{
	"bg_img":     "https://fitnessimg.dailyworkout.cn/image/3ff17dc8a180f42ee4654b2d8cda7404.png",
	"button_img": "https://fitnessimg.dailyworkout.cn/image/ea7e77ae565fade3dd24c1e5c77a4e70.png",
	"text":       "放弃优惠",
}
