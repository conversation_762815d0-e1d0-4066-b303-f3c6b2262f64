package pay

const (
	PayTypeWechat = iota + 1
	PayTypeAlipay
	PayTypeApple
	PayTypeTikTok
)

const (
	LimitMaxChargeOneDay = 3
)

var PayTypeDesc = map[int]string{
	PayTypeWechat: "微信",
	PayTypeAlipay: "支付宝",
	PayTypeApple:  "iOS应用内支付",
	PayTypeTikTok: "抖音支付",
}

type TypeInfoItem struct {
	Type int    `json:"type"`
	Name string `json:"name"`
	Icon string `json:"icon"`
}

var TypeInfoMap = map[int]*TypeInfoItem{
	PayTypeAlipay: {
		Type: PayTypeAlipay,
		Name: "支付宝支付",
		Icon: "https://qnchildren.childrenworkout.com.cn/image/c25440529fbda033c8490a8317feb495xayl.png",
	},
	PayTypeWechat: {
		Type: PayTypeWechat,
		Name: "微信支付",
		Icon: "https://qnchildren.childrenworkout.com.cn/image/18ef2f7e41e78faf8b9fc1e698b7c59axayl.png",
	},
	PayTypeApple: {
		Type: PayTypeApple,
		Name: "iOS应用内支付",
		Icon: "https://qnchildren.childrenworkout.com.cn/image/96d8bc65adbbcf8c2b31e21ac2061ac6xayl.png",
	},
}

// PayTypeToSensor 因为神策和每日瑜伽共用，支付方式需要转换 避免和每日瑜伽冲突
var PayTypeToSensor = map[int]int{
	PayTypeWechat: 200,
	PayTypeAlipay: 201,
	PayTypeApple:  202,
	PayTypeTikTok: 203,
}

type OrderStatusInt int

var OrderStatus = struct {
	UnPay OrderStatusInt
	Paid  OrderStatusInt
}{
	UnPay: 0,
	Paid:  1,
}

type ContractChangeType int

// ContractChangeTypeEnum 签约状态 0等待签约 1 签约 2 解约
var ContractChangeTypeEnum = struct {
	ContractChangeTypeWait   ContractChangeType
	ContractChangeTypeAdd    ContractChangeType
	ContractChangeTypeDelete ContractChangeType
}{
	ContractChangeTypeWait:   0,
	ContractChangeTypeAdd:    1,
	ContractChangeTypeDelete: 2,
}

// AliContractToChangeType 支付宝支付签约状态和change_type互转
var AliContractToChangeType = map[string]ContractChangeType{
	"NORMAL": ContractChangeTypeEnum.ContractChangeTypeAdd,
	"UNSIGN": ContractChangeTypeEnum.ContractChangeTypeDelete,
}

var SubscribeStatusDesc = map[ContractChangeType]string{
	ContractChangeTypeEnum.ContractChangeTypeAdd:    "生效中",
	ContractChangeTypeEnum.ContractChangeTypeDelete: "已取消",
}

type SubscribeModeInt int

var SubscribeModeEnum = struct {
	PayAndSubscribe SubscribeModeInt // 支付中签约
	OnlySubscribe   SubscribeModeInt // 先签约后付款
}{
	PayAndSubscribe: 1,
	OnlySubscribe:   2,
}

type OrderSubscribeStatus int

var OrderSubscribeStatusEnum = struct {
	Wait    OrderSubscribeStatus
	Success OrderSubscribeStatus
	Not     OrderSubscribeStatus
}{
	Wait:    1,
	Success: 2,
	Not:     3,
}

type UnsubscribeTypeInt int

var UnsubscribeTypeEnum = struct {
	User  UnsubscribeTypeInt
	Admin UnsubscribeTypeInt
	Auto  UnsubscribeTypeInt
}{
	User:  1,
	Admin: 2,
	Auto:  3,
}

var UnsubscribeTypeDesc = map[UnsubscribeTypeInt]string{
	UnsubscribeTypeEnum.User:  "用户",
	UnsubscribeTypeEnum.Admin: "后台",
	UnsubscribeTypeEnum.Auto:  "自动解约",
}

type RefundStatusInt int

var RefundStatusEnum = struct {
	All      RefundStatusInt
	Paid     RefundStatusInt
	Wait     RefundStatusInt
	Refuse   RefundStatusInt
	Ing      RefundStatusInt
	Success  RefundStatusInt
	Fail     RefundStatusInt
	Retrieve RefundStatusInt
}{
	All:      -1,
	Paid:     0,
	Wait:     1,
	Refuse:   2,
	Ing:      3,
	Success:  4,
	Fail:     5,
	Retrieve: 8,
}

var RefundStatusDesc = map[RefundStatusInt]string{
	RefundStatusEnum.Paid:     "支付成功",
	RefundStatusEnum.Wait:     "待审核",
	RefundStatusEnum.Refuse:   "已拒绝",
	RefundStatusEnum.Ing:      "退费中",
	RefundStatusEnum.Success:  "退费成功",
	RefundStatusEnum.Fail:     "退费失败",
	RefundStatusEnum.Retrieve: "挽回成功",
}

type ReasonTypeInt int

var ReasonTypeEnum = struct {
	OB              ReasonTypeInt
	ActivityNo      ReasonTypeInt
	RepeatPay       ReasonTypeInt
	SystemSubscribe ReasonTypeInt
	Personal        ReasonTypeInt
	ChangeEquity    ReasonTypeInt
	Test            ReasonTypeInt
	Others          ReasonTypeInt
	Challenge       ReasonTypeInt
	ChildrenOp      ReasonTypeInt
	TooManyPay      ReasonTypeInt
	PersonReason    ReasonTypeInt
	PriceMistake    ReasonTypeInt
	AutoSubscribe   ReasonTypeInt
	ZeroChallenge   ReasonTypeInt
	UnScreenShadow  ReasonTypeInt
	TestChallenge   ReasonTypeInt
	ComplaintOrder  ReasonTypeInt
	NoIdeaToPay     ReasonTypeInt
	InnerTest       ReasonTypeInt
}{
	OB:              1,
	ActivityNo:      2,
	RepeatPay:       3,
	SystemSubscribe: 4,
	Personal:        5,
	ChangeEquity:    6,
	Test:            7,
	Others:          8,
	Challenge:       9,
	ChildrenOp:      10,
	TooManyPay:      11,
	PersonReason:    12,
	PriceMistake:    13,
	AutoSubscribe:   14,
	ZeroChallenge:   15,
	UnScreenShadow:  16,
	TestChallenge:   17,
	ComplaintOrder:  18,
	NoIdeaToPay:     19,
	InnerTest:       20,
}

var ReasonTypeDesc = map[ReasonTypeInt]string{
	ReasonTypeEnum.OB:              "OB流程",
	ReasonTypeEnum.ActivityNo:      "活动不认可",
	ReasonTypeEnum.RepeatPay:       "重复支付",
	ReasonTypeEnum.SystemSubscribe: "系统续订",
	ReasonTypeEnum.Personal:        "个人原因",
	ReasonTypeEnum.ChangeEquity:    "更换权益",
	ReasonTypeEnum.Test:            "测试",
	ReasonTypeEnum.Others:          "其他",
	ReasonTypeEnum.Challenge:       "挑战赛退款",
	ReasonTypeEnum.ChildrenOp:      "小孩支付",
	ReasonTypeEnum.TooManyPay:      "多笔支付",
	ReasonTypeEnum.PersonReason:    "个人原因",
	ReasonTypeEnum.NoIdeaToPay:     "无感支付",
	ReasonTypeEnum.PriceMistake:    "价格看错",
	ReasonTypeEnum.AutoSubscribe:   "自动续费",
	ReasonTypeEnum.ZeroChallenge:   "0元挑战赛",
	ReasonTypeEnum.UnScreenShadow:  "无法投屏",
	ReasonTypeEnum.TestChallenge:   "内测挑战赛成功",
	ReasonTypeEnum.ComplaintOrder:  "投诉外溢",
	ReasonTypeEnum.InnerTest:       "内部测试",
}

var ReasonSpecialTypeEnum = struct {
	Alipay ReasonTypeInt
	Ios    ReasonTypeInt
}{
	Alipay: 1,
	Ios:    2,
}

var ReasonSpecialTypeDesc = map[ReasonTypeInt]string{
	ReasonSpecialTypeEnum.Alipay: "支付宝超时",
	ReasonSpecialTypeEnum.Ios:    "IOS退费",
}

type RefundTypeInt int

// RefundTypeEnum 1 是全部退款 2 部分退款
var RefundTypeEnum = struct {
	Whole    RefundTypeInt
	Part     RefundTypeInt
	Retrieve RefundTypeInt
}{
	Whole:    1,
	Part:     2,
	Retrieve: 3,
}

type CommunicateStatusInt int

var CommunicateStatusEnum = struct {
	Wait         CommunicateStatusInt
	SubmitRefund CommunicateStatusInt
	Ignore       CommunicateStatusInt
}{
	Wait:         1,
	SubmitRefund: 2,
	Ignore:       3,
}

var CommunicateStatusDesc = map[CommunicateStatusInt]string{
	CommunicateStatusEnum.Wait:         "未沟通",
	CommunicateStatusEnum.SubmitRefund: "已提交退款",
	CommunicateStatusEnum.Ignore:       "已忽略",
}

type DebitTypeInt int

var DebitTypeEnum = struct {
	System DebitTypeInt
	Manual DebitTypeInt
}{
	System: 1,
	Manual: 2,
}

var DebitTypeDesc = map[DebitTypeInt]string{
	DebitTypeEnum.System: "微信/支付宝订阅自动扣款",
	DebitTypeEnum.Manual: "微信/支付宝手动支付",
}

const (
	AutoRefundName = "autoRefund"
)

const AssistLimit = "assist_limit" // 限制配置
const FitnessProjectType = 3

const (
	RefundTypeGeneral = iota
	RefundTypeSpecial
)

const (
	PaymentOrderTypeVip = iota
	PaymentOrderTypeStrong
	PaymentOrderTypeVoice
	PaymentOrderTypePlan
	PaymentOrderTypeVoiceStrong
	PaymentOrderTypePlanCard
	PaymentOrderTypePlanCardStrong
	PaymentOrderTypePackCard
	PaymentOrderTypePackCardStrong
	PaymentOrderTypeKegel
	PaymentOrderTypeKegelStrong
)

// 场景类型 0-默认场景 1-强付费 2-精练课堂非会员
const (
	ScenesTypeTypeStrong = iota + 1
	ScenesTypeTypeNonMemberPlan
)

const (
	ProductTypeVip = "vip"
)

// AliCustomCopyNotSupported water 新账号不支持自定文案
var AliCustomCopyNotSupported = []string{"****************", "****************", "****************"}

// 支付宝扣款
const (
	AlipayChargeControlBegin = 9
	AlipayChargeControlEnd   = 15
)

const (
	AbnormalTypeLogoff = iota + 1
	AbnormalTypeUnsign
	AbnormalTypeMaxChargeDay
)

const (
	AliErrCodeUserAccount        = iota + 1 // 用户账户问题
	AliErrCodeAliSystemErr                  // 支付宝系统服务异常
	AliErrCodeProductAmountLimit            // 产品额度超限
	AliErrCodeOrderInProcess                // 订单处理中
)

var AliErrCodeMsg = map[int]string{
	AliErrCodeUserAccount:        "用户账户异常",
	AliErrCodeAliSystemErr:       "支付宝系统服务异常",
	AliErrCodeProductAmountLimit: "产品额度超限",
	AliErrCodeOrderInProcess:     "订单处理中",
}

var AliErrCategoryMap = map[string]int{
	"ACQ.BUYER_BALANCE_NOT_ENOUGH":             AliErrCodeUserAccount,
	"ACQ.NO_PAYMENT_INSTRUMENTS_AVAILABLE":     AliErrCodeUserAccount,
	"ACQ.PAYMENT_FAIL":                         AliErrCodeUserAccount,
	"ACQ.BUYER_ENABLE_STATUS_FORBID":           AliErrCodeUserAccount,
	"ACQ.BUYER_PAYMENT_AMOUNT_DAY_LIMIT_ERROR": AliErrCodeUserAccount,
	"aop.ACQ.SYSTEM_ERROR":                     AliErrCodeAliSystemErr,
	"ACQ.PRODUCT_AMOUNT_LIMIT_ERROR":           AliErrCodeProductAmountLimit,
	"CustomErr_OrderSuccessPayInprocess":       AliErrCodeOrderInProcess,
}

var AppIDToAccountMap = map[string]string{
	"****************": "<EMAIL>",
	"****************": "<EMAIL>",
	"****************": "<EMAIL>",
	"****************": "<EMAIL>",
	"****************": "<EMAIL>",
	"****************": "<EMAIL>",

	"****************": "<EMAIL>",
	"****************": "<EMAIL>",
	"****************": "<EMAIL>",
	"****************": "<EMAIL>",
	"****************": "<EMAIL>",
	"****************": "<EMAIL>",

	"****************": "<EMAIL>",
	"****************": "<EMAIL>",
	"****************": "<EMAIL>",

	"****************": "<EMAIL>",
	"****************": "<EMAIL>",
	"****************": "<EMAIL>",
	"****************": "<EMAIL>",
	"****************": "<EMAIL>",
	"****************": "<EMAIL>",
	"****************": "<EMAIL>",
	"****************": "<EMAIL>",

	"****************": "<EMAIL>",
	"****************": "<EMAIL>",
	"****************": "<EMAIL>",

	"****************": "<EMAIL>",
	"****************": "<EMAIL>",
	"****************": "<EMAIL>",

	"****************": "<EMAIL>",
	"2021004108616001": "<EMAIL>",
	"2021004108617001": "<EMAIL>",

	"2021004107694001": "<EMAIL>",
	"2021004106663048": "<EMAIL>",
	"2021004106663049": "<EMAIL>",

	"2021004107621014": "<EMAIL>",
	"2021004107693006": "<EMAIL>",
	"****************": "<EMAIL>",
	"2021004111657164": "<EMAIL>",
	"2021004112636133": "<EMAIL>",
	"2021004112623135": "<EMAIL>",

	"2021004108601295": "<EMAIL>",
	"2021004109613120": "<EMAIL>",
	"2021004108630287": "<EMAIL>",

	"2021004107650485": "<EMAIL>",
	"2021004106663522": "<EMAIL>",
	"2021004108622491": "<EMAIL>",

	"2021004108694376": "<EMAIL>",
	"2021004107609403": "<EMAIL>",
	"2021004109611255": "<EMAIL>",

	"2021004106691387": "<EMAIL>",
	"2021004108682335": "<EMAIL>",
	"2021004107646360": "<EMAIL>",

	"2021004108680164": "<EMAIL>",
	"2021004109611196": "<EMAIL>",
	"2021004107604378": "<EMAIL>",

	"2021004108692312": "<EMAIL>",
	"2021004107613354": "<EMAIL>",
	"2021004109611200": "<EMAIL>",

	"2021004107646369": "<EMAIL>",
	"2021004108659363": "<EMAIL>",
	"2021004107695324": "<EMAIL>",

	"2021004107611478": "<EMAIL>",
	"2021004106666505": "<EMAIL>",
	"2021004107691173": "<EMAIL>",

	"2021004107626477": "<EMAIL>",
	"2021004108665450": "<EMAIL>",
	"2021004109611335": "<EMAIL>",

	"2021004108694452": "<EMAIL>",
	"2021004107611496": "<EMAIL>",
	"2021004108667524": "<EMAIL>",
}

const (
	CompanyXiAnYuLe    = iota + 1 // 西安瑜乐
	CompanyLongNanMing            // 龙南铭
	CompanyYinNiu                 // 银纽
)

var CompanyNameMap = map[int]string{
	CompanyXiAnYuLe:    "西安瑜乐",
	CompanyLongNanMing: "龙南铭",
	CompanyYinNiu:      "银纽",
}

type MerchantInfo struct {
	Email  string
	Number string
}

var CompanyToMerchant = map[int][]MerchantInfo{
	CompanyXiAnYuLe: {
		{Number: "2088111106785620", Email: "<EMAIL>"},
		{Number: "2088441690018650", Email: "<EMAIL>"},
		{Number: "2088231954222000", Email: "<EMAIL>"},
		{Number: "2088241010102850", Email: "<EMAIL>"},
	},
	CompanyLongNanMing: {
		{Number: "2088721071298160", Email: "<EMAIL>"},
		{Number: "2088731282566410", Email: "<EMAIL>"},
		{Number: "2088441264442300", Email: "<EMAIL>"},
		{Number: "2088341901525760", Email: "<EMAIL>"},
		{Number: "2088621526003960", Email: "<EMAIL>"},
		{Number: "2088541770620250", Email: "<EMAIL>"},
	},
	CompanyYinNiu: {
		{Number: "2088621480185990", Email: "<EMAIL>"},
		{Number: "2088641653626270", Email: "<EMAIL>"},
		{Number: "2088641655345490", Email: "<EMAIL>"},
		{Number: "2088641653309530", Email: "<EMAIL>"},
		{Number: "2088641655067090", Email: "<EMAIL>"},
		{Number: "2088641655219770", Email: "<EMAIL>"},
	},
}

const (
	VipCenterComplianceUserGroup = 999
	VipCenterUserGroupDef        = 0
)

const WeekToDay = 7

const MaxRetryTimes = 3

const (
	IosRefundStatusDef = iota
	IosRefundStatusAlreadyApplied
	IosRefundStatusSuccess
	IosRefundStatusReject
)

// TikTokMchIDToAppID pay-bridge 请同步补充 退款要用
var TikTokMchIDToAppID = map[string]string{
	"73723807870916549470": "tt723f0482ed09e12101",
}

// TikTokMchIDToDeduceTemplate 商户号对应的扣款模版ID
var TikTokMchIDToDeduceTemplate = map[string]string{
	"73723807870916549470": "7384703308490869055",
}

var TiktokContractToChangeType = map[string]ContractChangeType{
	"SUCCESS":  ContractChangeTypeEnum.ContractChangeTypeAdd,
	"TIME_OUT": ContractChangeTypeEnum.ContractChangeTypeWait,
	"CANCEL":   ContractChangeTypeEnum.ContractChangeTypeDelete,
	"DONE":     ContractChangeTypeEnum.ContractChangeTypeDelete,
}

// pay_scenes
// 全屏弹窗    1
// 个人tab- banner 2
// 自定义卡片 3
// 增值tab吸底小条 4

const (
	PayScenesFullScreenPopup = iota + 1
	PayScenesPersonalTabBanner
	PayScenesCustomCard
	PayScenesVipTabBottomBar
)

var PayScenesDesc = map[int]string{
	PayScenesFullScreenPopup:   "全屏弹窗",
	PayScenesPersonalTabBanner: "个人tab- banner",
	PayScenesCustomCard:        "自定义卡片",
	PayScenesVipTabBottomBar:   "增值tab吸底小条",
}
