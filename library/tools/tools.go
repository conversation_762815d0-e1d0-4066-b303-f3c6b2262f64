package tools

import (
	"io"
	"os"
)

func InIntSlice(haystack []int64, needle int64) bool {
	for _, e := range haystack {
		if e == needle {
			return true
		}
	}

	return false
}

func GetMaxID(arr []int64) int64 {
	max := int64(0)
	if len(arr) < 1 {
		return max
	}
	for _, e := range arr {
		if e > max {
			max = e
		}
	}
	return max
}

func GetMinPrice(arr []float64) float64 {
	min := arr[0]
	if len(arr) < 1 {
		return min
	}
	for _, e := range arr {
		if e < min {
			min = e
		}
	}
	return min
}

func Int64Intersection(slice1, slice2 []int64) []int64 {
	// 创建一个 map 来存储 slice1 中的元素
	set := make(map[int64]bool)
	for _, val := range slice1 {
		set[val] = true
	}
	// 创建一个用于存储交集的切片
	var intersect []int64
	// 遍历 slice2，如果元素在 set 中存在，则将其加入交集切片中
	for _, val := range slice2 {
		if set[val] {
			intersect = append(intersect, val)
		}
	}
	return intersect
}

// ReadFile defined path
func ReadFile(file string) ([]byte, error) {
	fp, err := os.Open(file)
	if err != nil {
		return nil, err
	}

	defer func() {
		_ = fp.Close()
	}()

	var data []byte
	data, err = io.ReadAll(fp)
	if err != nil {
		return nil, err
	}
	return data, err
}
