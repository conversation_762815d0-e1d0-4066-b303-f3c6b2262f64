package routers

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/server/children/controllers/user"
)

// 用户相关接口
func userRouters(s *http.Engine) {
	// 获取七牛token
	s.Handle("GET", "/children/user/upload/token", user.DfVercode.QueryToken)
	// 用户登陆
	s.Handle("POST", "/children/user/login", user.DfLogin.Login)
	// 获取手机号验证码
	s.Handle("POST", "/children/user/mobile/vercode", user.DfVercode.GetCode)
	// 检查手机号验证码是否正确
	s.Handle("GET", "/children/user/check/vercode", user.DfVercode.CheckCode)
	// 退出登录
	s.Handle("POST", "/children/user/logout", user.DfLogin.Logout)
	// 用户通用信息接口
	s.Handle("GET", "/children/user/common/info", user.DfUser.CommonInfo)
	// 更新用户信息接口
	s.Handle("POST", "/children/user/update/info", user.DfUser.UpdateInfo)
	// 找回密码
	s.Handle("POST", "/children/user/find/password", user.DfLogin.FindPassword)
	// 设置密码
	s.Handle("POST", "/children/user/reset/password", user.DfLogin.ResetPassword)
	// 注销账号
	s.Handle("POST", "/children/user/logoff", user.DfLogin.Logoff)
	// 账号绑定手机号接口
	s.Handle("POST", "/children/user/bind/mobile", user.DfUser.BindMobile)
	// 账号绑定微信接口
	s.Handle("POST", "/children/user/bind/wechat", user.DfUser.BindWechat)
	// 账号绑定IOS接口
	s.Handle("POST", "/children/user/bind/apple", user.DfUser.BindApple)
	// 账号绑定IOS接口
	s.Handle("POST", "/children/user/bind/one_key", user.DfUser.BindOneKey)
	// 练习数据上报接口
	s.Handle("POST", "/children/practice/report", user.DfUser.PracticeReport)
	// 练习感受数据上报接口
	s.Handle("POST", "/children/practice_feel/report", user.DfUser.PracticeFeelReport)
	// 设备绑定神策账号
	s.Handle("POST", "/children/sc/bind/anonymous/id", user.DfUser.BindAnonymousID)
	// 巨量实时归因
	s.Handle("POST", "/children/byte/bind/attribution", user.DfUser.BindByteAttribution)
	// 给匿名ID设置渠道，测试用
	s.Handle("GET", "/children/set/anonymous/utmsource", user.DfUser.SetUtmSource)
	// abt结果
	s.Handle("GET", "/children/user/common/abt", user.DfUser.CommonAbtResult)
	// 自主提交退款
	s.Handle("POST", "/children/user/refund/submit", user.SubmitRefund)
	// 会员记录
	s.Handle("GET", "/children/user/vip/history", user.DfUser.UserVipHistory)
	// 积分墙提交
	s.Handle("POST", "/children/user/integralwall/submit", user.IntegralWallSubmit)
	// 积分墙校验
	s.Handle("POST", "/children/user/integralwall/report", user.IntegralWallReport)
	// 自动订阅管理
	s.Handle("GET", "/children/user/subscribe/manage", user.Manage)
	// 自动订阅退订
	s.Handle("POST", "/children/user/subscribe/cancel", user.UnSubscribe)
	// 统计ob进入次数
	s.Handle("GET", "/children/ob/isEnter", user.GetOBIsEnter)
	// 统计方案页进入次数
	s.Handle("GET", "/children/page/isEnter", user.GetPageIsEnter)
	// 用户账号解锁
	s.Handle("POST", "/children/user/account_unlock", user.DfUser.AccountUnlock)
	// 配置数据
	s.Handle("GET", "/children/user/conf/data", user.DfUser.ConfData)
	// 用户个人信息收集
	s.Handle("GET", "/children/user/info/collect", user.DfUser.CollectUserInfo)
	// 用户隐私协议
	s.Handle("GET", "/children/user/privacy_agreement", user.DfUser.PrivacyAgreement)
	// 七鱼嵌入客诉后台
	s.Handle("GET", "/children/user/cs/qiyu/order", user.DfUser.GetUserData)
	// ob流程计划生成页
	s.Handle("GET", "/children/ob/plan_page", user.DfUser.PagePlanGeneration)
	// ob流程页面
	s.Handle("GET", "/children/ob/process/list", user.DfUser.GetObProcess)
}
