package routers

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/server/children/controllers/collect"
)

func collectRouters(s *http.Engine) {
	// 用户收藏列表
	s.Handle("GET", "/children/user/collect_list", collect.UserCollectNew)
	// 收藏
	s.Handle("POST", "/children/user/collect", collect.AddCollect)
	// 取消收藏
	s.Handle("POST", "/children/user/cancel_collect", collect.DelCollect)
}
