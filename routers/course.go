package routers

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/server/children/controllers/course"
	"gitlab.dailyyoga.com.cn/server/children/controllers/practice"
)

// 课程相关接口
func courseRouters(s *http.Engine) {
	// 创建ob计划接口
	s.Handle("POST", "/children/create/user/obprogram", course.CreateObProgram)
	// 重新开始OB课表
	s.Handle("GET", "/children/restart/user/obprogram", course.RestartObProgram)
	// ob计划详情接口
	s.Handle("GET", "/children/user/obprogram/detail", course.ObProgramDetail)
	// 课程详情接口
	s.Handle("GET", "/children/course/detail", course.GetCourseDetail)
	// 猜你喜欢接口
	s.Handle("GET", "/children/course/guess/like", course.GuessUserLike)
	// 最近练习接口
	s.Handle("GET", "/children/course/user/last_practice", course.LastPractice)
	// 习练历程 练习记录柱状图看板
	s.Handle("GET", "/children/practice/practice/overview", practice.DataView)
	// 习练历程 练习记录
	s.Handle("GET", "/children/practice/log/overview", practice.DataList)
	// 习练历程 个人tab练习统计
	s.Handle("GET", "/children/practice/stat", practice.DataStat)
	// 习练历程 最喜欢的课程
	s.Handle("GET", "/children/practice/session/favorite", practice.FavoriteCourse)
	// 习练历程 测试用来添加练习记录
	s.Handle("GET", "/children/practice/test", practice.TestPractice)
	// 完训页面 获取用户 练习打卡天数 周练习天数
	s.Handle("GET", "/children/practice/continuous/stat", practice.TrainingCompletionStat)
	// 获取课程标签
	s.Handle("GET", "/children/course/label/list", course.LabelList)
	// 选课标签筛选接口
	s.Handle("GET", "/children/filter/label/session", course.SelectTabFilter)
}
