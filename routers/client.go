package routers

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"

	"gitlab.dailyyoga.com.cn/server/children/controllers/apple"
	"gitlab.dailyyoga.com.cn/server/children/controllers/appstart"
	"gitlab.dailyyoga.com.cn/server/children/controllers/ocpx"
	"gitlab.dailyyoga.com.cn/server/children/controllers/user"
)

func clientRouters(s *http.Engine) {
	// 获取服务器时间
	s.Handle("GET", "/children/config/get_service_time", appstart.GetServiceTime)
	// 用户同意隐私政策之前，客户端调服务端接口上报神策
	s.Handle("POST", "/children/statistic/reptocl", user.DfUser.ReptocalSensor)
	// 审核相关配置 (1.6以后升级成为合规开关)
	s.Handle("GET", "/children/conf/audit/switch/info", user.DfUser.AuditInfo)
	// 相关配置
	s.Handle("GET", "/children/conf/info", appstart.AppConfig)
	// APP激活上报
	s.Handle("GET", "/children/app/activation", appstart.AppActivation)
	// 苹果ASA
	s.Handle("POST", "/children/iap/datareport/apple_ads_info", apple.AdsInfo)
	// 华为ocpd推送
	s.Handle("GET", "/children/huawei/put", ocpx.HuaweiOCPD.OcpdPush)
	s.Handle("POST", "/children/huawei/put", ocpx.HuaweiOCPD.OcpdPush)
	// 获取审核开关 1.6以后
	s.Handle("GET", "/children/clientconfig/getAppAudit", user.DfUser.GetAppAudit)
	// 推荐开关详情
	s.Handle("GET", "/children/user_switch/info", user.DfUser.UserSwitchInfo)
	// 推荐开关
	s.Handle("POST", "/children/user_switch/change", user.DfUser.UserSwitchChange)
	// 客服入口ABT
	s.Handle("GET", "/children/customer_service/enter", appstart.CustomerServiceEnter)
	// 应用商店好评上报
	s.Handle("POST", "/children/store/praise/add", user.StorePraiseReport)
	// 隐私协议开关
	s.Handle("GET", "/children/privacy/protocol/info", user.DfUser.GetPrivacyProtoCol)
}
