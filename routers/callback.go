package routers

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/server/children/controllers/callback"
)

// 第三方回调相关接口
func callbackRouters(s *http.Engine) {
	// 支付宝下单回调
	s.Handle("POST", "/children/callback/alipay/tradeapppay", callback.Alipay.TradePayNotify)
	// 支付宝H5下单回调
	s.Handle("POST", "/children/callback/alipay/tradewappay", callback.Alipay.TradePayNotify)
	// 支付宝支付中签约首次付款回调
	s.Handle("POST", "/children/callback/alipay/agreementapppay", callback.Alipay.TradePayNotify)
	// 支付宝签约回调 先签约后扣款
	s.Handle("POST", "/children/callback/alipay/aggrement", callback.Alipay.AgreementSign)
	// 支付宝签约回调 支付中签约
	s.Handle("POST", "/children/callback/alipay/aggrementpay", callback.Alipay.AgreementPaySign)
	// 支付宝自动续订扣款回调
	s.Handle("POST", "/children/callback/alipay/tradepay", callback.Alipay.TradePayCharge)
	// 微信下单回调
	s.Handle("POST", "/children/callback/weixin/unifiedorder", callback.Weixin.UnifiedOrderNotify)
	// 微信H5下单回调
	s.Handle("POST", "/children/callback/weixin/unifiedh5order", callback.Weixin.UnifiedOrderNotify)
	// IOS IAP product通知
	s.Handle("POST", "/children/callback/iap/notify", callback.ServerNotify)
	// IOS 沙盒IAP 通知qa
	s.Handle("POST", "/children/callback/iap/sandbox_notify", callback.ServerNotify)
	// IOS 沙盒IAP 通知mirror
	s.Handle("POST", "/children/callback/iap/sandbox_mirror", callback.ServerNotify)

	// IOS V2沙盒IAP 通知qa
	s.Handle("POST", "/children/callback/iap_v2/sandbox_qa", callback.ServerNotifyV2)
	// IOS V2沙盒IAP 通知mirror
	s.Handle("POST", "/children/callback/iap_v2/sandbox_mirror", callback.ServerNotifyV2)
	// IOS IAP V2 product通知
	s.Handle("POST", "/children/callback/iap_v2/notify", callback.ServerNotifyV2)
	// IOS 根据交易的订单号 恢复 账号权益
	s.Handle("POST", "/children/callback/iap_v2/lookuporderid", callback.LookUpIosIAPOrder)
}
