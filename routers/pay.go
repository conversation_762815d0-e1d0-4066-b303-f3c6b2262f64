package routers

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/server/children/controllers/pay"
)

// 支付相关接口
// nolint
func payRouters(s *http.Engine) {
	// 付费方案页接口
	s.Handle("GET", "/children/pay/page", pay.DfPage.PageInfo)
	// 预下单接口
	s.Handle("POST", "/children/pay/preorder", pay.DfOrder.PreOrder)
	// 订单信息接口
	s.Handle("GET", "/children/order/info", pay.DfOrder.Info)
	// IOS 完成订单上报
	s.Handle("POST", "/children/order/complete/iap", pay.OrderIAP)
	// IOS 检查订单上报是否处理完毕
	s.Handle("GET", "/children/order/check/iap_complete", pay.CheckCompleteIAP)
	// 查询签约信息是否存在
	s.Handle("GET", "/children/order/agreement/exist", pay.DfOrder.CheckAggrement)
	// 模拟ios退款
	s.Handle("POST", "/children/mock/ios/refund", pay.DfOrder.MockIosRefund)
	// 订单列表
	s.Handle("GET", "/children/order/list", pay.DfOrder.OrderList)
}
