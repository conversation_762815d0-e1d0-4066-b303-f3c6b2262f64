package main

import (
	"log"

	"gitlab.dailyyoga.com.cn/gokit/conf"
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/middlewares/auth"
	srvconf "gitlab.dailyyoga.com.cn/server/children/config"
	"gitlab.dailyyoga.com.cn/server/children/databases"
	"gitlab.dailyyoga.com.cn/server/children/grpc"
	"gitlab.dailyyoga.com.cn/server/children/library/errorcode"
	"gitlab.dailyyoga.com.cn/server/children/routers"
	"gitlab.dailyyoga.com.cn/server/children/rpc"
	"gitlab.dailyyoga.com.cn/server/children/service/cache"
	"gitlab.dailyyoga.com.cn/server/children/service/sensor"
)

func httpStarter(ms *microservice.Microservice) {
	opts := []http.Option{
		http.WithGZip(),
		http.WithCORS(http.WithCORSAllowAll()),
		http.WithRouter(routers.Routers),
		http.WithResponseCodes(errorcode.ErrorCodeMessage),
		http.WithMiddlewares(auth.MiddlewareAPI(auth.WithAPIConfig(&srvconf.Get().APIConfig))),
	}
	http.NewServer(ms, opts...)
}

func main() {
	config := conf.Read("children", "common/redis", "common/etcd", "db/children", "db/yogacs")
	opts := []microservice.Option{
		microservice.WithConfig(config),
		microservice.WithStarter(srvconf.InitConf, httpStarter, databases.Starter, cache.Starter, grpc.Starter, sensor.Starter, CronStarter, rpc.Starter),
	}
	if err := microservice.Run(opts...); err != nil {
		log.Fatal(err)
	}
}
